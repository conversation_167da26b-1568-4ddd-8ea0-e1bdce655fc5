import { Box, IconButton, Typography } from "@mui/material";
import { useState } from "react";
import { useTranslation } from "react-i18next";
import { useNavigate, useParams } from "react-router-dom";
import { ButtonOutline } from "../../../../components/button";
import { EditIcon, EyeIcon, TrashIcon } from "@/components/icons";
import { ApplicationStatusEnum, formatDate, useQuery } from "@/helpers";
import { useCustomMutation } from "@refinedev/core";
import { API_URL } from "@/api";
import ConfirmationDialog from "@/components/dialog/confirm";

import { useSelector } from "react-redux";
import { getAliranTugasAccess, getUserPermission } from "@/redux/userReducer";
import { DataTable, IColumn } from "@/components";
import { FieldValues, useForm } from "react-hook-form";

const Pegawai: React.FC = () => {
  const { t } = useTranslation();
  const [isBlackListed, setIsBlackListed] = useState(false);
  const navigate = useNavigate();
  const { id: societyId } = useParams();

  const handleDaftarPegawaiAwam = () => {
    navigate("create-awam");
  };

  const handleDaftarPegawaiHarta = () => {
    navigate("create-harta");
  };

  const sectionStyle = {
    color: "var(--primary-color)",
    marginBottom: "16px",
    borderRadius: "16px",
    fontSize: "14px",
    fontWeight: "500 !important",
  };
  const { id } = useParams();

  const { refetch: fetchSociety, isLoading: fetchSocietyIsLoading } = useQuery({
    url: `society/${societyId}`,
    onSuccess: (data) => {
      setIsBlackListed(data?.data?.data?.subStatusCode === "003" || false);
    },
  });

  const { setValue, watch } = useForm<FieldValues>({
    defaultValues: {
      page: 1,
      pageSize: 5,
    },
  });

  const page = watch("page");
  const pageSize = watch("pageSize");

  const {
    data: publicOfficersDataResponse,
    isLoading,
    refetch: refetchPulicOfficer,
  } = useQuery({
    url: "society/public_officer/getAll",
    filters: [
      {
        field: "societyId",
        value: id,
        operator: "eq",
      },
      {
        field: "pageSize",
        value: pageSize,
        operator: "eq",
      },
      {
        field: "pageNo",
        value: page,
        operator: "eq",
      },
    ],
  });

  const totalList = publicOfficersDataResponse?.data?.data?.total ?? 0;
  const rowData = publicOfficersDataResponse?.data?.data?.data ?? [];

  const { setValue: setPrO, watch: watchPrO } = useForm<FieldValues>({
    defaultValues: {
      page: 1,
      pageSize: 5,
    },
  });

  const pagePrO = watchPrO("page");
  const pageSizePrO = watchPrO("pageSize");

  const {
    data: propertyOfficersDataResponse,
    isLoading: isloadingPropertyOfficers,
    refetch: refetchPropertyOfficers,
  } = useQuery({
    url: "society/property_officer/getAll",
    filters: [
      {
        field: "societyId",
        value: id,
        operator: "eq",
      },
      {
        field: "pageSize",
        value: pageSizePrO,
        operator: "eq",
      },
      {
        field: "pageNo",
        value: pagePrO,
        operator: "eq",
      },
    ],
  });

  const totalPrOList = propertyOfficersDataResponse?.data?.data?.total ?? 0;
  const rowDataPrO = propertyOfficersDataResponse?.data?.data?.data ?? [];

  const {
    data: pendingApprovalExistsAwam,
    isLoading: isLoadingPendingApprovalExistsAwam,
    refetch: fetchPendingApprovalExistsAwam,
  } = useQuery({
    url: "society/public_officer/existsSocietyPublicOfficerApplication",
    filters: [
      {
        field: "societyId",
        value: id,
        operator: "eq",
      },
    ],
  });

  const {
    data: pendingApprovalExistsHarta,
    isLoading: isLoadingPendingApprovalExistsHarta,
    refetch: fetchPendingApprovalExistsHarta,
  } = useQuery({
    url: "society/property_officer/existsSocietyPropertyOfficerApplication",
    filters: [
      {
        field: "societyId",
        value: id,
        operator: "eq",
      },
    ],
  });

  const isPendingApprovalAwam = pendingApprovalExistsAwam?.data?.data;
  const isPendingApprovalHarta = pendingApprovalExistsHarta?.data?.data;

  const handleEditPublicOfficer = (officerId: string | number) => {
    navigate("create-awam", {
      state: {
        officerId: officerId,
      },
    });
  };

  const handleViewPublicOfficer = (officerId: string | number) => {
    navigate("create-awam", {
      state: {
        officerId: officerId,
        view: true,
      },
    });
  };

  const handleEditPropertyOfficer = (
    id: string | number,
    isView: boolean,
    propertyOfficers: any
  ) => {
    navigate(`create-harta`, {
      state: {
        propertyOfficerICs: propertyOfficers?.map(
          (item: any) => item?.identificationNo
        ),
        propertyOfficerApplicationId: id,
        view: isView,
      },
    });
  };

  const { mutate: deletePublicOfficer, isLoading: isUpdateAJK } =
    useCustomMutation();
  const [officerId, setOfficerId] = useState<string | number | null>(null);
  const [openConfirm, setOpenConfirm] = useState<boolean>(false);
  const [deletePublicOfficerFlag, setDeletePublicOfficerFlag] =
    useState<boolean>(false);

  const handleConfirmDeletePublicOfficer = (id: string | number) => {
    setDeletePublicOfficerFlag(true);
    setOfficerId(id);
    setOpenConfirm(true);
  };

  const handleConfirmDeletePropertyOfficer = (id: string | number) => {
    setDeletePublicOfficerFlag(false);
    setOfficerId(id);
    setOpenConfirm(true);
  };

  const handleDeletePublicOfficer = () => {
    deletePublicOfficer(
      {
        url: deletePublicOfficerFlag
          ? `${API_URL}/society/public_officer/${officerId}`
          : `${API_URL}/society/property_officer/${officerId}`,
        method: "delete",
        values: {},
        config: {
          headers: {
            portal: localStorage.getItem("portal"),
            authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
          },
        },
      },
      {
        onSuccess: () => {
          refetchLatestList();
          setOpenConfirm(false);
        },
      }
    );
  };

  const refetchLatestList = () => {
    refetchPropertyOfficers();
    refetchPulicOfficer();
    fetchPendingApprovalExistsAwam();
    fetchPendingApprovalExistsHarta();
  };

  const isManager = useSelector(getUserPermission);
  const isAliranTugasAccess = useSelector(getAliranTugasAccess);
  const isAccessible = !isBlackListed && (isManager || isAliranTugasAccess);

  const columns: IColumn[] = [
    {
      field: "name",
      headerName: t("name"),
      flex: 1,
      align: "center",
    },
    {
      field: "identificationNo",
      headerName: t("idNumber"),
      flex: 1,
      align: "center",
    },
    {
      field: "email",
      headerName: t("email"),
      flex: 1,
      align: "center",
    },
    {
      field: "tarikhBayar",
      headerName: t("tarikhLantik"),
      flex: 1,
      align: "center",
      renderCell: (params: any) => formatDate(params?.row?.appointmentDate),
      // params?.row?.appointmentDate
      //   ? formatDate(params?.row?.appointmentDate)
      //   : "-",
    },
    {
      field: "appealApplicationStatusCode",
      headerName: t("status"),
      flex: 1,
      align: "center",
      headerAlign: "center",
      renderCell: (params: any) => {
        const row = params?.row;
        return row?.applicationStatusCode ? (
          <Typography sx={{ fontSize: "14px" }}>
            {t(
              ApplicationStatusEnum[
                (row?.applicationStatusCode as keyof typeof ApplicationStatusEnum) ||
                  "0"
              ]
            )}
          </Typography>
        ) : (
          "-"
        );
      },
      cellClassName: "custom-cell",
    },
    {
      field: "actions",
      headerName: "",
      align: "right",
      headerAlign: "right",
      renderCell: ({ row }: any) => {
        if (
          ["5", "6", "44"].includes(row.applicationStatusCode) &&
          isAccessible
        ) {
          return (
            <Box
              sx={{
                display: "flex",
                justifyContent: "center",
                alignContent: "center",
              }}
            >
              <IconButton onClick={() => handleEditPublicOfficer(row.id)}>
                <EditIcon
                  sx={{
                    color: "var(--primary-color)",
                    width: "1rem",
                    height: "1rem",
                  }}
                />
              </IconButton>
            </Box>
          );
        } else if (["1"].includes(row.applicationStatusCode) && isAccessible) {
          return (
            <Box
              sx={{
                display: "flex",
                justifyContent: "center",
                alignContent: "center",
              }}
            >
              <IconButton
                sx={{
                  display: "flex",
                  justifyContent: "center",
                  alignContent: "center",
                }}
                onClick={() => handleEditPublicOfficer(row.id)}
              >
                <EditIcon
                  sx={{
                    color: "var(--primary-color)",
                    width: "1rem",
                    height: "1rem",
                  }}
                />
              </IconButton>
              <IconButton
                sx={{
                  display: "flex",
                  justifyContent: "center",
                  alignContent: "center",
                }}
                onClick={() => {
                  handleConfirmDeletePublicOfficer(row.id);
                }}
              >
                <TrashIcon
                  sx={{
                    color: "#FF0000",
                    width: "1rem",
                    height: "1rem",
                  }}
                />
              </IconButton>
            </Box>
          );
        } else {
          return (
            <Box
              sx={{
                display: "flex",
                justifyContent: "center",
                alignContent: "center",
              }}
            >
              <IconButton
                sx={{
                  width: "3rem",
                  height: "3rem",
                }}
                onClick={() => handleViewPublicOfficer(row.id)}
              >
                <EyeIcon
                  color="var(--gray-text)"
                  sx={{
                    display: "flex",
                    justifyContent: "center",
                    alignContent: "center",
                    width: "1rem",
                    height: "1rem",
                  }}
                />
              </IconButton>
            </Box>
          );
        }
      },
    },
  ];

  const columnsPrO: IColumn[] = [
    {
      field: "pegawaiharta1",
      headerName: "Pegawai Harta 1",
      flex: 1,
      align: "center",
      renderCell: (params: any) => {
        const propertyOfficersList = params?.row?.propertyOfficers;
        return propertyOfficersList?.length > 0
          ? propertyOfficersList[0]?.name
          : "-";
      },
      cellClassName: "custom-cell",
    },
    {
      field: "pegawaiharta2",
      headerName: "Pegawai Harta 2",
      flex: 1,
      align: "center",
      renderCell: (params: any) => {
        const propertyOfficersList = params?.row?.propertyOfficers;
        return propertyOfficersList?.length > 0
          ? propertyOfficersList[1]?.name
          : "-";
      },
      cellClassName: "custom-cell",
    },
    {
      field: "pegawaiharta3",
      headerName: "Pegawai Harta 3",
      flex: 1,
      align: "center",
      renderCell: (params: any) => {
        const propertyOfficersList = params?.row?.propertyOfficers;
        return propertyOfficersList?.length > 0
          ? propertyOfficersList[2]?.name
          : "-";
      },
      cellClassName: "custom-cell",
    },
    {
      field: "tarikhBayar",
      headerName: t("tarikhLantik"),
      flex: 1,
      align: "center",
      renderCell: (params: any) => formatDate(params?.row?.appointmentDate),
    },
    {
      field: "appealApplicationStatusCode",
      headerName: t("status"),
      flex: 1,
      align: "center",
      headerAlign: "center",
      renderCell: (params: any) => {
        const row = params?.row;
        return row?.applicationStatusCode ? (
          <Typography sx={{ fontSize: "14px" }}>
            {t(
              ApplicationStatusEnum[
                (row?.applicationStatusCode as keyof typeof ApplicationStatusEnum) ||
                  "0"
              ]
            )}
          </Typography>
        ) : (
          "-"
        );
      },
      cellClassName: "custom-cell",
    },
    {
      field: "actions",
      headerName: "",
      align: "right",
      headerAlign: "right",
      renderCell: ({ row }: any) => {
        if (
          ["5", "6", "44"].includes(row.applicationStatusCode) &&
          isAccessible
        ) {
          return (
            <>
              <IconButton
                onClick={() =>
                  handleEditPropertyOfficer(
                    row.id,
                    false,
                    row?.propertyOfficers
                  )
                }
              >
                <EditIcon
                  sx={{
                    color: "var(--primary-color)",
                    width: "1rem",
                    height: "1rem",
                  }}
                />
              </IconButton>
            </>
          );
        } else if (["1"].includes(row.applicationStatusCode) && isAccessible) {
          return (
            <>
              <Box
                sx={{
                  display: "flex",
                  justifyContent: "center",
                  alignContent: "center",
                }}
              >
                <IconButton
                  sx={{
                    display: "flex",
                    justifyContent: "center",
                    alignContent: "center",
                  }}
                  onClick={() =>
                    handleEditPropertyOfficer(
                      row.id,
                      false,
                      row?.propertyOfficers
                    )
                  }
                >
                  <EditIcon
                    sx={{
                      color: "var(--primary-color)",
                      width: "1rem",
                      height: "1rem",
                    }}
                  />
                </IconButton>
                <IconButton
                  sx={{
                    display: "flex",
                    justifyContent: "center",
                    alignContent: "center",
                  }}
                  onClick={() => {
                    handleConfirmDeletePropertyOfficer(row.id);
                  }}
                >
                  <TrashIcon
                    sx={{
                      color: "#FF0000",
                      width: "1rem",
                      height: "1rem",
                    }}
                  />
                </IconButton>
              </Box>
            </>
          );
        } else {
          return (
            <IconButton
              onClick={() =>
                handleEditPropertyOfficer(row.id, true, row?.propertyOfficers)
              }
            >
              <EyeIcon
                color="var(--gray-text)"
                style={{ width: "1rem", height: "1rem" }}
              />
            </IconButton>
          );
        }
      },
    },
  ];

  const handleChangePage = (newPage: number) => {
    setValue("page", newPage);
  };
  const handleChangePagePro = (newPage: number) => {
    setPrO("page", newPage);
  };

  return (
    <>
      <Box
        sx={{
          p: { xs: 1, sm: 2, md: 3 },
          backgroundColor: "white",
          borderRadius: "14px",
          mb: 2,
        }}
      >
        <Box
          sx={{
            border: "1px solid rgba(0, 0, 0, 0.12)",
            borderRadius: "14px",
            p: 3,
            mb: 2,
          }}
        >
          <Typography
            sx={{
              color: "#666666",
              fontSize: 14,
              fontWeight: "400 !important",
            }}
          >
            <span style={{ color: "red", fontWeight: "bold" }}>
              {t("peringatan")} :
            </span>{" "}
            <br />
            - Seksyen 9(c) Akta Pertubuhan 1966 memperuntukkan bahawa sesuatu
            pertubuhan boleh mendakwa atau didakwa atas nama mana-mana seorang
            anggotanya sepertimana yang dinyatakan kepada Pendaftar Pertubuhan
            dan didaftar olehnya sebagai pegawai awam pertubuhan bagi maksud
            itu. <br />- Perlantikan Pegawai Awam perlulah dilantik dikalangan
            ahli persatuan.
          </Typography>
        </Box>

        <Box
          sx={{
            border: "1px solid rgba(0, 0, 0, 0.12)",
            borderRadius: "14px",
            p: 3,
            mb: 2,
          }}
        >
          <Typography variant="subtitle1" sx={sectionStyle}>
            {t("publicOfficerList")}
          </Typography>
          <Box sx={{ my: 3 }} display={"flex"} justifyContent={"flex-end"}>
            {isManager || isAliranTugasAccess ? (
              <ButtonOutline
                disabled={isPendingApprovalAwam || !isAccessible}
                onClick={handleDaftarPegawaiAwam}
              >
                {t("registerPublicOfficer")}
              </ButtonOutline>
            ) : (
              <></>
            )}
          </Box>
          <DataTable
            columns={columns}
            rows={rowData}
            page={page}
            rowsPerPage={pageSize}
            totalCount={totalList}
            onPageChange={handleChangePage}
            onPageSizeChange={(newPageSize) => {
              setValue("page", 1);
              setValue("pageSize", newPageSize);
            }}
            isLoading={isLoading}
          />
        </Box>
      </Box>

      <Box
        sx={{
          p: { xs: 1, sm: 2, md: 3 },
          backgroundColor: "white",
          borderRadius: "14px",
          mb: 2,
        }}
      >
        <Box
          sx={{
            border: "1px solid rgba(0, 0, 0, 0.12)",
            borderRadius: "14px",
            p: 3,
            mb: 2,
          }}
        >
          <Typography
            sx={{
              color: "#666666",
              fontSize: 14,
              fontWeight: "400 !important",
            }}
          >
            <span style={{ color: "red", fontWeight: "bold" }}>
              {t("peringatan")} :
            </span>{" "}
            <br />- Seksyen 9(b) Akta Pertubuhan 1966 memperuntukkan bahawa
            harta tak alih (bangunan/tanah) sesuatu pertubuhan boleh, jika tidak
            didaftarkan di atas nama pemegang amanah, diuruskan oleh tiga
            pemegang jawatan pertubuhan. Perlantikan ketiga-tiga pemegang
            jawatan ini hendaklah disahkan oleh Pendaftar Pertubuhan.
          </Typography>
        </Box>

        <Box
          sx={{
            border: "1px solid rgba(0, 0, 0, 0.12)",
            borderRadius: "14px",
            p: 3,
            mb: 2,
          }}
        >
          <Typography variant="subtitle1" sx={sectionStyle}>
            {t("propertyOfficerList")}
          </Typography>
          <Box sx={{ my: 3 }} display={"flex"} justifyContent={"flex-end"}>
            {isManager || isAliranTugasAccess ? (
              <ButtonOutline
                disabled={isPendingApprovalHarta || !isAccessible}
                onClick={handleDaftarPegawaiHarta}
              >
                {t("registerPropertyOfficer")}
              </ButtonOutline>
            ) : (
              <></>
            )}
          </Box>

          <DataTable
            columns={columnsPrO}
            rows={rowDataPrO}
            page={pagePrO}
            rowsPerPage={pageSizePrO}
            totalCount={totalPrOList}
            onPageChange={handleChangePagePro}
            onPageSizeChange={(newPageSize) => {
              setPrO("page", 1);
              setPrO("pageSize", newPageSize);
            }}
            isLoading={isloadingPropertyOfficers}
          />
        </Box>
        <ConfirmationDialog
          status={1}
          open={openConfirm}
          onClose={() => setOpenConfirm(false)}
          title={t("confirmDelete")}
          message={`${t("deleteConfirmationMessage")}`}
          onConfirm={handleDeletePublicOfficer}
          onCancel={() => setOpenConfirm(false)}
        />
      </Box>
    </>
  );
};

export default Pegawai;
