import {
  Controller,
  FieldValues,
  useWatch,
  type Control,
  type UseFormGetValues,
  type UseFormSetValue,
} from "react-hook-form";
import { useEffect, useMemo } from "react";
import { Grid, TextField, Box, Divider, Typography } from "@mui/material";

interface GrantField {
  id: string | number;
  fieldName: string;
  fieldType: string;
  isRequired: boolean;
  sequenceOrder: number;
  sectionName: string;
  pageNumber: number;
  options?: string[] | FormOption[] | TableOption[];
  grantTemplateId?: string | number;
}
type FormOption = {
  key: string;
  value: string;
};
type TableOption = {
  key: string;
  value: string;
  row?: number;
  col?: number;
};
type TransaksiTableProps = {
  field: GrantField;
  control: Control<FieldValues>;
  getValues: UseFormGetValues<FieldValues>;
  setValue: UseFormSetValue<FieldValues>;
  value: (string | number)[][];
};

const TransaksiTable: React.FC<TransaksiTableProps> = ({
  field,
  control,
  getValues,
  setValue,
  value,
}) => {
  const rawOptions = Array.isArray(field?.options) ? field.options : [];
  const rowCount = Number(
    (rawOptions as any[]).find((o) => o.key === "count-row")?.value || 0
  );
  const colCount = Number(
    (rawOptions as any[]).find((o) => o.key === "count-col")?.value || 0
  );
  const getCellName = (field: GrantField, r: number, c: number) =>
    `${field.fieldName}_${field.sequenceOrder}_${field.pageNumber}[${r}][${c}]`;

  const initialTable: string[][] = useMemo(() => {
    const t: string[][] = Array.from({ length: rowCount }, () =>
      Array.from({ length: colCount }, () => "")
    );
    (rawOptions as any[]).forEach((opt) => {
      if (opt.key === "count-row" || opt.key === "count-col") return;
      t[opt.row][opt.col] = opt.value ?? "";
    });
    return t;
  }, [rowCount, colCount, rawOptions]);

  useEffect(() => {
    for (let r = 0; r < rowCount; r++) {
      for (let c = 0; c < colCount; c++) {
        const name = getCellName(field, r, c);
        const current = getValues(name);
        if (typeof current === "undefined") {
          setValue(name, initialTable[r][c] ?? "", {
            shouldDirty: false,
            shouldTouch: false,
          });
        }
      }
    }
  }, [rowCount, colCount, field.fieldName, initialTable]);
  const rootName = `${field.fieldName}_${field.sequenceOrder}_${field.pageNumber}`;

  const watchedMatrix = useWatch({ control, name: rootName }) as
    | Record<number, Record<number, any>>
    | undefined;

  useEffect(() => {
    if (!watchedMatrix) return;

    for (let r = 1; r < rowCount; r++) {
      const val3 = Number(watchedMatrix?.[r]?.[2]) || 0;
      const val4 = Number(watchedMatrix?.[r]?.[3]) || 0;
      const product = val3 * val4;

      const name = `${rootName}[${r}][4]`;
      const current = getValues(name);

      if (current !== product) {
        setValue(name, product, { shouldValidate: true, shouldDirty: true });
      }
    }
  }, [watchedMatrix, rowCount, rootName, getValues, setValue]);

  const totalJumlah = useMemo(() => {
    if (!watchedMatrix || !colCount) return 0;
    let total = 0;
    for (let r = 1; r < rowCount; r++) {
      const v = watchedMatrix?.[r]?.[colCount - 1];
      total += Number(v) || 0;
    }
    return total;
  }, [watchedMatrix, rowCount, colCount]);

  const headers = value[0];
  const rows = value.slice(1);

  useEffect(() => {
    if (value?.length) {
      value.forEach((row, r) => {
        row.forEach((cell, c) => {
          const name = getCellName(field, r, c);
          setValue(name, cell, { shouldDirty: true, shouldTouch: true });
        });
      });
    }
  }, [value]);

  return (
    <>
      <Grid container direction="column" spacing={2} mb={2}>
        <Grid item>
          <Typography className="title" mt={2} mb={2}>
            {field.fieldName}
          </Typography>
        </Grid>
        {Array.from({ length: rowCount }).map((_, rowIndex) => (
          <Grid container item spacing={2} key={`row-${rowIndex}`}>
            {Array.from({ length: colCount }).map((__, colIndex) => {
              const name = `${field.fieldName}_${field.sequenceOrder}_${field.pageNumber}[${rowIndex}][${colIndex}]`;
              const isLastCol = colIndex === colCount - 1;
              const isReadOnly =
                rowIndex === 0 || colIndex === 0 || colIndex === 1 || isLastCol;

              const defaultVal = initialTable[rowIndex]?.[colIndex] ?? "";

              if (rowIndex === 0 || colIndex === 0 || colIndex === 1) {
                return (
                  <Grid
                    item
                    sx={{
                      flex: colIndex === 0 ? 1 : 3,
                      minWidth: 0,
                      display: "flex",
                      alignItems: "center",
                    }}
                    key={`cell-${rowIndex}-${colIndex}`}
                  >
                    <Typography
                      variant="body2"
                      sx={{ fontWeight: rowIndex === 0 ? 600 : 400 }}
                    >
                      {defaultVal || "—"}
                    </Typography>
                  </Grid>
                );
              }
              return (
                <Grid
                  item
                  sx={{
                    flex: colIndex === 0 ? 1 : 3,
                    minWidth: 0,
                  }}
                  key={`cell-${rowIndex}-${colIndex}`}
                >
                  <Controller
                    name={name}
                    control={control}
                    defaultValue={defaultVal}
                    render={({ field: rhfField }) => (
                      <TextField
                        {...rhfField}
                        fullWidth
                        size="small"
                        type="number"
                        disabled={isLastCol}
                        inputProps={{
                          inputMode: "numeric",
                          pattern: "[0-9]*",
                          min: 0,
                        }}
                      />
                    )}
                  />
                </Grid>
              );
            })}
          </Grid>
        ))}

        {/* Footer Jumlah */}
        <Divider sx={{ m: 2, borderColor: "#DADADA" }} />
        <Box
          sx={{
            display: "flex",
            justifyContent: "right",
            alignItems: "center",
            gap: 2,
          }}
        >
          <Typography sx={{ color: "#666666", fontSize: "12px" }}>
            Jumlah
          </Typography>
          <Grid item xs={12} md={3}>
            <TextField size="small" disabled fullWidth value={totalJumlah} />
          </Grid>
        </Box>
      </Grid>
    </>
  );
};

export default TransaksiTable;
