import {
  <PERSON>rid,
  TextField,
  Typography,
  Select,
  MenuItem,
  FormControl,
  InputAdornment,
  Theme,
  FormHelperText,
  SxProps,
  TextFieldProps,
  IconButton,
  FormControlLabel,
  Checkbox,
  Radio,
  RadioGroup,
  Box,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
} from "@mui/material";
import { createStyles, makeStyles } from "@mui/styles";
import React, {
  ChangeEvent,
  MouseEventHandler,
  ReactNode,
  useEffect,
  useRef,
  useState,
  WheelEvent,
} from "react";
import { useTranslation } from "react-i18next";
import { EyeIcon } from "../icons";
import { EyeSlashIcon } from "../icons/eyeSlash";
import { Clear, ExpandMoreRounded } from "@mui/icons-material";
import dayjs from "dayjs";
import utc from "dayjs/plugin/utc";
import {
  DatePicker,
  DateTimePicker,
  LocalizationProvider,
  PickersDay,
} from "@mui/x-date-pickers";
import { toCapitalCase } from "@/helpers/string";
import { ms } from "date-fns/locale/ms";
import { enUS } from "date-fns/locale/en-US";
import { AdapterDateFns } from "@mui/x-date-pickers/AdapterDateFnsV3";
import Quill from "quill";
import "quill/dist/quill.snow.css";
import { ButtonOutline, ButtonPrimary } from "../button";
import { GridCheckCircleIcon } from "@mui/x-data-grid";
import { quillColors } from "@/helpers";
import CurrencyInput from "react-currency-input-field";
import localizedFormat from "dayjs/plugin/localizedFormat";
import weekday from "dayjs/plugin/weekday";
import advancedFormat from "dayjs/plugin/advancedFormat";

type SelectChangeEvent = React.ChangeEvent<{ value: unknown }>;

export type Props = TextFieldProps & {
  isLabel?: boolean;
  isLabelNoSpace?: boolean;
  isMultiField?: boolean;
  label?: string;
  value?: string | number | string[] | number[];
  onChange?: (
    event: SelectChangeEvent | React.ChangeEvent<HTMLInputElement> | any
  ) => void;
  disabled?: boolean;
  fullWidth?: boolean;
  type?: string;
  required?: boolean;
  subTitle?: string;
  placeholder?: string;
  multiline?: boolean;
  rows?: number;
  options?: { value: string | number | null; label: string }[];
  optionsNullValue?: string;
  isLoadingData?: boolean;
  dynamicData?: { id: string; name: string; pid: string | number }[];
  useDynamicData?: boolean;
  pid?: string | number;
  customClass?: any;
  iconStart?: React.ReactNode;
  iconEnd?: React.ReactNode;
  error?: boolean;
  helperText?: any;
  textColor?: string;
  fontWeight?: string;
  sx?: SxProps<Theme>;
  comment?: string;
  commentColor?: string;
  alignItems?: string;
  optionsEnum?: boolean;
  availableDate?: string[];
  disabledDates?: string[];
  isStandardSize?: boolean;
  radioSquare?: boolean;
  minDate?: string;
  fullFeatureRichText?: boolean;
  /**
   * this props is applicable if {@link type} value is `select`
   * @default true
   */
  withClearIcon?: boolean;

  /**
   * this props is applicable if {@link type} value is `select` and {@link withClearIcon} is true
   */
  onClearIconClicked?: MouseEventHandler<HTMLButtonElement>;

  /**
   * this props only apply when {@link type} value is `select`.
   * @default 1
   */
  selectStyleProfileId?: 1 | 2;

  /**
   * @default false
   */
  onlyAcceptNumber?: boolean;

  /**
   * @default null
   */
  digitsLimit?: number | null;

  /**
   * @description converted to currency format
   * @default false
   */
  formattedNumber?: boolean;

  dateInputComponent?: (props: Props) => ReactNode;

  /**
   * @default true
   */
  trimHelperText?: boolean;
};

interface RichTextInputProps {
  value: string | number | number[] | string[] | undefined;
  onChange?: (e: { target: { name: string; value: string } }) => void;
  name?: string;
  placeholder?: string;
  disabled?: boolean;
  required?: boolean;
  iconStart?: React.ReactNode;
  iconEnd?: React.ReactNode;
  error?: boolean;
  helperText?: any;
  rows?: number;
  sx?: SxProps<Theme>;
  customClass?: SxProps<Theme>;
  multiline?: boolean;
  fullFeatureRichText?: boolean;
  t: any;
}

const useStyles = makeStyles((theme: Theme) =>
  createStyles({
    endAdornment: {
      paddingRight: 0,
    },
  })
);

dayjs.extend(utc);
dayjs.extend(localizedFormat);
dayjs.extend(weekday);
dayjs.extend(advancedFormat);

export const CustomPickersDay = (
  props: any & {
    availableDate?: string[];
    disabledDates?: string[];
    minDate?: string;
  }
) => {
  const { day, availableDate, disabledDates, minDate, ...other } = props;
  const formatted = dayjs(day).format("YYYY-MM-DD");

  const isAllowed =
    Array.isArray(availableDate) && availableDate.length > 0
      ? availableDate.includes(formatted)
      : true;

  const isDisabledDate =
    Array.isArray(disabledDates) && disabledDates.length > 0
      ? disabledDates.includes(formatted)
      : false;

  const isBeforeMinDate = minDate
    ? dayjs(day).isBefore(dayjs(minDate), "day")
    : false;

  const isDisabled = isDisabledDate || isBeforeMinDate;

  return (
    <PickersDay
      {...other}
      day={day}
      disabled={isDisabled}
      sx={{
        opacity: !isAllowed ? 0.5 : isDisabled ? 0.2 : 1,
      }}
      onClick={(e) => {
        if (!isDisabled) {
          props.onClick?.(e);
        }
      }}
    />
  );
};

function RichTextInput({
  value,
  onChange,
  name = "",
  placeholder,
  disabled,
  required,
  iconStart,
  iconEnd,
  error,
  helperText,
  rows = 4,
  sx,
  customClass,
  t,
  fullFeatureRichText = false, // New prop to enable advanced features
}: RichTextInputProps) {
  const editorRef = useRef<HTMLDivElement | null>(null);
  const quillRef = useRef<Quill | null>(null);
  const [linkDialog, setLinkDialog] = useState({
    open: false,
    url: "",
  });

  const [selectionRange, setSelectionRange] = useState<ReturnType<
    Quill["getSelection"]
  > | null>(null);

  const latestOnChange = useRef(onChange);

  useEffect(() => {
    latestOnChange.current = onChange;
  }, [onChange]);

  useEffect(() => {
    if (editorRef.current && !quillRef.current) {
      quillRef.current = new Quill(editorRef.current, {
        theme: "snow",
        readOnly: disabled,
        placeholder,
        modules: {
          toolbar: {
            container: disabled
              ? []
              : fullFeatureRichText
              ? [
                  [{ header: [1, 2, 3, 4, 5, 6, false] }],
                  ["bold", "italic", "underline", "strike"],
                  // [{ color: [] }, { background: [] }],
                  [{ color: quillColors }, { background: quillColors }],
                  [{ list: "ordered" }, { list: "bullet" }],
                  [{ indent: "-1" }, { indent: "+1" }],
                  [{ align: [] }],
                  ["link"],
                  ["clean"],
                ]
              : [
                  [{ list: "ordered" }, { list: "bullet" }],
                  ["bold", "italic", "underline"],
                  ["link"],
                ],
            handlers: {
              link: function (this: { quill: Quill }) {
                if (disabled) return; // Block dialog if disabled
                const range = this.quill.getSelection();
                if (range) {
                  const currentLink = this.quill.getFormat(range).link;
                  setSelectionRange(range); // Save the range
                  setLinkDialog({
                    open: true,
                    // @ts-ignore
                    url: currentLink || "",
                  });
                }
              },
            },
          },
        },
      });

      // Fix for link tooltip
      setTimeout(() => {
        const linkTooltip = document.querySelector(
          '.ql-tooltip[data-mode="link"]'
        );
        if (linkTooltip) {
          linkTooltip.addEventListener("click", (e) => e.stopPropagation());
        }
      }, 100);

      quillRef.current.on("text-change", () => {
        const html = quillRef.current?.root.innerHTML || "";
        latestOnChange.current?.({ target: { name, value: html } });
      });
    }

    if (
      quillRef.current &&
      value !== quillRef.current.root.innerHTML &&
      typeof value === "string"
    ) {
      // Only update content if it really changed
      quillRef.current.clipboard.dangerouslyPasteHTML(value);
    }
  }, [value, onChange, name, disabled, placeholder, fullFeatureRichText]);

  useEffect(() => {
    if (quillRef.current) {
      quillRef.current.enable(!disabled);
    }
  }, [disabled]);

  return (
    <Box sx={{ display: "flex", flexDirection: "column", ...sx }}>
      <Box
        sx={{
          position: "relative",
          border: "1px solid",
          borderColor: error ? "error.main" : "#c4c4c4",
          borderRadius: "5px",
          backgroundColor: disabled ? "#E8E9E8" : "white",
          minHeight: rows * 24,
          ...customClass,
          "& .ql-toolbar.ql-snow": {
            border: "none",
            borderBottom: "1px solid var(--text-light-grey)",
          },
          "& .ql-snow .ql-picker": {
            color: "black",
          },
          "& .ql-snow .ql-stroke": {
            stroke: "black",
          },
          "& .ql-snow .ql-fill": {
            fill: "black",
          },
        }}
      >
        {iconStart && (
          <InputAdornment
            position="start"
            sx={{ position: "absolute", top: 12, left: 8 }}
          >
            {iconStart}
          </InputAdornment>
        )}

        <Box
          ref={editorRef}
          sx={{
            pl: iconStart ? 4 : 0,
            pr: iconEnd ? 4 : 0,
            border: "none",
            fontFamily: "Poppins, sans-serif !important",
            // Hide the preview toolbar completely
            "& .ql-preview, & .ql-action, & .ql-remove": {
              display: "none !important",
              fontFamily: "Poppins, sans-serif !important",
            },
            "& .ql-editor": {
              fontSize: "14px",
              color: "black",
              fontFamily: "Poppins, sans-serif !important",
              minHeight: rows * 24,
              padding: 2,
              border: "none !important",
            },
            // Also hide the input element if present
            "& input[type='text']": {
              display: "none !important",
              border: "none",
              fontFamily: "Poppins, sans-serif !important",
            },
            "& .ql-toolbar.ql-snow": {
              backgroundColor: "#f5f5f5",
              border: "none",
              padding: "4px 8px",
              fontFamily: "Poppins, sans-serif !important",
            },
            "& .ql-container.ql-snow": {
              border: "none",
              borderRadius: "0 0 5px 5px",
              fontFamily: "Poppins, sans-serif !important",
            },
          }}
        />

        {iconEnd && (
          <InputAdornment
            position="end"
            sx={{ border: "none", position: "absolute", top: 12, right: 8 }}
          >
            {iconEnd}
          </InputAdornment>
        )}
      </Box>

      {(helperText || error) && (
        <FormHelperText sx={{ ml: "0px" }} error={error}>
          {helperText}
        </FormHelperText>
      )}
      <Dialog
        open={linkDialog.open}
        onClose={() => setLinkDialog({ ...linkDialog, open: false })}
      >
        <DialogTitle>{t("insertLink")}</DialogTitle>
        <DialogContent>
          <TextField
            autoFocus
            margin="dense"
            label="URL"
            type="url"
            fullWidth
            variant="outlined"
            value={linkDialog.url}
            onChange={(e) =>
              setLinkDialog({ ...linkDialog, url: e.target.value })
            }
            placeholder="https://example.com"
          />
        </DialogContent>
        <DialogActions sx={{ m: 2 }}>
          <ButtonOutline
            onClick={() => setLinkDialog({ ...linkDialog, open: false })}
          >
            {t("cancel")}
          </ButtonOutline>
          <ButtonPrimary
            onClick={() => {
              const quill = quillRef.current;
              let url = linkDialog.url.trim();

              if (url && quill) {
                // Add https:// if no scheme is provided
                if (!/^https?:\/\//i.test(url)) {
                  url = `https://${url}`;
                }

                if (selectionRange) {
                  quill.setSelection(selectionRange);
                  quill.format("link", url);
                }
              }

              setLinkDialog({ ...linkDialog, open: false });
            }}
            startIcon={<GridCheckCircleIcon />}
          >
            {t("terima")}
          </ButtonPrimary>
        </DialogActions>
      </Dialog>
    </Box>
  );
}

function Input(inputProps: Props) {
  const {
    isLabel = true,
    isLabelNoSpace = true,
    isMultiField = false,
    label,
    required,
    subTitle,
    onChange,
    multiline,
    rows,
    options = [],
    optionsNullValue = "",
    type = "text",
    isLoadingData = false,
    dynamicData = [],
    useDynamicData = false,
    pid = 1,
    customClass = {},
    sx,
    comment,
    commentColor,
    alignItems = "center",
    optionsEnum = false,
    withClearIcon = false,
    onClearIconClicked,
    selectStyleProfileId = 1,
    onlyAcceptNumber = false,
    digitsLimit = null,
    placeholder,
    availableDate = [],
    isStandardSize = false,
    radioSquare = false,
    disabledDates = [],
    minDate = "",
    fullFeatureRichText = false,
    formattedNumber = false,
    dateInputComponent: initialDateInputComponent,
    trimHelperText = true,
    ...props
  } = inputProps;
  const { t, i18n } = useTranslation();
  const [showPassword, setShowPassword] = useState(false);
  const textFieldRef = useRef<HTMLInputElement>(null);

  const locale = ["my", "ms"].includes(i18n.language) ? ms : enUS;

  const togglePasswordVisibility = () => {
    setShowPassword((prevState) => !prevState);
  };

  const classes = useStyles();
  const dateInputComponent = initialDateInputComponent?.(inputProps) ?? (
    <LocalizationProvider adapterLocale={locale} dateAdapter={AdapterDateFns}>
      <FormControl
        fullWidth
        variant="outlined"
        size="small"
        disabled={props.disabled || isLoadingData}
        error={props.error}
        required={required}
        sx={{
          "& .MuiFormHelperText-root": {
            ml: 0,
          },
        }}
      >
        <DatePicker
          sx={{
            background: props.disabled ? "#E8E9E8" : "",
            "& .MuiInputBase-input.Mui-disabled": {
              WebkitTextFillColor: "#666666",
            },
            borderRadius: "5px",
            fontSize: "14px",
            "& .MuiSelect-select": {
              color: "black",
              fontSize: "16px",
            },
            "&.Mui-disabled .MuiSelect-select": {
              WebkitTextFillColor: "#666666",
            },
            ...customClass,
            ...sx,
          }}
          name={props.name}
          format="dd-MM-yyyy"
          value={
            Array.isArray(props.value)
              ? dayjs(props.value.join("-"), "YYYY-MM-DD").toDate()
              : props.value
              ? dayjs(props.value, ["YYYY-MM-DD", "DD-MM-YYYY"]).toDate()
              : null
          }
          disabled={props.disabled}
          onChange={(date) => {
            if (!date) return;

            const event = {
              target: {
                name: props.name,
                value: dayjs(date).format("YYYY-MM-DD"), // Store in "YYYY-MM-DD"
              },
            };

            if (onChange) {
              onChange(event);
            }
          }}
          slots={{
            day: (dayProps) => (
              <CustomPickersDay
                {...dayProps}
                availableDate={availableDate}
                disabledDates={disabledDates}
                minDate={minDate}
              />
            ),
          }}
          slotProps={{
            textField: {
              fullWidth: true,
              size: "small",
              "aria-readonly": true,
              helperText: props.helperText,
              error: props.error,
            },
          }}
        />
      </FormControl>
    </LocalizationProvider>
  );

  function parseInputDate(dateValue: any) {
    //supports the following formats
    // - Date objects
    // - ISO strings
    // - Date strings (DD-MM-YYYY, DD/MM/YYYY)
    // - Arrays: [DD,MM,YYYY], [YYYY,MM,DD], [DD,MM,YYYY,HH,mm,ss], [YYYY,MM,DD,HH,mm,ss]

    if (dateValue instanceof Date) return dateValue;

    // Handle array formats
    if (Array.isArray(dateValue)) {
      const arr = dateValue.map(Number); // Convert all elements to numbers

      if (arr.length >= 3) {
        // Determine if array starts with year (4-digit) or day
        const isYearFirst = String(arr[0]).length === 4;

        // Extract date components
        const year = isYearFirst ? arr[0] : arr[2];
        const month = (isYearFirst ? arr[1] : arr[1]) - 1; // Months are 0-indexed
        const day = isYearFirst ? arr[2] : arr[0];

        // Default to midnight if no time components
        const hours = arr[3] || 0;
        const minutes = arr[4] || 0;
        const seconds = arr[5] || 0;

        return new Date(year, month, day, hours, minutes, seconds);
      }
    }

    // Handle string formats
    if (typeof dateValue === "string") {
      // ISO format or contains time
      if (dateValue.includes("T") || dateValue.includes(":")) {
        return new Date(dateValue.replace(" ", "T")); // Ensure proper ISO format
      }

      // DD-MM-YYYY or DD/MM/YYYY
      const dateParts = dateValue.split(/[-\/]/);
      if (dateParts.length === 3) {
        return new Date(
          parseInt(dateParts[2], 10),
          parseInt(dateParts[1], 10) - 1,
          parseInt(dateParts[0], 10),
          0,
          0,
          0
        );
      }
    }

    // Fallback to current date at midnight
    const now = new Date();
    return new Date(now.getFullYear(), now.getMonth(), now.getDate(), 0, 0, 0);
  }

  const dayDateTimeInputComponent = (
    <LocalizationProvider adapterLocale={locale} dateAdapter={AdapterDateFns}>
      <FormControl
        fullWidth
        variant="outlined"
        size="small"
        disabled={props.disabled || isLoadingData}
        error={props.error}
        required={required}
        sx={{
          "& .MuiFormHelperText-root": {
            ml: 0,
          },
        }}
      >
        <DateTimePicker
          // disabled={props.disabled || isLoadingData}
          sx={{
            background: props.disabled ? "#E8E9E8" : "",
            pointerEvents: props.disabled ? "none" : "auto",
            "& .MuiInputBase-input": {
              color: props.disabled ? "#666666" : "inherit",
              WebkitTextFillColor: props.disabled ? "#666666" : "inherit",
            },
            "& .MuiIconButton-root": {
              color: props.disabled ? "#999" : "inherit",
            },
            ...customClass,
            ...sx,
          }}
          name={props.name}
          value={props.value ? parseInputDate(props.value) : null}
          onChange={(date) => {
            if (!date) return;

            const event = {
              target: {
                name: props.name,
                value: dayjs(date).format("YYYY-MM-DD HH:mm:ss"),
              },
            };

            if (onChange) {
              onChange(event);
            }
          }}
          format="EEEE, dd-MM-yyyy hh:mm:ss a"
          slotProps={{
            textField: {
              fullWidth: true,
              size: "small",
              "aria-readonly": true,
              helperText: props.helperText,
              error: props.error,
            },
          }}
        />
      </FormControl>
    </LocalizationProvider>
  );

  // Tentukan opsi berdasarkan kondisi dinamis atau statis
  const selectOptions = useDynamicData
    ? dynamicData
        .filter((item) => item.pid == pid)
        .map((item: any) => ({
          value: item.id,
          label: item.name
            ? item.name
            : i18n.language === "en"
            ? item.nameEn
            : item.nameBm,
        }))
    : options;

  const formFieldComponents = (
    <>
      {type === "date" ? (
        dateInputComponent
      ) : type === "datetime" ? (
        dayDateTimeInputComponent
      ) : type === "radio" ? (
        <FormControl component="fieldset" required={required}>
          <RadioGroup
            row
            name={props.name}
            value={props.value}
            onChange={onChange}
          >
            {options.map((option) => (
              <FormControlLabel
                key={option.value}
                value={option.value}
                control={
                  radioSquare ? (
                    <Checkbox
                      color="primary"
                      checked={props.value === option.value}
                      onChange={onChange}
                      icon={
                        <Box
                          sx={{
                            width: 20,
                            height: 20,
                            border: "1px solid var(--indicator-grey)",
                            borderRadius: "4px",
                          }}
                        />
                      }
                      checkedIcon={
                        <Box
                          sx={{
                            width: 20,
                            height: 20,
                            backgroundColor: "var(--primary-color)",
                            borderRadius: "4px",
                          }}
                        />
                      }
                      sx={{
                        ml: 0.2,
                      }}
                    />
                  ) : (
                    <Radio color="primary" />
                  )
                }
                label={option.label}
                disabled={props.disabled}
                sx={{
                  "& .MuiFormControlLabel-label": {
                    color: props.textColor || "#666666",
                    fontWeight: props.fontWeight || 400,
                    fontSize: "14px",
                  },
                }}
              />
            ))}
          </RadioGroup>
        </FormControl>
      ) : type === "checkbox" ? (
        <FormControlLabel
          control={
            <Checkbox
              checked={Boolean(props.value)}
              onChange={onChange}
              disabled={props.disabled}
              color="primary"
            />
          }
          label={label}
        />
      ) : type === "select" ? (
        <FormControl
          fullWidth
          variant="outlined"
          size="small"
          disabled={props.disabled || isLoadingData}
          error={props.error}
          required={required}
        >
          <Select
            IconComponent={ExpandMoreRounded}
            name={props.name}
            required={required}
            value={props.value || ""}
            onChange={(event) =>
              onChange && onChange(event as SelectChangeEvent)
            }
            displayEmpty
            sx={{
              background: props.disabled ? "#E8E9E8" : "",
              borderRadius: "5px",
              fontSize: "14px",
              "& .MuiSelect-select": {
                color: "black",
                fontSize: "14px",
              },
              "&.Mui-disabled .MuiSelect-select": {
                WebkitTextFillColor: "#666666",
              },
              ...customClass,
            }}
            renderValue={(selected) => {
              if (options.length === 0) {
                return "-";
              }

              const selectedOption = options?.find(
                (option) => option.value === selected
              );

              if (selectedOption) {
                const label = optionsEnum
                  ? t(selectedOption.label)
                  : selectedOption.label;
                return <span style={{ color: "black" }}>{label}</span>;
              }

              if (selected && !selectedOption) {
                return <span style={{ color: "black" }}>-</span>;
              }

              if (optionsNullValue && selectedOption == null) {
                return (
                  <span style={{ color: "black" }}>{optionsNullValue}</span>
                );
              }

              return (
                <span style={{ color: "#9e9e9e" }}>
                  {isLoadingData
                    ? "Loading..."
                    : toCapitalCase(
                        placeholder ? placeholder : t("selectPlaceholder")
                      )}
                </span>
              );
            }}
            {...(withClearIcon
              ? {
                  endAdornment: (
                    <InputAdornment sx={{ marginRight: "1rem" }} position="end">
                      <IconButton onClick={onClearIconClicked}>
                        <Clear />
                      </IconButton>
                    </InputAdornment>
                  ),
                }
              : {})}
          >
            {useDynamicData
              ? [
                  <MenuItem value="" disabled key="placeholder">
                    {isLoadingData
                      ? "Loading..."
                      : toCapitalCase(
                          placeholder ? placeholder : t("selectPlaceholder")
                        )}
                  </MenuItem>,
                  ...(!isLoadingData
                    ? selectOptions?.map((option) => (
                        <MenuItem key={option.value} value={option.value}>
                          {option.label}
                        </MenuItem>
                      ))
                    : []),
                ]
              : selectOptions?.map((option) => (
                  <MenuItem key={option.value} value={option.value}>
                    {optionsEnum ? t(option.label) : option.label}
                  </MenuItem>
                ))}
          </Select>
          {!props?.disabled &&
            Boolean(
              trimHelperText ? props.helperText?.trim() : props.helperText
            ) && (
              <FormHelperText sx={{ ml: "0px" }}>
                {props.helperText}
              </FormHelperText>
            )}
        </FormControl>
      ) : type === "richText" ? (
        <>
          <RichTextInput
            fullFeatureRichText={fullFeatureRichText}
            name={props.name}
            value={props.value}
            onChange={onChange}
            error={props.error}
            helperText={props.helperText ?? (props.disabled && "")}
            required={required}
            disabled={props.disabled}
            placeholder={placeholder}
            iconStart={props.iconStart}
            iconEnd={props.iconEnd}
            rows={rows || 4}
            customClass={customClass}
            sx={sx}
            t={t}
          />
        </>
      ) : type === "currrency" ? (
        <>
          <TextField
            fullWidth
            variant="outlined"
            onChange={onChange}
            required={required}
            multiline={multiline}
            autoFocus={props.error}
            placeholder={toCapitalCase(placeholder ? placeholder : "")}
            InputProps={{
              inputComponent: CurrencyInput as any,
              inputProps: {
                decimalsLimit: 2,
                allowNegativeValue: false,
              },
              startAdornment: props.iconStart ? (
                <InputAdornment position="start">
                  {props.iconStart}
                </InputAdornment>
              ) : (
                ""
              ),
              classes: {
                adornedEnd: classes.endAdornment,
              },
            }}
            rows={multiline ? rows : undefined}
            {...props}
            size="small"
            sx={{
              background: props.disabled ? "#E8E9E8" : "",
              borderRadius: "5px",
              "& fieldset": { borderRadius: "5px" },
              "& .MuiInputBase-input.Mui-disabled": {
                WebkitTextFillColor: "#666666",
              },
              "& .MuiInputBase-input": {
                color: "black",
              },
              [`& input[type="number"]`]: {
                MozAppearance: "textfield",
                appearance: "textfield",
              },
              "& input[type=number]::-webkit-outer-spin-button": {
                WebkitAppearance: "none",
                margin: 0,
              },
              "& input[type=number]::-webkit-inner-spin-button": {
                WebkitAppearance: "none",
                margin: 0,
              },
              "& input::placeholder, & textarea::placeholder": {
                fontSize: "14px !important",
              },
              ...customClass,
              ...sx,
            }}
            error={props.error}
            helperText={props?.helperText ?? (props?.disabled && "")}
            {...((onlyAcceptNumber ||
              (digitsLimit && typeof digitsLimit === "number")) && {
              onInput: (e: ChangeEvent<HTMLInputElement>) => {
                const newValue = digitsLimit
                  ? e.target.value.replace(/\D/g, "").slice(0, digitsLimit)
                  : e.target.value.replace(/\D/g, "");
                e.target.value = newValue;
                props?.onInput?.(e);
              },
              onWheel: (e: WheelEvent<HTMLInputElement>) => {
                e.preventDefault();
                textFieldRef.current?.blur();
                props?.onWheel?.(e);
              },
              onWheelCapture: (e: WheelEvent<HTMLInputElement>) => {
                e.preventDefault();
                textFieldRef.current?.blur();
                props?.onWheelCapture?.(e);
              },
              inputProps: {
                ...(digitsLimit && {
                  pattern: new RegExp(`/^\d${digitsLimit}$/`),
                }),
                ...props?.inputProps,
              },
            })}
          />
          {comment && (
            <Typography
              sx={{ color: commentColor && commentColor, fontSize: "11px" }}
              className="label"
            >
              {comment}
            </Typography>
          )}
        </>
      ) : (
        <>
          <TextField
            type={
              type !== "password" ? type : showPassword ? "text" : "password"
            }
            fullWidth
            variant="outlined"
            onChange={onChange}
            required={required}
            multiline={multiline}
            autoFocus={props.error}
            placeholder={toCapitalCase(placeholder ? placeholder : "")}
            InputProps={{
              startAdornment: props.iconStart ? (
                <InputAdornment position="start">
                  {props.iconStart}
                </InputAdornment>
              ) : (
                ""
              ),
              endAdornment:
                type !== "password" ? (
                  props.iconEnd ? (
                    <InputAdornment position="end">
                      {props.iconEnd}
                    </InputAdornment>
                  ) : (
                    ""
                  )
                ) : (
                  <InputAdornment position="end">
                    <IconButton
                      sx={{ marginRight: 1 }}
                      onClick={togglePasswordVisibility}
                      edge="end"
                    >
                      {showPassword ? <EyeIcon /> : <EyeSlashIcon />}
                    </IconButton>
                  </InputAdornment>
                ),
              classes: {
                adornedEnd: classes.endAdornment,
              },
            }}
            rows={multiline ? rows : undefined}
            {...props}
            size="small"
            sx={{
              background: props.disabled ? "#E8E9E8" : "",
              borderRadius: "5px",
              "& fieldset": { borderRadius: "5px" },
              "& .MuiInputBase-input.Mui-disabled": {
                WebkitTextFillColor: "#666666",
              },
              "& .MuiInputBase-input": {
                color: "black",
              },
              [`& input[type="number"]`]: {
                MozAppearance: "textfield",
                appearance: "textfield",
              },
              "& input[type=number]::-webkit-outer-spin-button": {
                WebkitAppearance: "none",
                margin: 0,
              },
              "& input[type=number]::-webkit-inner-spin-button": {
                WebkitAppearance: "none",
                margin: 0,
              },
              "& input::placeholder, & textarea::placeholder": {
                fontSize: "14px !important",
              },
              ...customClass,
              ...sx,
            }}
            error={props.error}
            helperText={props?.helperText ?? (props?.disabled && "")}
            FormHelperTextProps={{
              sx: {
                backgroundColor: "transparent",
                marginLeft: 0,
                marginRight: 0,
                marginTop: 0,
                ...(props.disabled && {
                  backgroundColor: "#fff", // ensure it's not same as disabled bg
                  padding: "0 4px",
                }),
              },
            }}
            {...((onlyAcceptNumber ||
              (digitsLimit && typeof digitsLimit === "number")) && {
              onInput: (e: ChangeEvent<HTMLInputElement>) => {
                const newValue = digitsLimit
                  ? e.target.value.replace(/\D/g, "").slice(0, digitsLimit)
                  : e.target.value.replace(/\D/g, "");
                e.target.value = newValue;
                props?.onInput?.(e);
              },
              onWheel: (e: WheelEvent<HTMLInputElement>) => {
                e.preventDefault();
                textFieldRef.current?.blur();
                props?.onWheel?.(e);
              },
              onWheelCapture: (e: WheelEvent<HTMLInputElement>) => {
                e.preventDefault();
                textFieldRef.current?.blur();
                props?.onWheelCapture?.(e);
              },
              inputProps: {
                ...(digitsLimit && {
                  pattern: new RegExp(`/^\d${digitsLimit}$/`),
                }),
                ...props?.inputProps,
              },
            })}
          />
          {comment && (
            <Typography
              sx={{ color: commentColor && commentColor, fontSize: "11px" }}
              className="label"
            >
              {comment}
            </Typography>
          )}
        </>
      )}
    </>
  );

  return isLabelNoSpace ? (
    <Grid
      container
      spacing={2}
      alignItems={
        type === "select" && selectStyleProfileId === 2
          ? "flex-start"
          : alignItems
      }
      sx={{
        ...(!props?.disabled && selectStyleProfileId === 2 && { mb: 1 }),
        ...(selectStyleProfileId === 1 && { mb: 1 }),
        ...(required && props?.helperText ? { alignItems: "flex-start" } : {}),
      }}
    >
      {isLabelNoSpace && (
        <Grid item xs={12} sm={isStandardSize ? 3 : 4}>
          {isLabel && (
            <>
              <Typography
                variant="body1"
                sx={{
                  color: props.textColor ? props.textColor : "#666666",
                  fontWeight: props.fontWeight
                    ? props.fontWeight
                    : "400 !important",
                  fontSize: "14px",
                }}
              >
                {label}
                {required && <span style={{ color: "red" }}>*</span>}
              </Typography>
              <Typography variant="body2">{subTitle}</Typography>
            </>
          )}
        </Grid>
      )}

      <Grid item xs={12} sm={isStandardSize ? 9 : 8}>
        {/** insert form field here */}
        {formFieldComponents}
      </Grid>
    </Grid>
  ) : (
    <Box>{formFieldComponents}</Box>
  );
}

export default Input;
