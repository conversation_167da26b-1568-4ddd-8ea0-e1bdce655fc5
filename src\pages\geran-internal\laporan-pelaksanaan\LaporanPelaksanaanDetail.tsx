import {
  DecisionOptionsCode,
  DecisionOptionsCodeGeran,
  geranRole,
  globalStyles,
  useMutation,
  useQuery,
} from "@/helpers";
import AccordionComp from "@/pages/pengurusan-pertubuhan-internal/View/Accordion";

import { Box, Grid, Typography, useTheme } from "@mui/material";

import { API_URL } from "@/api";
import {
  ButtonPrimary,
  SelectFieldController,
  SocietyBanner,
  TextFieldController,
} from "@/components";
import { useEffect, useState } from "react";
import { FieldValues, useForm } from "react-hook-form";
import { useTranslation } from "react-i18next";
import { useNavigate, useParams } from "react-router-dom";
import LaporanPelaksanaanSection from "./LaporanPelaksanaanSection";
import { LoadingOverlay } from "@/components/loading";
type ReadStatusType = {
  [key: number]: boolean;
};
const LaporanPelaksanaanDetail: React.FC = () => {
  const [hasJPPMHQRole, setHasJPPMHQRole] = useState(false);
  const [hasJPPMHStateRole, setHasJPPMStateRole] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const classes = globalStyles();
  const { t } = useTranslation();
  const [readStatus, setReadStatus] = useState<ReadStatusType>({});
  const navigate = useNavigate();
  const [isSuccess, setIsSuccess] = useState(false);
  const [dialogSejarahKuiriSaveOpen, setDialogSejarahKuiriSaveOpen] =
    useState(false);
  const theme = useTheme();
  const { applicationGeranId, societyId, reportId } = useParams();
  const [currentExpandSection, setCurrentExpandSection] = useState<
    number | false
  >(false);
  const decisionAllowPublicOptions = [
    {
      value: 1,
      label: "Ya",
    },
    {
      value: 0,
      label: "Tidak",
    },
  ];
  const labelStyle = {
    fontSize: "16px",
    color: "#666666",
    fontWeight: "400 !important",
  };

  const { control, handleSubmit, getValues, watch, setValue } =
    useForm<FieldValues>({
      defaultValues: {
        approvalStatus: "",
        rejectReason: "",
        note: "",
        decisionAllowPublic: 0,
      },
    });
  const decisionAllowPublic = watch("decisionAllowPublic");
  const [isFormDisabled, setIsFormDisabled] = useState(false);

  const fetchUserRoles = async () => {
    try {
      setIsLoading(true);
      const portal = localStorage.getItem("portal") || "";
      const authToken = localStorage.getItem("refine-auth");

      if (!authToken) {
        throw new Error("Authorization token is missing");
      }

      const response = await fetch(
        `${API_URL}/user/profile/getUserInternalDetails`,
        {
          headers: {
            portal,
            authorization: `Bearer ${authToken}`,
            "Content-Type": "application/json",
          },
        }
      );

      if (!response.ok) {
        throw new Error(`Error: ${response.status} ${response.statusText}`);
      }

      const data = await response.json();
      const isJppmBranch = data?.data?.isJppmBranch;
      if (
        data?.data?.userRole?.length > 0 &&
        ((data.data.userRole.includes("PEGAWAI PENDAFTARAN PERTUBUHAN (RO)") &&
          !isJppmBranch) ||
          data.data.userRole.includes(geranRole.ADMIN_GERAN_HQ))
      ) {
        setHasJPPMHQRole(true);
      } else if (
        (data?.data?.userRole?.length > 0 &&
          data.data.userRole.includes("PEGAWAI PENDAFTARAN PERTUBUHAN (RO)") &&
          isJppmBranch) ||
        data.data.userRole.includes(geranRole.ADMIN_GERAN_NEGERI)
      ) {
        setHasJPPMStateRole(true);
      } else {
        setHasJPPMHQRole(false);
        setHasJPPMStateRole(false);
      }
    } catch (error) {
      console.error("Failed to fetch user data");
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    fetchUserRoles();
  }, []);

  const decisionOptions = DecisionOptionsCodeGeran(t).filter(
    (i) => i.value !== 36
  );
  const {
    data: grantReportDetailDataResponse,
    isLoading: isLoadingGrantTemplate,
  } = useQuery({
    url: `grant/report/application/${applicationGeranId}`,
    onSuccess: (data) => {
      const grantReports = data?.data?.data;
      setValue("title", grantReports.title);
      setValue("description", grantReports.description);
      setValue("note", grantReports.note);
      setValue("applicationStatusCode", grantReports.applicationStatusCode),
        setValue(
          "decisionAllowPublic",
          grantReports.allowPublicDisplay ? 1 : 0
        );
    },
  });

  const sectionItems = [
    {
      subTitle: "Laporan Pelaksanaan Geran",
      component: <LaporanPelaksanaanSection />,
    },
  ];
  const handleChangeCurrentExpandSection =
    (item: number) => (event: React.SyntheticEvent, isExpanded: boolean) => {
      setCurrentExpandSection(isExpanded ? item : false);

      if (isExpanded) {
        setReadStatus((prevState) => {
          const updatedStatus = sectionItems.reduce((acc, _, i) => {
            if (i + 1 <= item) {
              acc[i + 1] = true;
            } else {
              acc[i + 1] = !!prevState[i + 1] || false;
            }
            return acc;
          }, {} as Record<number, boolean>);
          return updatedStatus;
        });
      }
    };

  const { data: societyListDataResponse, isLoading: isLoadingSociety } =
    useQuery({ url: `society/${societyId}` });
  const societyData = societyListDataResponse?.data?.data ?? {};

  const {
    fetch: updatingApprovalReportGeran,
    isLoading: isUpdatingApprovalReportGeran,
  } = useMutation({
    url: "grant/report/state-approval",
    method: "put",
    onSuccess: (res) => {
      const responseCode = res?.data?.code;

      if (responseCode === 200) {
        setIsSuccess(true);
        setTimeout(() => {
          navigate("../laporan-pelaksanaan", { replace: true });
        }, 2000);
      }
    },
  });
  const onSubmit = async () => {
    const dataPayload = {
      applicationStatusCode: getValues("applicationStatusCode"),
      roApprovalType: "GRANT_REPORT",
      note: getValues("note"),
      decisionAllowPublic: decisionAllowPublic,
      grantReportId: reportId,
    };
    await updatingApprovalReportGeran(dataPayload);
  };

  if (isUpdatingApprovalReportGeran) {
    return <LoadingOverlay />;
  }
  return (
    <>
      <Box className={classes.section} mt={1} mb={1}>
        <SocietyBanner
          societyName={societyData?.societyName}
          societyNo={societyData?.societyNo}
        />
      </Box>
      <Box mb={2}>
        {sectionItems.map((item, index) => {
          return (
            <AccordionComp
              key={index}
              subTitle={item.subTitle}
              currentIndex={index + 1}
              currentExpand={currentExpandSection}
              readStatus={readStatus}
              onChangeFunc={handleChangeCurrentExpandSection}
            >
              {item.component}
            </AccordionComp>
          );
        })}
      </Box>

      <Box className={classes.section} mt={1} mb={1}>
        <Box className={classes.sectionBox} mt={2}>
          <Typography className="title" mb={4}>
            Keputusan Syor Negeri
          </Typography>
          <Grid container>
            <Grid item xs={12} sm={4}>
              <Typography variant="body1" sx={labelStyle}>
                Syor Negeri
              </Typography>
            </Grid>

            <Grid item xs={12} sm={8} sx={{ mb: 3 }}>
              <SelectFieldController
                control={control}
                name="applicationStatusCode"
                options={decisionOptions}
                disabled={hasJPPMHQRole ? true : false}
                sx={{
                  background: isFormDisabled ? "rgba(218, 218, 218, 0.5)" : "",
                }}
                required
              />
            </Grid>

            <Grid item xs={12} sm={4}>
              <Typography variant="body1" sx={labelStyle}>
                Catatan Keputusan
              </Typography>
            </Grid>
            <Grid item xs={12} sm={8}>
              <TextFieldController
                control={control}
                name="note"
                multiline
                disabled={hasJPPMHQRole ? true : false}
                required={true}
                sxInput={{
                  minHeight: "92px",
                }}
              />
            </Grid>
            <Grid item xs={12} sm={4} sx={{ mt: 3 }}>
              <Typography variant="body1" sx={labelStyle}>
                Kebenaran untuk papar aktiviti di ruangan hebahan
              </Typography>
            </Grid>

            <Grid item xs={12} sm={8} sx={{ mt: 3 }}>
              <SelectFieldController
                control={control}
                name="decisionAllowPublic"
                options={decisionAllowPublicOptions}
                disabled={hasJPPMHQRole ? true : false}
                sx={{
                  background: isFormDisabled ? "rgba(218, 218, 218, 0.5)" : "",
                }}
                required
              />
            </Grid>
          </Grid>
        </Box>
        <ButtonPrimary
          type="submit"
          onClick={handleSubmit(onSubmit)}
          sx={{ marginLeft: "auto", display: "block", mt: 3 }}
        >
          {t("kemaskiniKeputusan")}
        </ButtonPrimary>
      </Box>
    </>
  );
};

export default LaporanPelaksanaanDetail;
