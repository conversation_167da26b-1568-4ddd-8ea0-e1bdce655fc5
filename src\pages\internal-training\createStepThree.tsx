import React, {useEffect, useState} from "react";
import {Box, Grid, IconButton, TextField, Typography} from "@mui/material";
import {useTranslation} from "react-i18next";
import DurationComponent from "@/pages/internal-training/durationComponent";
import {DeleteIcon} from "@/components/icons/delete";
import {TrainingAddIcon} from "@/components/icons/trainingAdd";
import {ButtonOutline, ButtonPrimary, DialogConfirmation} from "@/components";
import QuestionFragment from "@/pages/internal-training/questionFragment";
import {useCustom, useCustomMutation, useNotification} from "@refinedev/core";
import {API_URL} from "@/api";
import {useNavigate} from "react-router-dom";
import {TrainingFormProps} from "./createStepOne";

export interface TrainingAnswer {
  id: string,
  answer: string,
  correct: boolean,
  sequenceOrder: number,
}

export interface TrainingQuestion {
  id: string,
  question: string,
  isUpdated: boolean,
  error: boolean,
  answers: TrainingAnswer[]
}

const CreateStepThree: React.FC<TrainingFormProps> = ({
  headerStyle,
  labelStyle,
  borderStyle,
  handleNext,
  courseId,
  isUpdate,
                                                        allowedToDelete,
}) => {

  const { t, i18n } = useTranslation();
  const navigate = useNavigate();
  const { open: openNotification } = useNotification();
  const [openModal, setOpenModal] = useState(false);
  const [openModalDelete, setOpenModalDelete] = useState(false);
  const [tempId, setTempId] = useState({
    id:"0",
    index:0
  });
  const [totalQuestions, setTotalQuestions] = useState<TrainingQuestion[]>([{
    id: "0",
    question: "",
    isUpdated: false,
    error: false,
    answers: [],
  }])
  const [canChange, setCanChange] = useState(true);

  const validateForm = () => {
    const errors: { [key: string]: string } = {};
    console.log("validateForm");
    //if (!formData.no) errors.no = t("fieldRequired");
    //if (!formData.timeLimit) errors.timeLimit = t("fieldRequired");
    if (!formData.passingScore) errors.passingScore = t("fieldRequired");
    return errors;
  };

  const [formErrors, setFormErrors] = useState<{ [key: string]: string }>({});
  const [formData, setFormData] = useState({
    timeLimit: 0,
    passingScore: 0,
    isUpdated: false,
    error: false,
  });

  const [hour, setHour] = useState<number>(0)
  const [minute, setMinute] = useState<number>(0)

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const name = e.target.name;
    let value = e.target.value;
    if (name === "no" || name === "passingScore") {
      value = value.replace(/^0+/, '');
    }
    setFormData((prevState) => ({
      ...prevState,
      [name]: value,
      isUpdated: true,
    }));
    setFormErrors((prev) => ({ ...prev, [name]: "" }));
  };

  const hourCallback = (e: number) => {
    setHour(e);
  }

  const minuteCallback = (e: number) => {
    setMinute(e);
  }

  useEffect(() => {
    setFormData((prevState) => ({
      ...prevState,
      timeLimit: hour * 60 + minute,
      isUpdated: true,
    }));
  }, [hour, minute]);

  const handleAddChapter = (e: React.MouseEvent<HTMLButtonElement>) => {
    if(!allowedToDelete){
      openNotification?.({
        type: "error",
        message: t("TRAINING_ENROLLED_CANT_ADD"),
        //description: "Failed to fetch feedback answers",
      });
      return
    }
    const temp = {
      id: "0",
      question: "",
      isUpdated: false,
      error: false,
      answers: [],
    }
    setTotalQuestions(prevQuestions => [...prevQuestions, temp]);
    setFormData((prevState) => ({
      ...prevState,
      quizNo: totalQuestions.length,
    }));
  }

  const handleDeleteQuiz = () => {
    setTotalQuestions(prevQuestions => prevQuestions.filter((q) => q.id != tempId.id));
    setFormData((prevState) => ({
      ...prevState,
      quizNo: totalQuestions.length,
    }));
    if(tempId.id!="0") DeleteQuestion(tempId.id);
  }

  const handleSaveDraft = (e: React.MouseEvent<HTMLButtonElement>) => {
    const errors = validateForm();
    if (Object.keys(errors).length > 0) {
      setFormErrors(errors);
      return;
    }
    //console.log(trainingQuiz.id);
    if (!trainingQuiz.id || trainingQuiz?.id === "0") CreateQuiz();
    else if (trainingQuiz?.id!="0" && formData.isUpdated) EditQuiz();
    else if (trainingQuiz?.id!="0" && !formData.isUpdated) {
      /*totalQuestions.forEach((e, i) => {
        if (e.id && e.isUpdated) {
          EditQuestion(trainingQuiz?.id, i)
        } else if (!e.id) {
          CreateQuestion(trainingQuiz?.id, i)
        } else {
          if (i === totalQuestions.length - 1) {
            openNotification?.({
              type: "success",
              message: t("noChanges"),
              //description: "Failed to fetch feedback answers",
            });
          }
        }
      })*/
      UpdateQuizQuestion(trainingQuiz?.id);
    }
    else {
      openNotification?.({
        type: "success",
        message: t("noChanges"),
        //description: "Failed to fetch feedback answers",
      });
    }
  }

  const handleSave = () => {
    const errors = validateForm();
    if (Object.keys(errors).length > 0) {
      setFormErrors(errors);
      return;
    }
    if (formData.error) {
      return;
    }
    if (!trainingQuiz.id || trainingQuiz?.id === "0") CreateQuiz();
    else if (trainingQuiz?.id!="0" && formData.isUpdated) EditQuiz();
    else if (trainingQuiz?.id!="0" && !formData.isUpdated) {
      /*totalQuestions.forEach((e, i) => {
        if (e.id && e.isUpdated) {
          EditQuestion(trainingQuiz?.id, i)
        } else if (!e.id) {
          CreateQuestion(trainingQuiz?.id, i)
        }
      })*/
      UpdateQuizQuestion(trainingQuiz?.id);
    }
    else {
      //navigate("/latihan-internal");
      openNotification?.({
        type: "success",
        message: t("noChanges"),
        //description: "Failed to fetch feedback answers",
      });
    }
  }

  const { mutate: createQuiz, isLoading: isQuizLoadingCreate } = useCustomMutation();
  const CreateQuiz = (): void => {
    const currentDate = new Date();
    const formattedDate = currentDate.toISOString().split("T")[0];
    createQuiz(
      {
        url: `${API_URL}/society/admin/training/quiz`,
        method: "post",
        values: {
          trainingCourseId: courseId,
          title: "E-ROSES Basics Quiz",
          description: "Test your knowledge of E-ROSES basics",
          isMandatory: true,
          minScore: formData.passingScore,
          timeLimitMinutes: formData.timeLimit,
          sequenceOrder: 1,
          quizNo: totalQuestions.length
        },
        config: {
          headers: {
            "Content-Type": "application/json",
            portal: localStorage.getItem("portal") || "",
            authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
          },
        },
        successNotification: (data) => {
          if (data?.data?.data) {
            //handleNext(courseId!,"pelajaran");
            //totalQuestions.forEach((e, i) => CreateQuestion(data?.data?.data, i))
            UpdateQuizQuestion(data?.data?.data);
            return {
              message: t("TRAINING_QUIZ_CREATED"),
              type: "success",
            };
          } else {
            return {
              message: t("error") + data?.data?.msg,
              type: "error",
            };
          }
        },
        errorNotification: (data) => {
          return {
            message: data?.response?.data?.msg,
            type: "error",
          };
        },
      },
      {
        onError(error, variables, context) {
          console.log(error);
        },
      }
    );
  };

  const { mutate: editQuiz, isLoading: isQuizLoadingEdit } = useCustomMutation();
  const EditQuiz = (): void => {
    const currentDate = new Date();
    const formattedDate = currentDate.toISOString().split("T")[0];
    editQuiz(
      {
        url: `${API_URL}/society/admin/training/quiz`,
        method: "put",
        values: {
          trainingCourseId: courseId,
          title: "E-ROSES Basics Quiz",
          description: "Test your knowledge of E-ROSES basics",
          isMandatory: true,
          minScore: formData.passingScore,
          timeLimitMinutes: formData.timeLimit,
          sequenceOrder: 1,
          quizNo: totalQuestions.length
        },
        config: {
          headers: {
            "Content-Type": "application/json",
            portal: localStorage.getItem("portal") || "",
            authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
          },
        },
        successNotification: (data) => {
          if (data?.data?.data) {
            //handleNext(courseId!,"pelajaran");
            UpdateQuizQuestion(trainingQuiz?.id);
            /*totalQuestions.forEach((e, i) => {
              if (e.id != 0 && e.isUpdated) {
                EditQuestion(data?.data?.data, i, true)
              } else if (e.id == 0) {
                CreateQuestion(data?.data?.data, i, true)
              } else {
                //navigate("/latihan-internal");
              }
            })*/
            return {
              message: t("TRAINING_QUIZ_UPDATED"),
              type: "success",
            };
          }
        },
        errorNotification: (data) => {
          return {
            message: data?.response?.data?.msg,
            type: "error",
          };
        },
      },
      {
        onError(error, variables, context) {
          console.log(error);
        },
      }
    );
  };

  const { mutate: deleteQuestion, isLoading: isQuestionLoadingDelete } = useCustomMutation();
  const DeleteQuestion = (id: string): void => {
    deleteQuestion(
      {
        url: `${API_URL}/society/admin/training/quiz/questions/${id}`,
        method: "delete",
        values: {
        },
        config: {
          headers: {
            "Content-Type": "application/json",
            portal: localStorage.getItem("portal") || "",
            authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
          },
        },
        successNotification: (data) => {
          if (data?.data?.data) {
            setOpenModal(false);
            return {
              message: t("TRAINING_QUIZ_QUESTION_DELETED"),
              type: "success",
            };
          }
        },
        errorNotification: (data) => {
          return {
            message: data?.response?.data?.msg,
            type: "error",
          };
        },
      },
      {
        onError(error, variables, context) {
          console.log(error);
        },
      }
    );
  };

  const { mutate: createQuestion, isLoading: isQuestionLoadingCreate } = useCustomMutation();
  const CreateQuestion = (quizId: number, i: number): void => {
    const currentDate = new Date();
    const formattedDate = currentDate.toISOString().split("T")[0];
    const answers = totalQuestions[i].answers.map((e, i) => {
      return {
        optionText: e.answer,
        isCorrect: e.correct,
        sequenceOrder: e.sequenceOrder,
      }
    })
    createQuestion(
      {
        url: `${API_URL}/society/admin/training/quiz/questions`,
        method: "post",
        values: {
          trainingQuizId: quizId,
          questionText: totalQuestions[i].question,
          questionType: "MULTIPLE_CHOICE",
          points: 10,
          sequenceOrder: i + 1,
          options: answers
        },
        config: {
          headers: {
            "Content-Type": "application/json",
            portal: localStorage.getItem("portal") || "",
            authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
          },
        },
        successNotification: (data) => {
          if (data?.data?.data) {
            setOpenModal(false);
            //navigate("/latihan-internal");
            return {
              message: t("TRAINING_QUIZ_QUESTION_CREATED"),
              type: "success",
            };
          }
        },
        errorNotification: (data) => {
          return {
            message: data?.response?.data?.msg,
            type: "error",
          };
        },
      },
      {
        onError(error, variables, context) {
          console.log(error);
        },
      }
    );
  };

  const { mutate: editQuestion, isLoading: isQuestionLoadingEdit } = useCustomMutation();
  const EditQuestion = (quizId: number, i: number): void => {
    const currentDate = new Date();
    const formattedDate = currentDate.toISOString().split("T")[0];
    const answers = totalQuestions[i].answers.map((e, i) => {
      return {
        optionText: e.answer,
        isCorrect: e.correct,
        sequenceOrder: e.sequenceOrder
      }
    })
    editQuestion(
      {
        url: `${API_URL}/society/admin/training/quiz/questions`,
        method: "put",
        values: {
          id: totalQuestions[i].id,
          trainingQuizId: quizId,
          questionText: totalQuestions[i].question,
          questionType: "MULTIPLE_CHOICE",
          points: 10,
          sequenceOrder: i + 1,
          options: answers
        },
        config: {
          headers: {
            "Content-Type": "application/json",
            portal: localStorage.getItem("portal") || "",
            authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
          },
        },
        successNotification: (data) => {
          if (data?.data?.data) {
            setOpenModal(false);
            //navigate("/latihan-internal");
            return {
              message: t("TRAINING_QUIZ_QUESTION_UPDATED"),
              type: "success",
            };
          }
        },
        errorNotification: (data) => {
          return {
            message: data?.response?.data?.msg,
            type: "error",
          };
        },
      },
      {
        onError(error, variables, context) {
          console.log(error);
        },
      }
    );
  };

  const { mutate: updateQuizQuestion, isLoading: isUpdateQuizQuestionLoading } = useCustomMutation();
  const UpdateQuizQuestion = (quizId: number): void => {
    const currentDate = new Date();
    const formattedDate = currentDate.toISOString().split("T")[0];
    /*const answers = totalQuestions[i].answers.map((e, i) => {
      return {
        optionText: e.answer,
        isCorrect: e.correct,
        sequenceOrder: e.sequenceOrder
      }
    })*/
    updateQuizQuestion(
      {
        url: `${API_URL}/society/admin/training/quiz/questions-batch`,
        method: "post",
        values: totalQuestions.map((tc,i) => {
          return {
            id: tc.id,
            trainingQuizId: quizId,
            questionText: tc.question,
            questionType: "MULTIPLE_CHOICE",
            points: 10,
            sequenceOrder: i + 1,
            options: tc.answers.map((a) => {
              return {
                id: a.id,
                optionText: a.answer,
                isCorrect: a.correct,
                sequenceOrder: a.sequenceOrder
              }
            })
          }
        }),
        config: {
          headers: {
            "Content-Type": "application/json",
            portal: localStorage.getItem("portal") || "",
            authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
          },
        },
        successNotification: (data) => {
          if (data?.data?.data) {
            setOpenModal(false);
            mapQuestion(data?.data?.data);
            //navigate("/latihan-internal");
            return {
              message: t("TRAINING_QUIZ_QUESTION_UPDATED"),
              type: "success",
            };
          }
        },
        errorNotification: (data) => {
          return {
            message: data?.response?.data?.msg,
            type: "error",
          };
        },
      },
      {
        onError(error, variables, context) {
          console.log(error);
        },
      }
    );
  };

  const { data: trainingData, isLoading: isTrainingLoading } = useCustom({
    url: `${API_URL}/society/admin/training/courses/${courseId}/quiz`,
    method: "get",
    config: {
      headers: {
        portal: localStorage.getItem("portal"),
        authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
      },
    },
    queryOptions: {
      enabled: courseId != "0",
      retry: false,
      cacheTime: 0,
    },
  });



  const trainingQuiz = trainingData?.data?.data || {};
  const tempQuestions = trainingQuiz?.questions || [];
  //console.log("trainingQuiz", trainingData, isTrainingLoading)

  useEffect(() => {
    if ((isUpdate || courseId) && Object.keys(trainingQuiz).length > 0) {
      const temp = {
        id: trainingQuiz.id ?? "0",
        no: trainingQuiz.quizNo,
        timeLimit: trainingQuiz.timeLimitMinutes,
        passingScore: trainingQuiz.minScore,
        isUpdated: false,
        error: false,
      }
      if (trainingQuiz.timeLimitMinutes > 59) {
        const tempH = Math.floor(trainingQuiz.timeLimitMinutes / 60);
        setHour(tempH)
      }
      setMinute(trainingQuiz.timeLimitMinutes % 60);
      setFormData(temp);
      //const tempQuestions = structuredClone(trainingQuiz.questions);
    }
  }, [trainingData]);

  useEffect(() => {
    if(tempQuestions.length > 0){
      setCanChange(false);
      mapQuestion(trainingQuiz.questions);
    }
  },[tempQuestions])

  const mapQuestion =  (questions: any) => {
    const tempTotalQuestions: TrainingQuestion[] = []
    questions.forEach((q: any) => {
      const tempAnswers: TrainingAnswer[] = [];
      q.options.forEach((a: any) => {
        tempAnswers.push({
          id: a.id,
          answer: a.optionText,
          correct: a.isCorrect,
          sequenceOrder: a.sequenceOrder,
        })
      })
      tempTotalQuestions.push(
        {
          id: q.id,
          question: q.questionText,
          isUpdated: false,
          error: false,
          answers: tempAnswers,
        }
      )
    })
    //console.log("tempTotalQuestions",tempTotalQuestions);
    setTotalQuestions(tempTotalQuestions);
    setCanChange(true);
    //setTimeout(() =>  setTotalQuestion(tempTotalQuestions),5000);
  }

  const handleDataChange = (i: number, data: TrainingQuestion) => {
    if(canChange){
      setTotalQuestions(prevQuestions => prevQuestions.map((q,index) => {
        if(i === index){
          return {
            ...q,
            question: data.question,
            answers: data.answers,
            isUpdated: data.isUpdated,
          }
        }
        return q
      }))
    }
  }

  return (<>
    <Box sx={{ display: "flex" }}>
      <Box sx={{ width: "85%", }}>
        <Box
          sx={{
            borderRadius: 2.5,
            backgroundColor: "#fff",
            //display: "inline",
            px: 2,
            py: 2,
            mb: 1,
          }}
        >
          <Box
            sx={borderStyle}
          >
            <Typography
              sx={headerStyle}
            >
              {t("quizSettings")}
            </Typography>
            <Grid container spacing={2} sx={{ mt: 1 }}>
              <Grid item xs={12} sm={4}>
                <Typography sx={labelStyle}>
                  {t("trainingQuizNo")} <span style={{ color: "red" }}>*</span>
                </Typography>
              </Grid>
              <Grid item xs={12} sm={8}>
                <TextField
                  size={"small"}
                  fullWidth
                  //required
                  name="no"
                  value={totalQuestions.length}
                  //error={!!formErrors.no}
                  helperText={formErrors.no}
                  type={"number"}
                  inputProps={
                    { readOnly: true }
                  }
                  /*onChange={(e: React.ChangeEvent<HTMLInputElement>) => {
                    if (/[1-9]/.test(e.target.value)) {
                      handleInputChange(e)
                    }
                  }}*/
                />
              </Grid>
              <DurationComponent setHour={setHour} setMinute={setMinute}
                labelStyle={labelStyle} hour={hour} minute={minute}/>
              <Grid item xs={12} sm={4}>
                <Typography sx={{...labelStyle,lineHeight:0.5}}>
                  {t("trainingMinScore")} <span style={{ color: "red" }}>*</span>
                </Typography>
              </Grid>
              <Grid item xs={6} sm={1}>
                <TextField
                  size={"small"}
                  fullWidth
                  required
                  type={"number"}
                  name="passingScore"
                  value={formData.passingScore}
                  error={!!formErrors.passingScore}
                  helperText={formErrors.passingScore}
                  onChange={(e: React.ChangeEvent<HTMLInputElement>) => {
                    if (/[1-9]/.test(e.target.value) && parseInt(e.target.value) <= 100) {
                      handleInputChange(e)
                    }
                  }}
                />
              </Grid>
              <Grid item xs={6} sm={1}>
                <Typography sx={labelStyle}>
                  {`/ 100%`}
                </Typography>
              </Grid>
              <Grid item xs={12} sm={6}>

              </Grid>
            </Grid>
          </Box>
        </Box>
        {totalQuestions.map((e: TrainingQuestion, index) => {
          return (<Box
            key={index}
            sx={{
              borderRadius: 2.5,
              backgroundColor: "#fff",
              //display: "inline",
              px: 2,
              py: 2,
              mb: 1,
            }}
          >
            <QuestionFragment no={index + 1} headerStyle={headerStyle} borderStyle={borderStyle}
              labelStyle={labelStyle} handleDataChange={handleDataChange} data={e} />
            {index === totalQuestions.length - 1 ?
              <Grid
                item
                xs={12}
                sx={{
                  mt: 2,
                  display: "flex",
                  flexDirection: "row",
                  justifyContent: "flex-end",
                  gap: 1,
                }}
              >
                <ButtonOutline
                  sx={{
                    bgcolor: "white",
                    "&:hover": { bgcolor: "white" },
                    width: "auto",
                  }}
                  onClick={() => setOpenModal(true)}
                  disabled={isQuestionLoadingCreate
                    || isQuestionLoadingEdit
                    || isQuizLoadingCreate
                    || isQuizLoadingEdit
                    || isUpdateQuizQuestionLoading}
                >
                  {t("save")}
                </ButtonOutline>
                {/*<ButtonPrimary
                  variant="contained"
                  sx={{
                    width: "auto",
                  }}
                  onClick={(e) => setOpenModal(true)}
                  disabled={isQuestionLoadingCreate
                || isQuestionLoadingEdit
                || isQuizLoadingCreate
                || isQuizLoadingEdit
                || isUpdateQuizQuestionLoading}
                >
                  {t("publish")}
                </ButtonPrimary>*/}
              </Grid> : <></>}
          </Box>)
        })}
      </Box>
      <Box sx={{ width: "15%" }}>
        <Box
          sx={{
            borderRadius: 2.5,
            backgroundColor: "#fff",
            //display: "inline",
            px: 2,
            py: 2,
            mb: 1,
            ml: 2,
          }}
        >
          {totalQuestions.map((e, i) => {
            return <Grid container spacing={1} key={i}>
              <Grid item xs={10}>
                <Box
                  sx={borderStyle}
                >
                  <Typography key={i} sx={{
                    fontSize: 14,
                    color: "#666666",
                    fontWeight: "400 !important",
                  }}>
                    {`${t("question")} ${i + 1}`}
                  </Typography>
                </Box></Grid>
              {i > 0 ?
                <Grid item xs={2}>
                  <IconButton
                    sx={{
                      display: "flex",
                      justifyContent: "center",
                      alignContent: "center",
                      mt: 1
                    }}
                    onClick={() => {
                      setTempId({
                        id:e.id,
                        index: i
                      });
                      setOpenModalDelete(true);
                    }}
                  >
                    <DeleteIcon
                      sx={{
                        color: "#FF0000",
                      }}
                    />
                  </IconButton></Grid> : <Grid item xs={2}></Grid>}
            </Grid>
          })}
          <Box>
            <Grid container sx={{ mt: 1 }}>
              <Grid item xs={3}>
                <Box
                  sx={{
                    display: "flex",
                    justifyContent: "center",
                    alignItems: "center",
                    borderRadius: 2.5,
                    backgroundColor: "#0CA6A6",
                    width: 36,
                    height: 36,
                  }}
                >
                  <TrainingAddIcon sx={{ color: "#FFF", }} />
                </Box>
              </Grid>
              <Grid item xs={9}>
                <ButtonOutline
                  sx={{
                    bgcolor: "white",
                    "&:hover": { bgcolor: "white" },
                    width: "75%",
                    minWidth: "75%"
                  }}
                  onClick={handleAddChapter}
                >
                  {t("addQuestion")}
                </ButtonOutline>
              </Grid>
            </Grid>
          </Box>
        </Box>
      </Box>
    </Box>
      <DialogConfirmation
        open={openModalDelete}
        onClose={() => {
          setOpenModalDelete(false);
        }}
        onAction={() => {
          if(!allowedToDelete){
            openNotification?.({
              type: "error",
              message: t("TRAINING_ENROLLED_CANT_DELETE"),
              //description: "Failed to fetch feedback answers",
            });
            return
          }
          handleDeleteQuiz()
        }}
        isMutating={false}
        onConfirmationText={t("CONFIRM_DELETE_TRAINING")}
      />
    <DialogConfirmation
      open={openModal}
      onClose={() => {
        setOpenModal(false);
      }}
      onAction={handleSave}
      isMutating={false}
      onConfirmationText={t("CONFIRM_CREATE_TRAINING")}
    />
  </>
  );
}

export default CreateStepThree;
