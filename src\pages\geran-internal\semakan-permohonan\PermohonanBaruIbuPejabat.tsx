import {
  formatDate,
  getStateNameById,
  globalStyles,
  stateGeran,
  statusOptionsGeran,
  useQuery,
  yearGeran,
} from "@/helpers";
import React, { useState } from "react";
import { FieldValues, useForm } from "react-hook-form";
import { useTranslation } from "react-i18next";

import FilterBar from "@/components/filter";
import {
  Box,
  IconButton,
  InputAdornment,
  TextField,
  Typography,
} from "@mui/material";

import { API_URL } from "@/api";
import CustomDataGridGeran from "@/components/datagrid/CustomDataGridGeran";
import { EditIcon, SearchIcon } from "@/components/icons";
import { GridColDef, GridRowSelectionModel } from "@mui/x-data-grid";
import { useNavigate } from "react-router-dom";

export interface GrantApplication {
  id: string | number;
  grantTemplateId: number;
  grantTemplateTitle: string | null;
  societyId: number;
  societyName: string | null;
  status: string;
  submissionDate: string | null;
  createdDate: string;
  createdBy: number;
  modifiedDate: string;
  modifiedBy: number;
}
interface PermohonanBaruIbuPejabatTabProps {
  onSelectIdsChange: (ids: string[] | number[]) => void;
  meetingFilter?: { num: number; year: number; label: string } | null;
}
const PermohonanBaruIbuPejabatTab: React.FC<
  PermohonanBaruIbuPejabatTabProps
> = ({ onSelectIdsChange, meetingFilter }) => {
  const navigate = useNavigate();
  const { t } = useTranslation();
  const classes = globalStyles();
  const { setValue, watch } = useForm<FieldValues>({
    defaultValues: {
      page: 1,
      pageSize: 10,
      status: undefined,
    },
  });
  const [refetchData, setRefetchData] = useState(false);

  const page = watch("page") - 1;
  const pageSize = watch("pageSize");
  const columnsNew: GridColDef[] = [
    // {
    //   field: "no",
    //   headerName: "No",
    //   width: 35,
    //   sortable: false,
    //   filterable: false,
    //   align: "center",
    //   headerAlign: "center",
    //   disableColumnMenu: true,
    //   renderCell: (params) =>
    //     page * pageSize +
    //     params.api.getRowIndexRelativeToVisibleRows(params.id) +
    //     1,
    // },
    {
      field: "societyName",
      headerName: "Nama Pertubuhan",
      flex: 1,
      sortable: false,
      filterable: false,
      disableColumnMenu: true,
      renderCell: ({ row }) => {
        return (
          <Typography
            sx={{
              color: "#666666",
              fontWeight: "400",
              fontSize: 12,
              cursor: "pointer",
            }}
          >
            {row?.societyName}
          </Typography>
        );
      },
    },
    {
      field: "societyNo",
      headerName: "Nombor Pertubuhan",
      flex: 1,
      filterable: false,
      sortable: false,
      disableColumnMenu: true,
      renderCell: ({ row }) => {
        return (
          <Typography
            sx={{
              color: "#666666",
              fontWeight: "400",
              fontSize: 12,
              cursor: "pointer",
            }}
          >
            {row?.societyNo}
          </Typography>
        );
      },
    },
    {
      field: "submissionDate",
      headerName: "Tarikh Hantar",
      filterable: false,
      sortable: false,
      disableColumnMenu: true,
      flex: 1,
      renderCell: ({ row }) => {
        const formattedDate = formatDate(row?.submissionDate);
        return formattedDate;
      },
    },
    {
      field: "status",
      headerName: "Status",
      flex: 1,
      filterable: false,
      disableColumnMenu: true,
      align: "center",
      renderCell: ({ row }) => {
        let color = "#9747FF";
        let borderColor = "#9747FF";
        let text = row.status;

        if (row.status === "APPROVED") {
          color = "#0F9D58";
          borderColor = "#0F9D58";
          text = "Lulus";
        } else if (row.status === "REJECTED") {
          color = "#D93025";
          borderColor = "#D93025";
          text = "Tidak Berjaya";
        } else if (row.status === "PENDING_STATE") {
          color = "#F4B400";
          borderColor = "#F4B400";
          text = "Menunggu Keputusan";
        } else if (row.status === "PENDING_HQ") {
          color = "#F4B400";
          borderColor = "#F4B400";
          text = "Menunggu Keputusan";
        }

        return (
          <Typography
            sx={{
              border: `1px solid ${borderColor}`,
              color: color,
              alignItems: "center",
              width: "130px",
              height: "29px",
              margin: "0 !important",
              borderRadius: "15px",
              fontSize: "10px",
              fontWeight: 500,
              fontFamily: "Poppins, sans-serif",
              lineHeight: "29px",
              textAlign: "center",
              backgroundColor: "#fff",
            }}
          >
            {text}
          </Typography>
        );
      },
    },
    {
      field: "state",
      headerName: "Negeri",
      flex: 1,
      disableColumnMenu: true,
      filterable: false,
      renderCell: ({ row }) => {
        return getStateNameById(row?.stateId);
      },
    },
    {
      field: "action",
      headerName: "Tindakan",
      flex: 1,
      disableColumnMenu: true,
      filterable: false,
      renderCell: ({ row }) => {
        return (
          <IconButton
            sx={{
              padding: 0,
            }}
            onClick={() => navigate(`./${row?.id}/${row?.societyId}`)}
          >
            <EditIcon />
          </IconButton>
        );
      },
    },
  ];

  const filterOptions = {
    status: statusOptionsGeran,
    state: stateGeran,
    date: yearGeran,
  };

  const [selectedFilters, setSelectedFilters] = useState<
    Record<string, string>
  >({
    status: "status",
    state: "negeri",
    date: "Tarikh Hantar",
  });
  const handleSelectedFiltersChange = (
    updatedFilters: Record<string, string>
  ) => {
    setSelectedFilters(updatedFilters);
  };

  const onFilterChange = (field: string, value: number | string) => {
    setValue("page", 1);
    setValue(field, value);
  };
  return (
    <Box className={classes.section}>
      <Box className={classes.sectionBox}>
        <Typography className="title" mb={8}>
          Permohonan Baru
        </Typography>

        <TextField
          fullWidth
          variant="outlined"
          placeholder={t("state")}
          sx={{
            display: "block",
            boxSizing: "border-box",
            width: "90%",
            height: "40px",
            marginInline: "auto",
            marginTop: "12px",
            background: "rgba(132, 132, 132, 0.3)",
            opacity: 0.5,
            border: "1px solid rgba(102, 102, 102, 0.8)",
            borderRadius: "10px",
            "& .MuiOutlinedInput-root": {
              height: "40px",
              "& fieldset": {
                border: "none",
              },
            },
          }}
          InputProps={{
            startAdornment: (
              <InputAdornment position="start">
                <SearchIcon sx={{ color: "#9CA3AF", marginLeft: "8px" }} />
              </InputAdornment>
            ),
          }}
        />

        <FilterBar
          filterOptions={filterOptions}
          onFilterChange={onFilterChange}
          selectedFilters={selectedFilters}
          onSelectedFiltersChange={handleSelectedFiltersChange}
        />

        <CustomDataGridGeran
          url={`${API_URL}/grant/application`}
          columns={columnsNew}
          type={2}
          noResultMessage={t("noData")}
          filters={[
            { field: "status", value: "PENDING_HQ", operator: "eq" },
            { field: "hasMeeting", value: true, operator: "eq" },
            {
              field: "meetingNumber",
              value: meetingFilter?.num || 0,
              operator: "eq",
            },
            {
              field: "meetingYear",
              value: meetingFilter?.year || 0,
              operator: "eq",
            },
          ]}
          checkboxSelection
          onSelectionChange={(selectedIds: GridRowSelectionModel) => {
            if (selectedIds.length === 0) {
              onSelectIdsChange([]);
              return;
            }
            if (typeof selectedIds[0] === "string") {
              onSelectIdsChange(selectedIds as string[]);
            } else {
              onSelectIdsChange(selectedIds as number[]);
            }
          }}
          isFiltered
          disableMultipleRowSelection
          setRefetchData={setRefetchData}
          refetchData={refetchData}
        />
      </Box>
    </Box>
  );
};

export default PermohonanBaruIbuPejabatTab;
