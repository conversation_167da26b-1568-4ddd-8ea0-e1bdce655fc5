import React, {useState} from "react";
import InternalHebahanHeader from "@/pages/hebahan/hebahanHeader";
import {Box, IconButton, Typography} from "@mui/material";
import CustomDataGrid from "@/components/datagrid";
import {useTranslation} from "react-i18next";
import {GridColDef} from "@mui/x-data-grid";
import {formatDate} from "@/helpers";
import {headerStyle} from "@/pages/hebahan/hebahanConstant";
import { API_URL } from "@/api";
import {EditIcon, EyeIcon} from "@/components/icons";
import {useNavigate} from "react-router-dom";


const ReportedArticleList: React.FC = () => {
  const { t, i18n } = useTranslation();
  const navigate = useNavigate();
  const [filter, setFilter] = useState("all");

  const columns: GridColDef[] = [
    {
      field: "no",
      headerName: "No",
      flex: 1,
      renderCell: ({row}) => {
        return <Typography
          sx={{
            color: "#666666",
            fontWeight: "400",
            fontSize: 14,
            //cursor: "pointer"
          }}
          //onClick={() => navigate("sijil/detail", {state:{enrollId:row?.trainingEnrollmentId, courseId:row?.trainingId}})}
        >
          {row?.no}
        </Typography>;
      },
    },
    {
      field: "postingTitle",
      headerName: t("articleTitle"),
      flex: 3,
      renderCell: ({row}) => {
        return row?.postingTitle;
      },
    },
    {
      field: "reportReason",
      headerName: t("articleReportReason"),
      flex: 1,
      renderCell: ({row}) => {
        return row?.reportReason;
      },
    },
    {
      field: "reportDate",
      headerName: t("articleReportDate"),
      flex: 1,
      renderCell: ({row}) => {
        return row?.reportDate;
      },
    },
    {
      field: "actionDate",
      headerName: t("actionDate"),
      flex: 1,
      renderCell: ({row}) => {
        return "-";
      },
    },
    {
      field: "action",
      headerName: t("action"),
      flex: 1,
      renderCell: ({row}) => {
        return (
          <Box
            sx={{
              display: "flex",
              justifyContent: "center",
              alignItems: "center",
              columnGap: "0.5rem",
            }}
          >
            <IconButton
              style={{
                minHeight: "2rem",
                minWidth: "2rem",
              }}
              //color="error"
              onClick={() => navigate(`report/${row.id}/${row.postingId}`)}
            >
              <EyeIcon />
            </IconButton>
          </Box>)
      },
    },
  ]

  return (<>
    <InternalHebahanHeader/>
    <Box
      sx={{
        borderRadius: 2.5,
        backgroundColor: "#fff",
        //display: "inline",
        px: 2,
        py: 2,
        mb: 1,
      }}
    >
      <Box
        sx={{
          borderRadius: 2.5,
          backgroundColor: "#fff",
          border: "1px solid #D9D9D9",
          //flex: 5,
          //display: "inline",
          px: 2,
          py: 2,
          mb: 1,
        }}
      >
        <Typography sx={headerStyle}>
          {'Senarai Artikel Dilaporkan'}
        </Typography>
        <CustomDataGrid
          url={`${API_URL}/society/admin/posting/reports`}
          columns={columns}
          noResultMessage={t("noData")}
          isFiltered
          type={2}
          //filters={[{ field: "status", operator: "eq", value: filter }]}
          //setRefetchData={setRefetchData}
          //refetchData={refetchData}
        />
      </Box>
    </Box>
  </>)
}

export default ReportedArticleList
