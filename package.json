{"name": "e-roses", "version": "0.1.0", "private": true, "type": "module", "dependencies": {"@aws-sdk/client-geo-places": "^3.744.0", "@aws-sdk/client-location": "^3.752.0", "@aws/amazon-location-for-maplibre-gl-geocoder": "^2.0.1", "@aws/amazon-location-utilities-auth-helper": "^1.2.1", "@dnd-kit/core": "^6.3.1", "@dnd-kit/sortable": "^10.0.0", "@dnd-kit/utilities": "^3.2.2", "@elevenlabs/elevenlabs-js": "^2.1.0", "@emotion/react": "^11.13.3", "@emotion/styled": "^11.13.0", "@fontsource/epilogue": "^5.1.1", "@hookform/resolvers": "^3.10.0", "@mui/icons-material": "^5.16.7", "@mui/lab": "^5.0.0-alpha.173", "@mui/material": "^5.16.7", "@mui/styles": "^6.1.5", "@mui/x-charts": "^7.18.0", "@mui/x-data-grid": "^6.20.4", "@mui/x-date-pickers": "^7.18.0", "@piotr-cz/redux-persist-idb-storage": "^1.1.3", "@reduxjs/toolkit": "^2.3.0", "@refinedev/cli": "^2.16.38", "@refinedev/core": "^4.54.1", "@refinedev/devtools": "^1.2.8", "@refinedev/kbar": "^1.3.12", "@refinedev/mui": "^5.21.0", "@refinedev/react-hook-form": "^4.9.0", "@refinedev/react-router-v6": "^4.6.0", "@refinedev/simple-rest": "^5.0.8", "@sentry/react": "^9.43.0", "@types/leaflet": "^1.9.14", "@types/react-redux": "^7.1.34", "@types/recharts": "^1.8.29", "@vis.gl/react-maplibre": "^8.0.1", "amazon-quicksight-embedding-sdk": "^2.10.1", "axios": "^1.7.7", "calendar-link": "^2.8.0", "date-fns": "3", "dayjs": "^1.11.13", "detect-browser": "^5.3.0", "diff": "^7.0.0", "dotenv": "^16.5.0", "dotenv-safe": "^9.1.0", "esbuild": "^0.25.8", "formik": "^2.4.6", "global": "^4.4.0", "html-react-parser": "^5.2.2", "htmlparser2": "^10.0.0", "i18next": "^23.15.1", "jspdf": "^3.0.1", "jspdf-autotable": "^3.8.4", "leaflet": "^1.9.4", "leaflet-geosearch": "^4.1.0", "libphonenumber-js": "^1.12.6", "lodash": "^4.17.21", "lodash.isequal": "^4.5.0", "mailchecker": "^6.0.16", "maplibre-gl": "^5.1.1", "qrcode.react": "^4.2.0", "quill": "^2.0.3", "react": "^18.3.1", "react-currency-input-field": "^3.10.0", "react-dom": "^18.3.1", "react-ga4": "^2.1.0", "react-hook-form": "^7.53.0", "react-i18next": "^15.0.2", "react-leaflet": "^4.2.1", "react-player": "^3.3.2", "react-redux": "^9.1.2", "react-router-dom": "^6.26.2", "react-to-print": "^3.0.2", "react-transition-group": "^4.4.5", "react-type-animation": "^3.2.0", "recharts": "^2.13.0", "reduxjs-toolkit-persist": "^7.2.1", "slate": "^0.118.1", "slate-dom": "^0.118.1", "slate-react": "^0.117.4", "vite-plugin-svgr": "^4.3.0", "yup": "^1.6.1"}, "devDependencies": {"@types/diff": "^5.2.2", "@types/dotenv-safe": "^8.1.6", "@types/eslint": "~9.6.1", "@types/jspdf": "^1.3.3", "@types/lodash.isequal": "^4.5.8", "@types/node": "^18.19.50", "@types/react": "^18.3.8", "@types/react-beautiful-dnd": "^13.1.8", "@types/react-dom": "^18.3.0", "@types/react-transition-group": "^4.4.11", "@typescript-eslint/eslint-plugin": "^5.62.0", "@typescript-eslint/parser": "^5.62.0", "@vitejs/plugin-react": "^4.3.1", "eslint": "^8.57.1", "eslint-plugin-react-hooks": "^4.6.2", "eslint-plugin-react-refresh": "^0.3.5", "react-beautiful-dnd": "^13.1.1", "sass-embedded": "^1.86.1", "terser": "^5.39.0", "typescript": "^5.6.2", "vite": "^6.2.0"}, "overrides": {"@aws/amazon-location-for-maplibre-gl-geocoder": {"@maplibre/maplibre-gl-geocoder": "^1.8.0"}}, "scripts": {"dev": "refine dev", "build": "tsc && refine build", "start": "refine start", "refine": "refine"}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "refine": {"projectId": "ZuW0GG-wwIwwZ-na1s6s"}}