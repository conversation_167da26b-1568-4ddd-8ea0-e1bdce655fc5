import { DocumentUploadType, globalStyles, useQuery } from "@/helpers";
import {
  Controller,
  FieldValues,
  FormProvider,
  useForm,
} from "react-hook-form";
import { useTranslation } from "react-i18next";

import {
  Box,
  Checkbox,
  FormControlLabel,
  FormGroup,
  Grid,
  Typography,
} from "@mui/material";
import MaklumatPertubuhanDetail from "@/pages/geran-external/senarai-permohonan/Form/MaklumatPertubuhanDetail";
import { useParams } from "react-router-dom";
import { LoadingOverlay } from "@/components/loading";
import {
  DatePickerForm,
  FileUploadController,
  FileUploader,
  FormFieldRow,
  Label,
  SelectFieldController,
  TextFieldController,
  ToggleButtonController,
} from "@/components";
import { useEffect, useState } from "react";
import TransaksiTable from "../../laporan-permohonan/TransaksiTable";
import dayjs from "dayjs";

interface GrantField {
  id: string | number;
  fieldName: string;
  fieldType: string;
  isRequired: boolean;
  sequenceOrder: number;
  sectionName: string;
  pageNumber: number;
  options?: string[] | FormOption[] | TableOption[];
  grantTemplateId?: number;
  fieldValue?: any;
}
type FormOption = {
  key: string;
  value: string;
};
type TableOption = {
  key: string;
  value: string;
  row?: number;
  col?: number;
};
const BorangPermohonanSection: React.FC = () => {
  const { t } = useTranslation();
  const classes = globalStyles();
  const methods = useForm<FieldValues>({
    defaultValues: {
      approvalStatus: "",
      rejectReason: "",
      note: "",
      meetingNumber: "",
      noteQuery: "",
    },
  });
  const {
    control,
    handleSubmit,
    getValues,
    setValue,
    watch,
    formState: { isValid },
  } = methods;

  const { applicationGeranId } = useParams<{ applicationGeranId: string }>();
  const { societyId } = useParams<{ societyId: string }>();
  const {
    data: grantApplicationDetailDataResponse,
    isLoading: isLoadingGrantTemplate,
  } = useQuery({ url: `grant/application/${applicationGeranId}` });

  const grantTemplateData: GrantField[] =
    grantApplicationDetailDataResponse?.data?.data?.fieldValues ?? [];

  useEffect(() => {
    grantTemplateData.forEach((field) => {
      if (field.fieldType === "TABLE") {
        try {
          const tableValues = JSON.parse(field.fieldValue || "[]");
          tableValues.forEach((row: any, rowIndex: number) => {
            Object.entries(row).forEach(([colName, colValue]) => {
              setValue(`${field.fieldName}.${rowIndex}.${colName}`, colValue);
            });
          });
        } catch {}
      } else if (field.fieldType === "CHECKBOX") {
        let initialValue: string[] = [];

        try {
          if (Array.isArray(field.fieldValue)) {
            initialValue = field.fieldValue;
          } else if (
            typeof field.fieldValue === "string" &&
            field.fieldValue.trim() !== ""
          ) {
            initialValue = [field.fieldValue];
          }
        } catch {
          initialValue = [];
        }

        setValue(field.fieldName, initialValue);
      } else {
        setValue(field.fieldName, field.fieldValue ?? "");
      }
    });
  }, [grantTemplateData, setValue]);

  const { data: societyListDataResponse, isLoading: isLoadingSociety } =
    useQuery({
      url: `society/${societyId}`,
    });
  const societyData = societyListDataResponse?.data?.data ?? {};

  const getValidationRules = (field: GrantField) => {
    return {
      required: field.isRequired ? "Medan ini wajib diisi." : false,
    };
  };
  useEffect(() => {
    grantTemplateData.forEach((field) => {
      if (field.fieldType === "TABLE") {
        try {
          const tableValues = JSON.parse(field.fieldValue || "[]");
          tableValues.forEach((row: any, rowIndex: number) => {
            Object.entries(row).forEach(([colName, colValue]) => {
              setValue(`${field.fieldName}.${rowIndex}.${colName}`, colValue);
            });
          });
        } catch {}
      } else if (field.fieldType === "CHECKBOX") {
        let initialValue: string[] = [];

        try {
          if (Array.isArray(field.fieldValue)) {
            initialValue = field.fieldValue;
          } else if (
            typeof field.fieldValue === "string" &&
            field.fieldValue.trim() !== ""
          ) {
            initialValue = [field.fieldValue];
          }
        } catch {
          initialValue = [];
        }

        setValue(field.fieldName, initialValue);
      } else if (field.fieldType === "TARIKH") {
        setValue(
          field.fieldName,
          field.fieldValue ? dayjs(field.fieldValue) : null
        );
      } else {
        setValue(field.fieldName, field.fieldValue ?? "");
      }
    });
  }, [grantTemplateData, setValue]);
  const renderFieldComponent = (field: GrantField, index: number) => {
    const label = <Label text={field.fieldName} required={field.isRequired} />;

    switch (field.fieldType) {
      case "TEXT":
        return (
          <FormFieldRow
            key={index}
            label={label}
            value={
              <TextFieldController
                control={control}
                name={field.fieldName}
                placeholder={field.fieldName}
                disabled
                rules={getValidationRules(field)}
              />
            }
          />
        );

      case "DROPDOWN":
        const rawOptions = Array.isArray(field?.options) ? field.options : [];

        const optionDropdown =
          typeof rawOptions[0] === "string"
            ? (rawOptions as string[])
                .map((itemString) => {
                  try {
                    const item = JSON.parse(itemString);
                    return {
                      value: item.value,
                      label: item.key,
                    };
                  } catch (err) {
                    console.warn("Gagal parse option:", itemString);
                    return null;
                  }
                })
                .filter(
                  (item): item is { value: string | number; label: string } =>
                    item !== null
                )
            : rawOptions.map((item: any) => ({
                value: item.key,
                label: item.value,
              }));

        return (
          <FormFieldRow
            key={index}
            label={label}
            value={
              <SelectFieldController
                control={control}
                name={field.fieldName}
                options={optionDropdown}
                placeholder={field.fieldName}
                rules={getValidationRules(field)}
                disabled
              />
            }
          />
        );

      case "CHECKBOX":
        const checkboxOptions: FormOption[] =
          Array.isArray(field?.options) && field.options.length > 0
            ? (field.options as FormOption[])
            : [];
        const parsedFieldValue = field.fieldValue
          ? JSON.parse(field.fieldValue)
          : {};

        return (
          <FormGroup>
            {checkboxOptions.map((opt) => (
              <FormControlLabel
                key={opt.key}
                control={
                  <Controller
                    name={`${field.fieldName}.${opt.key}`}
                    control={control}
                    defaultValue={parsedFieldValue?.[opt.key] ?? false}
                    render={({ field }) => (
                      <Checkbox
                        {...field}
                        checked={field.value}
                        onChange={(e) => field.onChange(e.target.checked)}
                        disabled
                      />
                    )}
                  />
                }
                label={<Label text={opt?.value} />}
              />
            ))}
          </FormGroup>
        );

      case "UPLOAD":
        return (
          <FormFieldRow
            key={index}
            label={label}
            value={
              <FileUploader
                type={DocumentUploadType.GERAN}
                grantTemplateFieldId={field.id}
                societyId={societyId}
                code={"GERAN_APPLICATION"}
                disabled={false}
                validTypes={[
                  "application/pdf",
                  "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
                  "application/msword",
                  "text/plain",
                ]}
              />
            }
          />
        );
      case "TARIKH":
        return (
          <FormFieldRow
            key={index}
            label={label}
            value={
              <DatePickerForm
                control={control}
                name={field.fieldName}
                disabled
              />
            }
          />
        );
      case "CONFIRM":
        let descriptionConfirm = "";
        let confirmationText = field.fieldName;

        if (Array.isArray(field.options) && field.options.length > 0) {
          if (typeof field.options[0] !== "string") {
            const opts = field.options as FormOption[];
            descriptionConfirm =
              opts.find((o) => o.key === "description")?.value || "";
            confirmationText =
              opts.find((o) => o.key === "confirmationText")?.value ||
              field.fieldName;
          }
        }

        return (
          <Controller
            key={index}
            name={field.fieldName || "akuan"}
            control={control}
            rules={{
              required: field.isRequired ? "Sila tandakan akuan ini." : false,
            }}
            render={({ field: rhfField }) => (
              <>
                <Typography className="label" mb={3}>
                  {descriptionConfirm}
                </Typography>

                <Box
                  sx={{ display: "flex", alignItems: "center", gap: "20px" }}
                >
                  <Checkbox
                    {...rhfField}
                    checked={rhfField.value || false}
                    disabled
                    icon={
                      <Box
                        sx={{
                          width: "12px",
                          height: "12px",
                          border: "0.5px solid #848484",
                          background: "none",
                        }}
                      />
                    }
                    checkedIcon={
                      <Box
                        sx={{
                          width: "12px",
                          height: "12px",
                          border: "0.5px solid var(--primary-color)",
                          background: "var(--primary-color)",
                        }}
                      />
                    }
                    sx={{ padding: 0 }}
                  />
                  <Label text={confirmationText} required={field.isRequired} />
                </Box>
              </>
            )}
          />
        );

      case "TRANSAKSI":
        let parsedTable: (string | number)[][] = [];

        try {
          parsedTable = JSON.parse(field.fieldValue || "[]");
        } catch (e) {
          console.warn("Failed to parse table fieldValue:", e);
        }

        return <TransaksiTable value={parsedTable} />;
      case "TABLE":
        const optionTable: TableOption[] = (field.options ?? []).map((opt) => {
          if (typeof opt === "string") {
            try {
              const parsed = JSON.parse(opt);
              return parsed as TableOption;
            } catch {
              return { key: "", value: "" };
            }
          } else {
            return opt as TableOption;
          }
        });

        const rowCounts = Number(
          optionTable.find((o: any) => o.key === "count-row")?.value || 0
        );
        const colCounts = Number(
          optionTable.find((o: any) => o.key === "count-col")?.value || 0
        );

        const tables: string[][] = Array.from({ length: rowCounts }, () =>
          Array.from({ length: colCounts }, () => "")
        );

        optionTable.forEach((opt: any) => {
          if (opt.key === "count-row" || opt.key === "count-col") return;
          tables[opt.row][opt.col] = opt.value;
        });

        return (
          <Grid container spacing={1} p={1.5}>
            {tables.map((row, rowIndex) => (
              <Grid
                container
                spacing={1}
                key={rowIndex}
                mt={rowIndex > 0 ? 1 : 0}
              >
                {row.map((cellValue, colIndex) => (
                  <Grid item xs={12} md={12 / colCounts} key={colIndex}>
                    {rowIndex === 0 ? (
                      <Typography
                        variant="subtitle2"
                        align="center"
                        sx={{ fontWeight: "bold" }}
                      >
                        {cellValue || `Kolom ${colIndex + 1}`}
                      </Typography>
                    ) : (
                      <TextFieldController
                        control={control}
                        name={`${field.fieldName}.${rowIndex}.${colIndex}`}
                        placeholder="Isi disini"
                        defaultValue={cellValue}
                        disabled
                        fullWidth
                        size="small"
                      />
                    )}
                  </Grid>
                ))}
              </Grid>
            ))}
          </Grid>
        );

      case "MAKLUMAT":
        return (
          <FormFieldRow
            key={index}
            label={<Label text={field.fieldName} required={field.isRequired} />}
            value={
              <TextFieldController
                control={control}
                name={field.fieldName}
                placeholder={field.fieldName || "Tulis Maklumat di sini"}
                fullWidth
                disabled
                multiline
                size="small"
              />
            }
          />
        );

      case "CATATAN":
        const optionMaklumat: FormOption[] =
          Array.isArray(field.options) && typeof field.options[0] !== "string"
            ? (field.options as FormOption[])
            : [];

        const description =
          optionMaklumat.find((o) => o.key === "description")?.value || "";

        return (
          <Grid item xs={12}>
            <Label text={description} />
          </Grid>
        );
    }
  };
  return (
    <>
      {/* Section Maklumat Pertubuhan */}
      <Box className={classes.section} mb={2}>
        {isLoadingSociety ? (
          <LoadingOverlay />
        ) : (
          <MaklumatPertubuhanDetail society={societyData} />
        )}
      </Box>

      {/* Section Template */}
      {isLoadingGrantTemplate ? (
        <LoadingOverlay />
      ) : (
        <Box className={classes.section} mb={2}>
          {Object.entries(
            grantTemplateData
              .sort((a, b) => {
                if (a.pageNumber !== b.pageNumber) {
                  return a.pageNumber - b.pageNumber;
                }

                return a.sequenceOrder - b.sequenceOrder;
              })
              .reduce<Record<string, GrantField[]>>((acc, field) => {
                const key = `${field.pageNumber}`;
                if (!acc[key]) acc[key] = [];
                acc[key].push(field);
                return acc;
              }, {})
          ).map(([groupKey, fields], i) => {
            let sectionTitle =
              fields.find((f) => f.sequenceOrder === 1)?.sectionName || "";

            if (fields.some((f) => f.fieldType === "CATATAN")) {
              sectionTitle = "";
            }
            return (
              <Box
                key={groupKey}
                mb={3}
                p={2}
                border="1px solid #ccc"
                borderRadius={2}
              >
                <Typography
                  className="title"
                  mb={2}
                >{`${sectionTitle}`}</Typography>
                {fields.map((field, index) =>
                  renderFieldComponent(field, index)
                )}
              </Box>
            );
          })}
        </Box>
      )}
    </>
  );
};

export default BorangPermohonanSection;
