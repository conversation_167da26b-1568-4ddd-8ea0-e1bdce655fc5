import { useState, useRef, useEffect } from "react";
import { t } from "i18next";
import {
  <PERSON><PERSON><PERSON>,
  <PERSON>rud<PERSON>ilter,
  useCustomMutation,
  useNotification,
} from "@refinedev/core";
import { API_URL } from "@/api";
import {
  DocumentUploadType,
  MimeTypeMap,
  useQuery,
  useUploadPresignedUrl,
  DOCUMENT_MAX_FILE_SIZE,
} from "@/helpers";

import {
  Box,
  Typography,
  IconButton,
  LinearProgress,
  Button,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  SxProps,
  Theme,
} from "@mui/material";
import CustomSkeleton from "../custom-skeleton";

import { FileAddIcon, TrashIcon } from "../icons";
import { GridCloseIcon } from "@mui/x-data-grid";
import CustomPopover from "../popover";

export interface DocumentGetParamResponseBodyGet {
  url: string;
  name: string;
}

interface FileUploaderProps<
  GetDocs extends DocumentGetParamResponseBodyGet = DocumentGetParamResponseBodyGet
> {
  type: DocumentUploadType;
  societyId?: string | number;
  branchId?: string | number | null;
  meetingId?: string | number | null;
  liquidationId?: string | number;
  appealId?: string | number | null;
  societyNo?: string | number | null;
  title?: string;
  info?: string;
  validTypes: string[];
  accept?:  string;
  label?: string;
  inputStyle?: boolean;
  hasDialog?: boolean;
  dialogOpenBtn?: string;
  dialogTitle?: string;
  dialogConent?: string;
  sxContainer?: SxProps<Theme> | undefined;
  maxFileSize?: number;
  societyCommitteeId?: string | number;
  societyNonCitizenCommitteeId?: string | number;
  branchCommitteeId?: string | number;
  disabled?: boolean;
  icNo?: any;
  code?: string;
  statementId?: string | number;
  grantTemplateId?: string | number;
  grantApplicationId?: string | number;
  grantTemplateFieldId? : string | number;
  grantReportId?: string | number;
  required?: boolean;
  uploadAfterSubmit?: boolean;
  uploadAfterSubmitIndicator?: any;
  feedbackId?: string | number;
  withAuthHeaders?: boolean;
  ajkAppointmentLetterDate?: string;
  onUploadComplete?: (id: any) => void;
  hasOldFiles?: (id: string) => void;
  onDeleteFile?: (id: any) => void;
  trainingId?: string | number;
  trainingMaterialId?: string | number;
  postingMediaId?: string | number;
  onChange?: (files: any[]) => void;

  /**
   * @default null
   */
  onUploadedFilesChanged?: null | ((files: any[]) => void);

  /**
   * @default true
   */
  showSuccessUploadNotification?: boolean;

  /**
   * @default true
   */
  showSuccessDeleteNotification?: boolean;

  /**
   * @default null
   */
  onUploadAfterSubmitSuccessfully?: null | (() => void | Promise<void>);
  resetFilesSignal?: number;

  onLoadComplete?: ((files: GetDocs[]) => void) | null;
}

export interface UploadingFile {
  name: string;
  url: string;
  progress: number;
  size: string;
  isComplete?: boolean;
  id?: string | number;
  societyCommitteeId?: string | number;
  societyNonCitizenCommitteeId?: string | number;
  branchCommitteeId?: string | number;
  icNo?: any;
}

export const FileUploader = <
  GetDocs extends DocumentGetParamResponseBodyGet = DocumentGetParamResponseBodyGet,
  PropType extends FileUploaderProps<GetDocs> = FileUploaderProps<GetDocs>
>({
  type,
  uploadAfterSubmit,
  uploadAfterSubmitIndicator,
  withAuthHeaders = true,
  societyId,
  branchId,
  liquidationId,
  appealId,
  societyNo,
  meetingId,
  statementId,
  validTypes,
  accept = ".pdf,.docx,.txt,.png,.jpg,.jpeg",
  title,
  info,
  label,
  inputStyle,
  hasDialog,
  dialogOpenBtn,
  dialogTitle,
  dialogConent,
  sxContainer,
  maxFileSize = DOCUMENT_MAX_FILE_SIZE,
  societyCommitteeId,
  societyNonCitizenCommitteeId,
  branchCommitteeId,
  disabled,
  icNo,
  code,
  required,
  feedbackId,
  ajkAppointmentLetterDate,
  onUploadComplete,
  hasOldFiles,
  onDeleteFile,
  trainingId,
  trainingMaterialId,
  postingMediaId,
  grantTemplateId,
  grantTemplateFieldId,
  grantApplicationId,
  grantReportId,
  onUploadedFilesChanged = null,
  showSuccessUploadNotification = true,
  showSuccessDeleteNotification = true,
  onUploadAfterSubmitSuccessfully = null,
  resetFilesSignal,
  onLoadComplete = null,
  onChange,
  ...otherParams
}: PropType) => {
  const {
    data,
    refetch,
    isLoading: isLoadingDocument,
  } = useQuery({
    url: "document/documentByParam",
    onSuccess: (data) => {
      onLoadComplete?.(data.data?.data ?? ([] as GetDocs[]));
    },
    filters: [
      {
        field: "type",
        operator: "eq",
        value: type,
      },
      ...(trainingId
        ? [
            {
              field: "trainingId",
              operator: "eq",
              value: trainingId,
            } satisfies CrudFilter,
          ]
        : []),
        ...(trainingMaterialId
        ? [
            {
              field: "trainingMaterialId",
              operator: "eq",
              value: trainingMaterialId,
            } satisfies CrudFilter,
          ]
        : []),
      ...(postingMediaId
        ? [
            {
              field: "postingMediaId",
              operator: "eq",
              value: postingMediaId,
            } satisfies CrudFilter,
          ]
        : []),
      ...(societyId
        ? [
            {
              field: "societyId",
              operator: "eq",
              value: societyId,
            } satisfies CrudFilter,
          ]
        : []),
      ...(meetingId
        ? [
            {
              field: "meetingId",
              operator: "eq",
              value: meetingId,
            } satisfies CrudFilter,
          ]
        : []),
      ...(appealId
        ? [
            {
              field: "appealId",
              operator: "eq",
              value: appealId,
            } satisfies CrudFilter,
          ]
        : []),
      ...(branchId
        ? [
            {
              field: "branchId",
              operator: "eq",
              value: branchId,
            } satisfies CrudFilter,
          ]
        : []),
      ...(societyCommitteeId
        ? [
            {
              field: "societyCommitteeId",
              operator: "eq",
              value: societyCommitteeId,
            } satisfies CrudFilter,
          ]
        : []),
      ...(societyNonCitizenCommitteeId
        ? [
            {
              field: "societyNonCitizenCommitteeId",
              operator: "eq",
              value: societyNonCitizenCommitteeId,
            } satisfies CrudFilter,
          ]
        : []),
      ...(branchCommitteeId
        ? [
            {
              field: "branchCommitteeId",
              operator: "eq",
              value: branchCommitteeId,
            } satisfies CrudFilter,
          ]
        : []),
      ...(icNo
        ? [
            {
              field: "icNo",
              operator: "eq",
              value: icNo,
            } satisfies CrudFilter,
          ]
        : []),
      ...(statementId
        ? [
            {
              field: "statementId",
              operator: "eq",
              value: statementId,
            } satisfies CrudFilter,
          ]
        : []),
      ...(grantTemplateId
        ? [
            {
              field: "grantTemplateId",
              operator: "eq",
              value: grantTemplateId,
            } satisfies CrudFilter,
          ]
        : []),
        ...(grantTemplateFieldId
        ? [
            {
              field: "grantTemplateFieldId",
              operator: "eq",
              value: grantTemplateFieldId,
            } satisfies CrudFilter,
          ]
        : []),
        ...(grantApplicationId
        ? [
            {
              field: "grantApplicationId",
              operator: "eq",
              value: grantApplicationId,
            } satisfies CrudFilter,
          ]
        : []),
        ...(grantReportId
        ? [
            {
              field: "grantReportId",
              operator: "eq",
              value: grantReportId,
            } satisfies CrudFilter,
          ]
        : []),
      ...(feedbackId
        ? [
            {
              field: "feedbackId",
              operator: "eq",
              value: feedbackId,
            } satisfies CrudFilter,
          ]
        : []),
      ...(ajkAppointmentLetterDate
        ? [
            {
              field: "ajkAppointmentLetterDate",
              operator: "eq",
              value: ajkAppointmentLetterDate,
            } satisfies CrudFilter,
          ]
        : []),
      ...(liquidationId
        ? [
            {
              field: "liquidationId",
              operator: "eq",
              value: liquidationId,
            } satisfies CrudFilter,
          ]
        : []),
      ...(code
        ? [
            {
              field: "code",
              operator: "eq",
              value: code,
            } satisfies CrudFilter,
          ]
        : []),
    ],
    autoFetch: false,
    withAuthHeaders,
  });

  const { open } = useNotification();
  const [isCleared, setIsCleared] = useState(false);
  const [clearedFiles, setClearedFiles] = useState<any[]>([]);

  useEffect(() => {
    if (societyId || (postingMediaId && postingMediaId!="0")) {
      refetch();
    }
  }, [societyId,postingMediaId]);



  useEffect(() => {
    if (resetFilesSignal) {
      const allUploaded = uploadedFiles;

      if (allUploaded.length > 0) {
        setIsCleared(true);
        setClearedFiles(allUploaded);
        setUploadedFiles([]);
      }
    }
  }, [resetFilesSignal]);

  const [openDialog, setOpenDialog] = useState(false);
  const handleOpenDialog = () => setOpenDialog(true);
  const handleCloseDialog = () => setOpenDialog(false);

  const [uploadedFiles, setUploadedFiles] = useState<any[]>([]);
  const [uploadingFiles, setUploadingFiles] = useState<UploadingFile[]>([]);
  const [fileHolder, setFileHolder] = useState<File[]>([]);
  const fileInputRef = useRef<HTMLInputElement>(null);

  useEffect(() => {
    const uploaded = data?.data?.data ?? [];
    if (!isCleared) {
      setUploadedFiles(uploaded);
      onUploadedFilesChanged?.(uploaded);
    }

    if (uploaded?.length > 0) {
      hasOldFiles?.(uploaded);
    }
  }, [data?.data?.data]);

  const params = {
    type: type,
    societyId: societyId,
    societyNo: societyNo,
    meetingId: meetingId,
    feedbackId: feedbackId,
    statementId: statementId,
    grantTemplateId: grantTemplateId,
    grantTemplateFieldId : grantTemplateFieldId,
    grantApplicationId: grantApplicationId,
    grantReportId: grantReportId,
    ajkAppointmentLetterDate: ajkAppointmentLetterDate,
    appealId: appealId,
    branchId: branchId,
    liquidationId: liquidationId,
    societyCommitteeId: societyCommitteeId,
    societyNonCitizenCommitteeId: societyNonCitizenCommitteeId,
    branchCommitteeId: branchCommitteeId,
    icNo: icNo,
    code: code,
    trainingId: trainingId,
    trainingMaterialId: trainingMaterialId,
    postingMediaId: postingMediaId,
  };

  const { upload, progress, returnId, returnURL } = useUploadPresignedUrl({
    withAuthHeaders,
    showSuccessNotification: showSuccessUploadNotification,
  });

  useEffect(() => {
    if (returnId && returnURL) {
      setUploadingFiles((prev) =>
        prev.map((f, index) =>
          index === prev.length - 1 ? { ...f, id: returnId, url: returnURL } : f
        )
      );
      onUploadComplete?.(returnId);
      setUploadingFiles([]);
      refetch();
    }
  }, [returnId, returnURL]);

  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return "0 Bytes";
    if (bytes > 25 * 1024 * 1024) return `25 MB`;

    const k = 1024;
    const sizes = ["Bytes", "KB", "MB"];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i];
  };

  const handlePreviewDocument = (fileUrl: string) => {
    if (fileUrl !== "") {
      window.open(fileUrl, "_blank");
    }
  };

  const handleFiles = async (files: FileList | null) => {
    if (!files) return;

    const newFiles = Array.from(files);
    onChange?.(newFiles);

    if (isCleared && clearedFiles.length > 0) {
      await Promise.all(
        clearedFiles.map(async (file) => {
          if (file?.id) await handleDeleteFile(file.id);
        })
      );
      setClearedFiles([]);
      setIsCleared(false);
    }

    Array.from(files).forEach((file) => {
      if (!file) return;

      if (!validTypes.includes(file.type)) {
        alert(
          `Invalid file type. Please upload a ${readableTypes.join(", ")} file.`
        );
        return;
      }

      if (file.size > DOCUMENT_MAX_FILE_SIZE) {
        // alert(
        //   `File size exceeds limit of ${formatFileSize(
        //     DOCUMENT_MAX_FILE_SIZE
        //   )}.`
        // );
        open?.({
          type: "error",
          message: t("inputValidationErrorFileSizeExceedsLimitDynamic", {
            limit: formatFileSize(maxFileSize),
          }),
        });
        return;
      }

      setUploadingFiles((prev) => [
        ...prev,
        {
          name: file.name,
          progress: 0,
          size: formatFileSize(file.size),
          url: "",
        },
      ]);

      if (uploadAfterSubmit) {
        setFileHolder((prev) => {
          const newValue = [...prev, file];
          onUploadedFilesChanged?.(newValue);
          return newValue;
        });
      } else {
        upload({ params, file });
      }
    });
  };

  const handleRemoveFromList = (selectedFile: any) => {
    if (uploadingFiles?.length > 0) {
      setUploadingFiles((prev) =>
        prev.filter((file) => file.name !== selectedFile.name)
      );
    }

    if (uploadedFiles) {
      setUploadedFiles((prevFiles) => {
        const updatedFiles = prevFiles?.filter(
          (file) => file.name !== selectedFile.name
        );

        return updatedFiles;
      });
    }

    if (fileHolder) {
      setFileHolder((prev) =>
        prev.filter((file) => file.name !== selectedFile.name)
      );
    }

    if (societyId) {
      refetch();
    }

    if (uploadAfterSubmit) {
      onChange?.(fileHolder.filter((file) => file.name !== selectedFile.name));
    } else {
      onChange?.(
        uploadedFiles.filter((file) => file.name !== selectedFile.name)
      );
    }
  };

  const handleUploadAfterSubmit = async () => {
    try {
      await Promise.all(
        fileHolder.map(async (file) => await upload({ params, file }))
      );
    } finally {
      onUploadAfterSubmitSuccessfully?.();
    }
  };

  // biome-ignore lint/correctness/useExhaustiveDependencies: only check value change of `uploadAfterSubmitIndicator`
  useEffect(() => {
    if (uploadAfterSubmit && uploadAfterSubmitIndicator) {
      handleUploadAfterSubmit();
    }
  }, [uploadAfterSubmitIndicator]);

  const handleFileUpload = async (e: React.ChangeEvent<HTMLInputElement>) => {
    e.preventDefault();
    e.stopPropagation();
    await handleFiles(e.target.files);
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    handleFiles(e.dataTransfer.files);
  };

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
  };

  const { mutate: deleteFile } = useCustomMutation();

  const DeleteFile = (id: string | number | undefined | BaseKey) => {
    if (!id) return;

    deleteFile(
      {
        url: `${API_URL}/document/deleteDocument?id=${id}`,
        method: "put",
        values: {},
        config: {
          headers: {
            "Content-Type": "application/json",
            portal: localStorage.getItem("portal") || "",
            authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
          },
        },
        successNotification: (data) => {
          setUploadingFiles((prev) => prev.filter((file) => file.id !== id));

          if (uploadedFiles) {
            setUploadedFiles((prevFiles) => {
              const updatedFiles = prevFiles?.filter((file) => file.id !== id);
              //@ts-expect-error
              hasOldFiles?.(updatedFiles);
              return updatedFiles;
            });
          }

          refetch();

          onDeleteFile?.(id); // Ensure this gets called

          return showSuccessDeleteNotification
            ? {
                message: data?.data?.msg,
                type: "success",
              }
            : undefined;
        },
        errorNotification: (data) => {
          return {
            message: data?.response?.data?.msg,
            type: "error",
          };
        },
      },
      {
        onError(error) {
          console.log(error);
        },
      }
    );
  };

  function handleDeleteFile(id: string | number | undefined | BaseKey) {
    DeleteFile(id);
    onChange?.(uploadedFiles.filter((file) => file.id !== id));
  }

  const readableTypes = validTypes.map(
    (type) => MimeTypeMap[type] || "Unknown"
  );

  const cleanString = (str: string): string => {
    // eslint-disable-next-line no-control-regex
    return str.replace(/[\u0008]/g, " ");
  };

  if (inputStyle) {
    return <Box>in progress</Box>;
  } else if (disabled) {
    return (
      <Box>
        {title ? (
          <Box sx={{ display: "flex", gap: 1, alignItems: "center" }}>
            <Typography color="primary">
              {t(`${title}`)}
              {required ? (
                <span style={{ color: "var(--error)" }}> *</span>
              ) : null}
            </Typography>
            {info && (
              <CustomPopover
                customStyles={{
                  maxWidth: "210px",
                  backgroundColor: "white",
                  mt: 1,
                }}
                content={
                  <Typography
                    sx={{
                      color: "#FF0000",
                      fontSize: "12px",
                      whiteSpace: "break-spaces",
                    }}
                  >
                    {t(info)}
                  </Typography>
                }
              />
            )}
          </Box>
        ) : null}

        {uploadedFiles?.length === 0 ? (
          <Box
            sx={{
              display: "flex",
              justifyContent: "center",
              alignItems: "top",
              py: 1,
            }}
          >
            <Typography className="label">
              {t("noAvailableDocuments")}
            </Typography>
          </Box>
        ) : (
          <Box
            sx={{
              textAlign: "left",
              mt: 2,
              maxHeight: 450,
              overflowY: "auto",
            }}
          >
            {isLoadingDocument ? (
              <CustomSkeleton />
            ) : (
              uploadedFiles?.length &&
              uploadedFiles?.map((file, index) => (
                <Box key={`uploaded-files-${index}`}>
                  <Box
                    key={file.id}
                    sx={{
                      border: "1px solid #E0E0E0",
                      borderRadius: "8px",
                      backgroundColor: "#fff",
                      p: 2,
                      mb: 1,
                    }}
                  >
                    <Box
                      sx={{
                        display: "flex",
                        justifyContent: "space-between",
                        mb: 1,
                      }}
                    >
                      <Box>
                        <Typography
                          sx={{
                            cursor: "pointer",
                            "&:hover": {
                              color: "var(--primary-color)",
                              textDecoration: "underline",
                            },
                          }}
                          onClick={() => handlePreviewDocument(file.url)}
                        >
                          {file.name}
                        </Typography>
                        <Typography
                          sx={{
                            color: "var(--primary-color)",
                            fontSize: "0.875rem",
                            mt: 0.5,
                          }}
                        >
                          {t("uploadComplete")}
                        </Typography>
                      </Box>
                      <Box
                        sx={{
                          display: "flex",
                          alignItems: "center",
                          gap: 1,
                        }}
                      >
                        <Box
                          sx={{
                            display: "flex",
                            alignItems: "center",
                            color: "#4CAF50",
                            p: 1,
                            borderRadius: 1,
                          }}
                        >
                          <svg
                            width="24"
                            height="24"
                            viewBox="0 0 24 24"
                            fill="none"
                            xmlns="http://www.w3.org/2000/svg"
                          >
                            <path
                              d="M23 12L20.56 9.21L20.9 5.52L17.29 4.7L15.4 1.5L12 2.96L8.6 1.5L6.71 4.69L3.1 5.5L3.44 9.2L1 12L3.44 14.79L3.1 18.49L6.71 19.31L8.6 22.5L12 21.03L15.4 22.49L17.29 19.3L20.9 18.48L20.56 14.79L23 12ZM9.38 16.01L7 13.61C6.9073 13.5175 6.83375 13.4076 6.78357 13.2866C6.73339 13.1657 6.70756 13.036 6.70756 12.905C6.70756 12.774 6.73339 12.6443 6.78357 12.5234C6.83375 12.4024 6.9073 12.2925 7 12.2L7.07 12.13C7.46 11.74 8.1 11.74 8.49 12.13L10.1 13.75L15.25 8.59C15.64 8.2 16.28 8.2 16.67 8.59L16.74 8.66C17.13 9.05 17.13 9.68 16.74 10.07L10.82 16.01C10.41 16.4 9.78 16.4 9.38 16.01Z"
                              fill="var(--primary-color)"
                            />
                          </svg>
                        </Box>
                        {!disabled && (
                          <IconButton
                            onClick={() => handleDeleteFile(file.id)}
                            sx={{
                              color: "#FF0000",
                              p: 1,
                            }}
                          >
                            <TrashIcon sx={{ width: 20, height: 20 }} />
                          </IconButton>
                        )}
                      </Box>
                    </Box>
                  </Box>
                </Box>
              ))
            )}
          </Box>
        )}
      </Box>
    );
  } else {
    return (
      <Box
        sx={{
          p: 3,
          borderRadius: "10px",
          border: "0.5px solid #dfdfdf",
          mt: 1,
          ...sxContainer,
        }}
      >
        <Box
          sx={{
            display: "flex",
            flexDirection: "row",
            justifyContent: "space-between",
            alignItems: "center",
            mb: 2,
            gap: 2,
          }}
        >
          {title ? (
            <Box sx={{ display: "flex", gap: 1, alignItems: "center" }}>
              <Typography color="primary">
                {t(`${title}`)}
                {required ? (
                  <span style={{ color: "var(--error)" }}> *</span>
                ) : null}
              </Typography>
              {info && (
                <CustomPopover
                  customStyles={{
                    maxWidth: "210px",
                    backgroundColor: "white",
                    mt: 1,
                  }}
                  content={
                    <Typography
                      sx={{
                        color: "#FF0000",
                        fontSize: "12px",
                        whiteSpace: "break-spaces",
                      }}
                    >
                      {t(info)}
                    </Typography>
                  }
                />
              )}
            </Box>
          ) : null}

          {hasDialog && (
            <Box sx={{ display: "flex", gap: 1 }}>
              <Button
                variant="contained"
                startIcon={
                  <svg
                    width="20"
                    height="20"
                    viewBox="0 0 12 12"
                    fill="none"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      fillRule="evenodd"
                      clipRule="evenodd"
                      d="M1.02002 1.02197C0.166016 1.87714 0.166016 3.25147 0.166016 6.0013C0.166016 8.75113 0.166016 10.1261 1.02002 10.9801C1.87518 11.8346 3.24952 11.8346 5.99935 11.8346C8.74918 11.8346 10.1241 11.8346 10.9781 10.9801C11.8327 10.1266 11.8327 8.75113 11.8327 6.0013C11.8327 3.25147 11.8327 1.87655 10.9781 1.02197C10.1247 0.167969 8.74918 0.167969 5.99935 0.167969C3.24952 0.167969 1.8746 0.167969 1.02002 1.02197ZM5.15002 3.38622C5.18962 3.34458 5.22064 3.29556 5.24129 3.24194C5.26195 3.18831 5.27184 3.13115 5.27041 3.0737C5.26897 3.01626 5.25624 2.95966 5.23293 2.90714C5.20962 2.85462 5.17619 2.8072 5.13456 2.76759C5.09292 2.72799 5.04389 2.69697 4.99027 2.67632C4.93665 2.65566 4.87949 2.64577 4.82204 2.6472C4.7646 2.64864 4.708 2.66137 4.65548 2.68468C4.60295 2.70799 4.55554 2.74142 4.51593 2.78305L3.1661 4.20055L2.8161 3.83305C2.77692 3.78976 2.72948 3.75474 2.67656 3.73007C2.62364 3.7054 2.56632 3.69157 2.50797 3.6894C2.44962 3.68723 2.39143 3.69676 2.33683 3.71743C2.28222 3.73809 2.2323 3.76948 2.19002 3.80975C2.14773 3.85001 2.11393 3.89832 2.09061 3.95185C2.06729 4.00538 2.05492 4.06304 2.05423 4.12142C2.05354 4.1798 2.06454 4.23773 2.08658 4.2918C2.10863 4.34586 2.14128 4.39497 2.1826 4.43622L2.84935 5.13622C2.89021 5.1791 2.93935 5.21325 2.9938 5.23657C3.04825 5.2599 3.10686 5.27193 3.1661 5.27193C3.22533 5.27193 3.28395 5.2599 3.3384 5.23657C3.39285 5.21325 3.44199 5.1791 3.48285 5.13622L5.15002 3.38622ZM6.58268 3.8138C6.46665 3.8138 6.35537 3.8599 6.27332 3.94194C6.19128 4.02399 6.14518 4.13527 6.14518 4.2513C6.14518 4.36733 6.19128 4.47861 6.27332 4.56066C6.35537 4.64271 6.46665 4.6888 6.58268 4.6888H9.49935C9.61538 4.6888 9.72666 4.64271 9.80871 4.56066C9.89076 4.47861 9.93685 4.36733 9.93685 4.2513C9.93685 4.13527 9.89076 4.02399 9.80871 3.94194C9.72666 3.8599 9.61538 3.8138 9.49935 3.8138H6.58268ZM5.14943 7.46955C5.19075 7.4283 5.2234 7.3792 5.24545 7.32513C5.26749 7.27107 5.2785 7.21314 5.2778 7.15475C5.27711 7.09637 5.26474 7.03871 5.24142 6.98519C5.2181 6.93166 5.1843 6.88334 5.14202 6.84308C5.09973 6.80282 5.04981 6.77143 4.99521 6.75076C4.9406 6.73009 4.88241 6.72056 4.82406 6.72273C4.76571 6.72491 4.70839 6.73873 4.65547 6.76341C4.60255 6.78808 4.55511 6.82309 4.51593 6.86638L3.1661 8.28389L2.8161 7.91639C2.77692 7.87309 2.72948 7.83808 2.67656 7.81341C2.62364 7.78873 2.56632 7.77491 2.50797 7.77273C2.44962 7.77056 2.39143 7.78009 2.33683 7.80076C2.28222 7.82143 2.2323 7.85282 2.19002 7.89308C2.14773 7.93334 2.11393 7.98166 2.09061 8.03519C2.06729 8.08871 2.05492 8.14637 2.05423 8.20475C2.05354 8.26313 2.06454 8.32107 2.08658 8.37513C2.10863 8.4292 2.14128 8.4783 2.1826 8.51955L2.84935 9.21955C2.89021 9.26244 2.93935 9.29658 2.9938 9.31991C3.04825 9.34324 3.10686 9.35526 3.1661 9.35526C3.22533 9.35526 3.28395 9.34324 3.3384 9.31991C3.39285 9.29658 3.44199 9.26244 3.48285 9.21955L5.14943 7.46955ZM6.58268 7.89714C6.46665 7.89714 6.35537 7.94323 6.27332 8.02528C6.19128 8.10732 6.14518 8.2186 6.14518 8.33463C6.14518 8.45067 6.19128 8.56195 6.27332 8.64399C6.35537 8.72604 6.46665 8.77213 6.58268 8.77213H9.49935C9.61538 8.77213 9.72666 8.72604 9.80871 8.64399C9.89076 8.56195 9.93685 8.45067 9.93685 8.33463C9.93685 8.2186 9.89076 8.10732 9.80871 8.02528C9.72666 7.94323 9.61538 7.89714 9.49935 7.89714H6.58268Z"
                      fill="white"
                    />
                  </svg>
                }
                onClick={handleOpenDialog}
                sx={{
                  backgroundColor: "var(--primary-color)",
                  textTransform: "none",
                  "&:hover": {
                    backgroundColor: "var(--primary-color)",
                  },
                  minWidth: "50px",
                  width: "40px",
                  height: "40px",
                  padding: 0,
                  "& .MuiButton-startIcon": {
                    margin: 0,
                    position: "absolute",
                    left: "50%",
                    top: "50%",
                    transform: "translate(-50%, -50%)",
                  },
                }}
              ></Button>
              <Button
                variant="outlined"
                sx={{
                  borderColor: "var(--primary-color)",
                  textTransform: "none",
                  "&:hover": {
                    borderColor: "#1976D2",
                    backgroundColor: "transparent",
                  },
                }}
              >
                {t(`${dialogOpenBtn}`)}
              </Button>
            </Box>
          )}
        </Box>

        <Box
          sx={{
            border: "2px dashed #e0e0e0",
            borderRadius: 1,
            p: 4,
            mb: 2,
            textAlign: "center",
            cursor: disabled ? "not-allowed" : "pointer",
            backgroundColor: disabled ? "#f5f5f5" : "transparent",
            "&:hover": {
              backgroundColor: disabled ? "#f5f5f5" : "#f0f0f0",
            },
            opacity: disabled ? 0.6 : 1,
          }}
          onClick={!disabled ? () => fileInputRef.current?.click() : undefined}
          onDragOver={!disabled ? handleDragOver : undefined}
          onDrop={!disabled ? handleDrop : undefined}
        >
          <input
            type="file"
            ref={fileInputRef}
            style={{ display: "none" }}
            onChange={handleFileUpload}
            multiple
            accept={accept}
            disabled={disabled ? disabled : false}
          />
          <FileAddIcon
            color="var(--primary-color)"
            sx={{ marginBottom: "8px", width: "28px", height: "29px" }}
          />
          <Typography
            sx={{
              fontSize: "16px",
              fontWeight: 400,
              lineHeight: "18.23px",
              textAlign: "center",
              textUnderlinePosition: "from-font",
              textDecorationSkipInk: "none",
              color: "#222222",
              marginBottom: "15px",
            }}
          >
            {t("clickToUpload")}
          </Typography>
          <Typography
            variant="caption"
            sx={{
              color: "var(--primary-color)",
              display: "flex",
              gap: 1,
              justifyContent: "center",
            }}
          >
            {readableTypes.map((type, index) => (
              <Box
                key={`${type}-${index}`}
                component="span"
                sx={{ bgcolor: "#3276E81A", px: 1, py: 0.5, borderRadius: 1 }}
              >
                {type.toUpperCase()}
              </Box>
            ))}
            <Box
              component="span"
              sx={{ bgcolor: "#E3F2FD", px: 1, py: 0.5, borderRadius: 1 }}
            >
              &lt;{formatFileSize(maxFileSize)}
            </Box>
          </Typography>
        </Box>

        <Box
          sx={{ textAlign: "left", mt: 2, maxHeight: 450, overflowY: "scroll" }}
        >
          {/* Show files being uploaded */}
          {uploadingFiles.map((file, index) => (
            <Box
              key={index}
              sx={{
                border: "1px solid #E0E0E0",
                borderRadius: "8px",
                backgroundColor: "#fff",
                p: 2,
                mb: 1,
              }}
            >
              <Box
                sx={{
                  display: "flex",
                  justifyContent: "space-between",
                  mb: 1,
                }}
              >
                <Box>
                  <Typography
                    sx={{
                      cursor: "pointer",
                      "&:hover": {
                        color: "var(--primary-color)",
                        textDecoration: "underline",
                      },
                    }}
                    onClick={() =>
                      progress === 100 && handlePreviewDocument(file.url)
                    }
                  >
                    {file.name}
                  </Typography>
                  {progress === 100 && (
                    <Typography
                      sx={{
                        color: "var(--primary-color)",
                        fontSize: "0.875rem",
                        mt: 0.5,
                      }}
                    >
                      {t("uploadComplete")}
                    </Typography>
                  )}
                </Box>
                <Box sx={{ display: "flex", alignItems: "start", gap: 1 }}>
                  {progress === 100 ? (
                    <Box
                      sx={{
                        display: "flex",
                        alignItems: "center",
                        color: "#4CAF50",
                        borderRadius: 1,
                        gap: 2,
                      }}
                    >
                      <svg
                        width="24"
                        height="24"
                        viewBox="0 0 24 24"
                        fill="none"
                        xmlns="http://www.w3.org/2000/svg"
                      >
                        <path
                          d="M23 12L20.56 9.21L20.9 5.52L17.29 4.7L15.4 1.5L12 2.96L8.6 1.5L6.71 4.69L3.1 5.5L3.44 9.2L1 12L3.44 14.79L3.1 18.49L6.71 19.31L8.6 22.5L12 21.03L15.4 22.49L17.29 19.3L20.9 18.48L20.56 14.79L23 12ZM9.38 16.01L7 13.61C6.9073 13.5175 6.83375 13.4076 6.78357 13.2866C6.73339 13.1657 6.70756 13.036 6.70756 12.905C6.70756 12.774 6.73339 12.6443 6.78357 12.5234C6.83375 12.4024 6.9073 12.2925 7 12.2L7.07 12.13C7.46 11.74 8.1 11.74 8.49 12.13L10.1 13.75L15.25 8.59C15.64 8.2 16.28 8.2 16.67 8.59L16.74 8.66C17.13 9.05 17.13 9.68 16.74 10.07L10.82 16.01C10.41 16.4 9.78 16.4 9.38 16.01Z"
                          fill="var(--primary-color)"
                        />
                      </svg>
                      {file.id && !disabled ? (
                        <IconButton
                          onClick={() => handleDeleteFile(file.id)}
                          sx={{
                            color: "#FF0000",
                            p: 1,
                          }}
                        >
                          <TrashIcon sx={{ width: 20, height: 20 }} />
                        </IconButton>
                      ) : null}
                    </Box>
                  ) : (
                    <></>
                  )}
                </Box>
              </Box>
              {progress !== 100 && (
                <Box
                  sx={{
                    display: "flex",
                    justifyContent: "space-between",
                    alignItems: "center",
                    gap: 2,
                  }}
                >
                  <Typography
                    variant="body2"
                    sx={{ color: "var(--primary-color)", minWidth: "180px" }}
                  >
                    {uploadAfterSubmit ? null : `${progress}% • `}
                    {t("uploading")} • {file.size}
                  </Typography>

                  <Box
                    sx={{
                      display: "flex",
                      alignItems: "center",
                      justifyContent: "flex-end",
                      gap: 2,
                      width: "300px",
                    }}
                  >
                    {uploadAfterSubmit ? null : (
                      <LinearProgress
                        variant="determinate"
                        value={progress}
                        sx={{
                          flex: 1,
                          height: 6,
                          borderRadius: 3,
                          backgroundColor: "#E0E0E0",
                          "& .MuiLinearProgress-bar": {
                            backgroundColor: "#00BCD4",
                            borderRadius: 3,
                          },
                        }}
                      />
                    )}

                    <IconButton
                      size="small"
                      // sx={{
                      //   display: "flex",
                      //   justifyContent: "center",
                      //   alignContent: "center",
                      // }}
                      onClick={() => handleRemoveFromList(file)}
                    >
                      <GridCloseIcon sx={{ color: "var(--text-grey)" }} />
                      {/* <CancelIcon sx={{ color: "red" }} /> */}
                    </IconButton>
                  </Box>
                </Box>
              )}
            </Box>
          ))}
          {/* Show uploaded files */}
          {isLoadingDocument ? (
            <CustomSkeleton />
          ) : (
            <Box>
              {uploadedFiles?.length ? uploadedFiles?.map((file, index) => (
                <Box
                  key={file.id}
                  sx={{
                    border: "1px solid #E0E0E0",
                    borderRadius: "8px",
                    backgroundColor: "#fff",
                    p: 2,
                    mb: 1,
                  }}
                >
                  <Box
                    sx={{
                      display: "flex",
                      justifyContent: "space-between",
                      mb: 1,
                    }}
                  >
                    <Box>
                      <Typography
                        sx={{
                          cursor: "pointer",
                          "&:hover": {
                            color: "var(--primary-color)",
                            textDecoration: "underline",
                          },
                        }}
                        onClick={() => handlePreviewDocument(file.url)}
                      >
                        {file.name}
                      </Typography>
                      <Typography
                        sx={{
                          color: "var(--primary-color)",
                          fontSize: "0.875rem",
                          mt: 0.5,
                        }}
                      >
                        {t("uploadComplete")}
                      </Typography>
                    </Box>
                    <Box
                      sx={{
                        display: "flex",
                        alignItems: "center",
                        gap: 1,
                      }}
                    >
                      <Box
                        sx={{
                          display: "flex",
                          alignItems: "center",
                          color: "#4CAF50",
                          p: 1,
                          borderRadius: 1,
                        }}
                      >
                        <svg
                          width="24"
                          height="24"
                          viewBox="0 0 24 24"
                          fill="none"
                          xmlns="http://www.w3.org/2000/svg"
                        >
                          <path
                            d="M23 12L20.56 9.21L20.9 5.52L17.29 4.7L15.4 1.5L12 2.96L8.6 1.5L6.71 4.69L3.1 5.5L3.44 9.2L1 12L3.44 14.79L3.1 18.49L6.71 19.31L8.6 22.5L12 21.03L15.4 22.49L17.29 19.3L20.9 18.48L20.56 14.79L23 12ZM9.38 16.01L7 13.61C6.9073 13.5175 6.83375 13.4076 6.78357 13.2866C6.73339 13.1657 6.70756 13.036 6.70756 12.905C6.70756 12.774 6.73339 12.6443 6.78357 12.5234C6.83375 12.4024 6.9073 12.2925 7 12.2L7.07 12.13C7.46 11.74 8.1 11.74 8.49 12.13L10.1 13.75L15.25 8.59C15.64 8.2 16.28 8.2 16.67 8.59L16.74 8.66C17.13 9.05 17.13 9.68 16.74 10.07L10.82 16.01C10.41 16.4 9.78 16.4 9.38 16.01Z"
                            fill="var(--primary-color)"
                          />
                        </svg>
                      </Box>
                      {!disabled && (
                        <IconButton
                          onClick={() => handleDeleteFile(file.id)}
                          sx={{
                            color: "#FF0000",
                            p: 1,
                          }}
                        >
                          <TrashIcon sx={{ width: 20, height: 20 }} />
                        </IconButton>
                      )}
                    </Box>
                  </Box>
                </Box>
              )) : null
            }
            </Box>
          )}
        </Box>

        <Dialog
          open={openDialog}
          onClose={handleCloseDialog}
          maxWidth="xl"
          fullWidth
        >
          <Box sx={{ p: 3 }}>
            <Box
              sx={{
                border: "1px solid #E0E0E0",
                borderRadius: "8px",
                backgroundColor: "#fff",
              }}
            >
              <DialogTitle
                sx={{
                  fontFamily: "Poppins",
                  fontSize: "16px",
                  fontWeight: 600,
                  color: "var(--primary-color)",
                  textAlign: "left",
                }}
              >
                {t(`${dialogTitle}`)}
              </DialogTitle>
              <DialogContent>
                <Box sx={{ py: 2, px: 4 }}>
                  <Typography
                    sx={{
                      mb: 2,
                      fontFamily: "Poppins",
                      fontSize: "14px",
                      whiteSpace: "pre-wrap",
                      "& > span": {
                        display: "block",
                        paddingLeft: "20px",
                      },
                    }}
                  >
                    {cleanString(t(`${dialogConent}`))}
                  </Typography>
                </Box>
              </DialogContent>
            </Box>
            <DialogActions
              sx={{
                p: 3,
                display: "flex",
                justifyContent: "center",
              }}
            >
              <Button
                onClick={handleCloseDialog}
                variant="contained"
                sx={{
                  backgroundColor: "var(--primary-color)",
                  "&:hover": {
                    backgroundColor: "var(--primary-color)",
                  },
                  textTransform: "none",
                  fontFamily: "Poppins",
                  fontSize: "14px",
                  minWidth: "120px",
                  color: "#fff",
                }}
              >
                {t("back")}
              </Button>
            </DialogActions>
          </Box>
        </Dialog>
      </Box>
    );
  }
};

export default FileUploader;
