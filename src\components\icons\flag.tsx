import React from "react";

interface IconProps extends React.SVGProps<SVGSVGElement> {
  sx?: React.CSSProperties;
}

export const FlagIcon: React.FC<IconProps> = React.forwardRef<
  SVGSVGElement,
  IconProps
>(({ sx, color = "inherit", ...props }, ref) => {
  return (
    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg"
         style={{ color, ...sx }}
         {...props}
    >
      <path d="M4 21.0005V15.6875M4 15.6875C9.818 11.1375 14.182 20.2375 20 15.6875V4.31347C14.182 8.86347 9.818 -0.236528 4 4.31347V15.6875Z" stroke="#666666" stroke-linecap="round" stroke-linejoin="round"/>
    </svg>

  );
});
