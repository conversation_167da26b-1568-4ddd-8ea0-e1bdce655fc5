/* eslint-disable react-refresh/only-export-components */
import { Route } from "react-router-dom";
import { lazy, Suspense } from "react";
import { useSelector } from "react-redux";
import { getUserPortal } from "@/redux/userReducer";
import { PORTAL_EXTERNAL } from "@/helpers";
import { registerRoutes } from "@/helpers/routeDetector";
import { RouteGuard } from "@/components/RouteGuard";

import { PageLoader } from "@/components";
import SenaraiPermohonanEditForm from "@/pages/geran-external/senarai-permohonan/Form/SenaraiPermohonanEditForm";
import SenaraiPermohonanViewForm from "@/pages/geran-external/senarai-permohonan/Form/SenaraiPermohonanViewForm";
const GeranExternalLayout = lazy(() => import("@/pages/geran-external/Layout"));
const GeranExternalMain = lazy(() => import("@/pages/geran-external/Main"));
const SenaraiPermohonanForm = lazy(
  () => import("@/pages/geran-external/senarai-permohonan/Form")
);
const PelaksanaanProgramDetail = lazy(
  () => import("@/pages/geran-external/pelaksanaan-program/PelaksanaanProgramDetail")
);

// Layout component to wrap all geran-external routes with protection
const GeranExternalGuardedLayout = () => (
  <RouteGuard
    autoUpdatePortal={true}
    showDebugInfo={process.env.NODE_ENV === 'development'}
  >
    <Suspense fallback={<PageLoader />}>
      <GeranExternalLayout />
    </Suspense>
  </RouteGuard>
);

// Register routes with their portal types
// TODO: Team should register specific paths here
registerRoutes({
  // Example route registrations:
  '/geran-external': 'external',
  '/geran-external/senarai-permohonan': 'external',
  '/geran-external/senarai-permohonan/:societyid/:id' :'external',
  '/geran-external/senarai-permohonan/:societyid/:applicationId' :'external',
  '/geran-external/senarai-permohonan/view/:grantApplicationId' :'external',
  '/geran-external/pelaksanaan-program/:id/:societyId':'external'
  // Add your route registrations here
});

const routeComponents = (
  <Route
    path="geran-external"
    element={<GeranExternalGuardedLayout />}
  >
    <Route index element={<GeranExternalMain />} />
    <Route path="senarai-permohonan/:societyid/:id" element={<SenaraiPermohonanForm />} />
    <Route path="senarai-permohonan/:societyid/:id/:applicationId" element={<SenaraiPermohonanEditForm />} />
    <Route path="senarai-permohonan/view/:grantApplicationId" element={<SenaraiPermohonanViewForm />} />
    <Route path="pelaksanaan-program/:id/:societyId" element={<PelaksanaanProgramDetail />} />
  </Route>
);

/**
 * @deprecated please use {@link useGeranExternalRoutes} instead
 */
export const geranExternal = {
  routes:
    localStorage.getItem("portal") === PORTAL_EXTERNAL &&
    import.meta.env.VITE_APP_ENV !== "production" ? (
      <></>
    ) : null,
};

export const useGeranExternalRoutes = () => {
  const userPortal = useSelector(getUserPortal);
  const routes =
    userPortal === parseInt(PORTAL_EXTERNAL) &&
    import.meta.env.VITE_APP_ENV !== "production" &&
    routeComponents;

  return {
    routes,
  };
};
