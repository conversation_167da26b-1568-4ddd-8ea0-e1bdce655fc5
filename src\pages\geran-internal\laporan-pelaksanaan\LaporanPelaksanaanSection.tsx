import { DocumentUploadType, globalStyles, useQuery } from "@/helpers";
import { FieldValues, FormProvider, useForm } from "react-hook-form";
import { useTranslation } from "react-i18next";
import { Box, Typography } from "@mui/material";
import {
  FileUploadController,
  FileUploader,
  FormFieldRow,
  Label,
  TextFieldController,
} from "@/components";
import { useParams } from "react-router-dom";

const LaporanPelaksanaanSection: React.FC = () => {
  const { t } = useTranslation();
  const classes = globalStyles();

  const { applicationGeranId, societyId, reportId } = useParams();
  const {
    data: grantReportDetailDataResponse,
    isLoading: isLoadingGrantTemplate,
  } = useQuery({
    url: `grant/report/application/${applicationGeranId}`,
    onSuccess: (data) => {
      const grantReports = data?.data?.data;
      setValue("title", grantReports.title);
      setValue("description", grantReports.description);
    },
  });
  const grantReports = grantReportDetailDataResponse?.data?.data;
  const methods = useForm<FieldValues>({
    defaultValues: {
      title: grantReports?.title || "",
      description: grantReports?.description || "",
    },
  });
  const { control, handleSubmit, getValues, setValue } = methods;

  return (
    <FormProvider {...methods}>
      <Box className={classes.section}>
        <Box className={classes.sectionBox}>
          <Typography className="title" mb={1}>
            Aktivit Program
          </Typography>

          <FormFieldRow
            label={<Label text="Tajuk aktiviti program" />}
            value={
              <TextFieldController control={control} name="title" disabled />
            }
          />

          <FormFieldRow
            align="flex-start"
            label={<Label text="Diskripi program" />}
            value={
              <TextFieldController
                control={control}
                name="description"
                multiline
                disabled
                rows={10}
              />
            }
          />

          <FormFieldRow
            align="flex-start"
            label={<Label text="Mauat naik gambar aktiviti" />}
            value={
              <FileUploader
                type={DocumentUploadType.GERAN}
                code={"GERAN_REPORT_PHOTO"}
                grantApplicationId={applicationGeranId}
                disabled={true}
                societyId={societyId}
                accept="image/png, image/jpeg, image/gif, image/svg+xml"
                validTypes={[
                  "image/png",
                  "image/jpeg",
                  "image/gif",
                  "image/svg+xml",
                ]}
              />
            }
          />

        </Box>
      </Box>

      <Box className={classes.section}>
        <Box className={classes.sectionBox}>
          <Typography className="title" mb={2}>
            Lampiran perlaksanaan program
          </Typography>
          <FormFieldRow
            align="flex-start"
            label={<Label text="Mauat naik gambar aktiviti" />}
            value={
              <FileUploader
                type={DocumentUploadType.GERAN}
                code={"GERAN_REPORT"}
                grantApplicationId={applicationGeranId}
                disabled={true}
                societyId={societyId}
                validTypes={[
                  "text/plain",
                  "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
                  "application/msword",
                  "application/pdf",
                ]}
              />
            }
          />
        </Box>
      </Box>
    </FormProvider>
  );
};

export default LaporanPelaksanaanSection;
