name: Deploy Frontend Eroses Staging

on:
  push:
    branches:
      - staging

jobs:
  deploy_staging:
    runs-on: ubuntu-latest

    steps:
      - name: Checkout Repository
        uses: actions/checkout@v4

      - name: Set up Node.js
        uses: actions/setup-node@v4
        with:
          # TODO: update to latest LTS version of node.js if possible
          # node-version: lts/*
          node-version: 18
          cache: 'npm'

      - name: Install dependencies
        run: npm ci

      - name: Build the project for Staging
        env:
          # TODO: add VITE_BACKEND_URL env and BACKEND_URL_STAGING secret in github
          VITE_BACKEND_URL: ${{ secrets.BACKEND_URL_STAGING }}
          VITE_APP_ENV: staging
          NODE_ENV: production
          VITE_AWS_API_KEY_REGION: 'ap-southeast-5'
          VITE_AWS_API_KEY: ${{ secrets.MAP_API_KEY_STAGING }}
          VITE_AWS_PLACE_INDEX: 'explore.place.Grab'
          VITE_AWS_MAP_NAME: 'explore.map.Grab'
          NODE_OPTIONS: --max-old-space-size=4096
          VITE_GA_MEASUREMENT_ID: ${{ secrets.GA_MEASUREMENT_ID }}
        run: npm run build

      - name: Configure AWS Credentials for Staging
        uses: aws-actions/configure-aws-credentials@v4
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID_STAGING }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY_STAGING }}
          aws-region: ap-southeast-5

      - name: Deploy to S3 with Enhanced Caching Strategy
        run: |
          # Make deployment script executable
          chmod +x deploy.sh

          # Deploy with proper cache control headers
          ./deploy.sh ./dist eroses-frontend-cicd-staging ${{ secrets.CLOUDFRONT_DISTRIBUTION_ID_STAGING }}
        env:
          VERIFY_DEPLOYMENT: 'true'
