import { TimePickerUI } from "@/components/input/TimePickerUI";
import { globalStyles, useMutation, useQuery } from "@/helpers";
import { Box, Button, Grid, Typography, useTheme } from "@mui/material";
import { useEffect, useState } from "react";
import { Controller, FieldValues, useForm } from "react-hook-form";
import { useTranslation } from "react-i18next";
import { useNavigate, useParams } from "react-router-dom";

import DragDropTemplateEditor from "@/components/dragndrop/DragnDropTemplateEditor";
import dayjs from "dayjs";
import SectionBorang from "./SectionBorang";
import { useDispatch } from "react-redux";
import {
  addFieldToSection,
  addSection,
  GeranState,
  getSections,
  removeSection,
  resetGrantTemplate,
  Section,
  setCategoryPairs,
  setLastPickedComponentLabel,
  setSocietyRegistrationDate,
  setStatusReport,
  setSyaratGeran,
  updateFieldPageNumber,
  updateSectionsOrder,
} from "@/redux/geranReducer";
import { useSelector } from "react-redux";
import { RootState } from "@/redux/store";
import { useGrantTemplate } from "./useGrantTemplate";
import {
  ButtonOutline,
  ButtonPrimary,
  DatePickerForm,
  FormFieldRow,
  Label,
  TextFieldController,
} from "@/components";
import { LoadingOverlay } from "@/components/loading";

interface GrantTemplateFieldCreateRequest {
  id: string;
  fieldName: string;
  fieldType: string;
  isRequired: boolean;
  sequenceOrder: number;
  sectionId: string;
  pageNumber: number;
  sectionName: string;
  options?: string[];
}

interface GrantTemplatePayload {
  id: number | string;
  title: string;
  description: string;
  prePublishDate: string | null;
  endDate: string | null;
  fields: GrantTemplateFieldCreateRequest[];
  categorySubcategories: number[][];
  societyRegistrationDate: string | null;
  statusReport: string;
}

type Item = {
  id: string;
  component: React.ReactNode;
  meta: {
    fieldName: string;
    fieldType: string;
    isRequired: boolean;
    sequenceOrder: number;
    sectionName: string;
    pageNumber: number;
    options?: string[];
  };
};

function buildTableFromOptions(options: any[]): string[][] {
  const rowCount = parseInt(
    options.find((o) => o.key === "count-row")?.value || "0"
  );
  const colCount = parseInt(
    options.find((o) => o.key === "count-col")?.value || "0"
  );

  const table: string[][] = Array.from({ length: rowCount }, () =>
    Array.from({ length: colCount }, () => "")
  );

  options.forEach((o) => {
    if (o.row !== undefined && o.col !== undefined) {
      table[o.row][o.col] = o.value;
    }
  });

  return table;
}
function generateId() {
  return (
    crypto?.randomUUID?.() ??
    `f-${Date.now()}-${Math.floor(Math.random() * 1000)}`
  );
}

type NormalizedOption =
  | { key: string; value: string }
  | { key: string; value: string; row: number; col: number };

export function fixMalformedOptionStrings(optionParts: string[]): any[] {
  const mergedString = optionParts.join("");
  try {
    return JSON.parse(mergedString);
  } catch {
    const items: string[] = [];
    let current = "";

    for (let i = 0; i < optionParts.length; i++) {
      const part = optionParts[i].trim();
      current += part;

      if (part.endsWith("}")) {
        current = current.replace(/"(\w+)"\s*"value"/g, `"$1", "value"`);
        current = current.replace(/"value"\s*:/g, `"value":`);
        current = current.replace(/"key"\s*:/g, `"key":`);

        items.push(current);
        current = "";
      }
    }

    try {
      const fixed = `[${items.join(",")}]`;
      return JSON.parse(fixed);
    } catch (e) {
      console.error(
        "❌ Failed parse result fixMalformedOptionStrings:",
        e,
        items
      );
      return [];
    }
  }
}

function revertOption(options: any, type: string): string[] | string[][] {
  if (!options) return [];

  if (type === "DROPDOWN" || type === "CHECKBOX") {
    return Array.isArray(options)
      ? options.map((o: any) => (typeof o === "string" ? o : o?.value ?? ""))
      : [];
  }

  if (type === "TABLE" || type === "TRANSAKSI") {
    const tableOptions = Array.isArray(options)
      ? options.filter(
          (o: any) => o && o.key && !["count-col", "count-row"].includes(o.key)
        )
      : [];

    const rowCount =
      tableOptions.length > 0
        ? Math.max(...tableOptions.map((o: any) => o.row)) + 1
        : 0;
    const colCount =
      tableOptions.length > 0
        ? Math.max(...tableOptions.map((o: any) => o.col)) + 1
        : 0;

    const table: string[][] = Array.from({ length: rowCount }, () =>
      Array(colCount).fill("")
    );

    tableOptions.forEach((o: any) => {
      if (o && typeof o.row === "number" && typeof o.col === "number") {
        table[o.row][o.col] = o.value ?? "";
      }
    });

    return table;
  }

  if (type === "CONFIRM") {
    const description =
      options.find((o: any) => o.key === "description")?.value ?? "";
    const confirmationText =
      options.find((o: any) => o.key === "confirmationText")?.value ?? "";
    return [description, confirmationText];
  }

  if (type === "MAKLUMAT" || type === "CATATAN") {
    const description =
      options.find((o: any) => o.key === "description")?.value ?? "";
    return [description];
  }

  return [];
}

function mapGrantTemplateToGeranState(data: any): GeranState {
  const sectionsMap = new Map<string, Section>();

  data?.fields?.forEach((f: any) => {
    const sectionKey = `page-${f.pageNumber}`;

    if (!sectionsMap.has(sectionKey)) {
      sectionsMap.set(sectionKey, {
        id: generateId(),
        name: f.sectionName || `Page ${f.pageNumber}`,
        pageNumber: f.pageNumber,
        fields: [],
      });
    }

    const section = sectionsMap.get(sectionKey)!;

    const reverted = revertOption(f.options, f.fieldType);

    let options: string[] = [];
    let table: string[][] = [];

    if (f.fieldType === "TABLE") {
      f.table = revertOption(f.options, f.fieldType) as string[][];
    }
    if (f.fieldType === "TRANSAKSI") {
      f.table = revertOption(f.options, f.fieldType) as string[][];
      f.options = [];
    } else {
      if (Array.isArray(reverted)) {
        options = reverted.map((opt: any) =>
          typeof opt === "string" ? opt : opt.value
        );
      }
    }
    section.fields.push({
      id: generateId(),
      sectionId: section.id,
      sectionName: section.name,
      fieldName: f.fieldName,
      fieldType: f.fieldType,
      isRequired: f.isRequired,
      sequenceOrder: f.sequenceOrder,
      pageNumber: f.pageNumber,
      options,
      table:
        f.fieldType === "TRANSAKSI" || f.fieldType === "TABLE"
          ? f.table
          : table,
    });
  });
  sectionsMap.forEach((section) => {
    section.fields.sort((a, b) => a.sequenceOrder - b.sequenceOrder);
  });

  const sortedSections = Array.from(sectionsMap.values()).sort(
    (a, b) => a.pageNumber - b.pageNumber
  );

  return {
    selectedOrganization: null,
    sections: sortedSections,
    title: data?.title || "",
    prePublishDate: data.prePublishDate || null,
    endDate: data.endDate || null,
    categoryPairs: data.categoryPairs || [],
    societyRegistrationDate: null,
    currentGrant: { id: null },
    statusReport: data.statusReport || "",
    lastPickedComponentLabel: null,
  };
}

const CiptaBorangDetail: React.FC = () => {
  const { grantTemplateId } = useParams();
  const navigate = useNavigate();
  const { t } = useTranslation();
  const classes = globalStyles();
  const [isSuccess, setIsSuccess] = useState(false);
  const dispatch = useDispatch();
  const labelStyle = {
    fontSize: "16px",
    color: "#666666",
    fontWeight: "400 !important",
  };

  const [itemSort, setItemSort] = useState<Item[]>([]);
  const sections = useSelector(getSections);
  const [selected, setSelected] = useState<string | null>(null);
  const geran = useSelector((state: RootState) => state.geran);

  const { control, handleSubmit, getValues, watch, setValue, reset } =
    useForm<FieldValues>({
      defaultValues: {
        title: "",
        description: "",
        prePublishDate: null,
        endDate: null,
        categoryPairs: [],
        societyRegistrationDate: null,
        statusReport: "",
      },
    });
  const {
    createTemplate,
    updateTemplate,
    isCreating,
    isUpdating,
    templateData,
  } = useGrantTemplate(grantTemplateId, reset);
  const values = watch();
  const { fetch: publishTemplateGeran, isLoading: isPublishApplicationGeran } =
    useMutation({
      url: "grant/template/publish",
      method: "put",
      onSuccess: (res) => {
        const responseCode = res?.data?.code;
        if (responseCode === 200) {
          setIsSuccess(true);
          setTimeout(() => {
            navigate(`../pengurusan-borang`);
            dispatch(resetGrantTemplate());
          }, 2000);
        }
      },
    });
  const onPublishDraft = async (formData: FieldValues) => {
    try {
      await onSaveDraftBeforeSubmit(formData);
    } catch (err) {
      console.error("Publish draft failed:", err);
    }
  };

  useEffect(() => {
    if (templateData?.data?.data) {
      const data = templateData.data.data;
      reset({
        title: data.title || "",
        description: data.description || "",
        prePublishDate: data.prePublishDate ? dayjs(data.prePublishDate) : null,
        endDate: data.endDate ? dayjs(data.endDate) : null,
        categoryPairs: data.categoriesSubCategories || [],
        societyRegistrationDate: data.societyRegistrationDate,
        statusReport: data.statusReport || "",
      });
      if (data.categoryPairs) {
        dispatch(setCategoryPairs(data.categoriesSubCategories));
      }
      if (data.societyRegistrationDate) {
        dispatch(setSocietyRegistrationDate(data.societyRegistrationDate));
      }
      if (data.statusReport) {
        dispatch(setStatusReport(data.statusReport));
      }
    }
  }, [templateData, reset]);
  const items = [
    "Tajuk",
    "Senarai Dropdown",
    "Ruangan Pilihan",
    "Ruangan Maklumat",
    "Ruangan Tarikh",
    "Ruangan Jadual",
    "Ruangan Konfirmasi",
    "Ruangan Lampiran",
    "Ruangan Transaksi",
    "Ruangan Catatan",
  ];

  const [activeSectionId, setActiveSectionId] = useState<string | null>(null);
  const handlePickComponent = (label: string) => {
    dispatch(setLastPickedComponentLabel(label));
    setSelected((prev) => (prev === label ? null : label));
  };

  const handleAddSection = () => {
    const id = `sec-${Date.now()}`;
    dispatch(
      addSection({
        id,
        name: `Section ${sections.length + 1}`,
      })
    );
    setActiveSectionId(id);
  };

  const handleAddItem = (label: string) => {
    if (!activeSectionId) {
      console.warn("Belum ada section aktif. Tambah/klik section dulu.");
      return;
    }
    const section = sections.find((s) => s.id === activeSectionId);
    const seq = (section?.fields?.length ?? 0) + 1;

    const newField = {
      id: `field-${Date.now()}-${Math.floor(Math.random() * 1000)}`,
      fieldName: label,
      fieldType: mapLabelToType(label),
      isRequired: true,
      sequenceOrder: seq,
      sectionId: activeSectionId,
      sectionName: "",
      pageNumber: section?.pageNumber ?? 1,
      options: [],
    };

    dispatch(
      addFieldToSection({ sectionId: activeSectionId, field: newField })
    );
  };

  const [templateId, setTemplateId] = useState<number | null>(null);

  const mapLabelToType = (label: string) => {
    switch (label) {
      case "Tajuk":
        return "TEXT";
      case "Senarai Dropdown":
        return "DROPDOWN";
      case "Ruangan Pilihan":
        return "CHECKBOX";
      case "Ruangan Jadual":
        return "TABLE";
      case "Ruangan Lampiran":
        return "UPLOAD";
      case "Ruangan Konfirmasi":
        return "CONFIRM";
      case "Ruangan Catatan":
        return "CATATAN";
      case "Ruangan Tarikh":
        return "TARIKH";
      case "Ruangan Maklumat":
        return "MAKLUMAT";
      case "Ruangan Transaksi":
        return "TRANSAKSI";
      default:
        return "TEXT";
    }
  };
  const { fetch: updateTemplateGeran, isLoading: isUpdateApplicationGeran } =
    useMutation({
      url: "grant/template/update",
      method: "put",
      onSuccess: (res) => {
        const responseCode = res?.data?.code;
        setTemplateId(res?.data.data);
        if (responseCode === 200) {
          setIsSuccess(true);
          setTimeout(() => {
            navigate(`../pengurusan-borang`);
          }, 2000);
        }
      },
    });

  const {
    fetch: updateTemplateBeforeSubmitGeran,
    isLoading: isUpdateBeforeSubmitApplicationGeran,
  } = useMutation({
    url: "grant/template/update",
    method: "put",
    onSuccess: (res) => {
      setTemplateId(res?.data.data);
      publishTemplateGeran({ id: grantTemplateId });
    },
  });

  const grantTemplate = useSelector((state: RootState) => state.geran.sections);
  function normalizeOptions(
    options: string[] = [],
    type: string,
    table?: string[][]
  ) {
    if (type === "DROPDOWN" || type === "CHECKBOX") {
      return options.map((o) => ({
        key: o.replace(/\s+/g, "").toLowerCase(),
        value: o,
      }));
    }
    if (
      type === "TABLE" ||
      (type === "TRANSAKSI" && table && table.length > 0)
    ) {
      const tableOptions: {
        key: string;
        value: string;
        row: number;
        col: number;
      }[] = [];

      table?.forEach((row, rowIndex) => {
        row.forEach((cell, colIndex) => {
          tableOptions.push({
            key: `r${rowIndex}-c${colIndex}`,
            value: cell,
            row: rowIndex,
            col: colIndex,
          });
        });
      });

      tableOptions.push(
        {
          key: "count-col",
          value: table?.[0].length.toString() || "0",
          row: 0,
          col: 0,
        },
        {
          key: "count-row",
          value: table?.length.toString() || "0",
          row: 0,
          col: 0,
        }
      );

      return tableOptions;
    }
    if (type === "CONFIRM") {
      return [
        {
          key: "description",
          value: options[0] ?? "",
        },
        {
          key: "confirmationText",
          value: options[1] ?? "",
        },
      ];
    }

    if (type === "MAKLUMAT" || type === "CATATAN") {
      return [
        {
          key: "description",
          value: options[0] ?? "",
        },
      ];
    }

    return [];
  }

  const isDraftEnabled =
    values.title &&
    values.prePublishDate &&
    values.endDate &&
    Array.isArray(values.categoryPairs) &&
    values.categoryPairs?.length > 0;

  const onSaveDraft = async (formData: FieldValues) => {
    const fields =
      grantTemplate?.flatMap((section: any) =>
        section.fields.map((field: any) => ({
          fieldName: field.fieldName,
          fieldType: field.fieldType,
          isRequired: field.isRequired,
          sequenceOrder: field.sequenceOrder,
          sectionName: field.sectionName,
          pageNumber: section.pageNumber,
          options: normalizeOptions(
            field.options,
            field.fieldType,
            field.table
          ),
        }))
      ) || [];
    dispatch(
      setSyaratGeran({
        prePublishDate: values.prePublishDate
          ? dayjs(values.prePublishDate).toISOString()
          : null,
        endDate: values.endDate ? dayjs(values.endDate).toISOString() : null,
      })
    );
    const payload: GrantTemplatePayload = {
      id: String(grantTemplateId),
      title: formData.title,
      description: formData.description || "",
      prePublishDate: formData.prePublishDate
        ? dayjs(formData.prePublishDate).format("YYYY-MM-DDTHH:mm:ss")
        : null,
      endDate: formData.endDate
        ? dayjs(formData.endDate).format("YYYY-MM-DDTHH:mm:ss")
        : null,
      fields,
      categorySubcategories: formData.categoryPairs || [],
      societyRegistrationDate: formData.societyRegistrationDate
        ? dayjs(formData.societyRegistrationDate).format("YYYY-MM-DD")
        : null,
      statusReport: formData.statusReport,
    };

    try {
      await updateTemplateGeran(payload);
      // dispatch(resetGrantTemplate());
    } catch (err) {
      console.error("Save draft failed:", err);
    }
  };

  const onSaveDraftBeforeSubmit = async (formData: FieldValues) => {
    const fields =
      grantTemplate?.flatMap((section: any) =>
        section.fields.map((field: any) => ({
          fieldName: field.fieldName,
          fieldType: field.fieldType,
          isRequired: field.isRequired,
          sequenceOrder: field.sequenceOrder,
          sectionName: field.sectionName,
          pageNumber: section.pageNumber,
          options: normalizeOptions(
            field.options,
            field.fieldType,
            field.table
          ),
        }))
      ) || [];
    dispatch(
      setSyaratGeran({
        prePublishDate: values.prePublishDate
          ? dayjs(values.prePublishDate).toISOString()
          : null,
        endDate: values.endDate ? dayjs(values.endDate).toISOString() : null,
      })
    );
    const payload: GrantTemplatePayload = {
      id: String(grantTemplateId),
      title: formData.title,
      description: formData.description || "",
      prePublishDate: formData.prePublishDate
        ? dayjs(formData.prePublishDate).format("YYYY-MM-DDTHH:mm:ss")
        : null,
      endDate: formData.endDate
        ? dayjs(formData.endDate).format("YYYY-MM-DDTHH:mm:ss")
        : null,
      fields,
      categorySubcategories: formData.categoryPairs || [],
      societyRegistrationDate: formData.societyRegistrationDate
        ? dayjs(formData.societyRegistrationDate).format("YYYY-MM-DD")
        : null,
      statusReport: formData.statusReport,
    };

    try {
      await updateTemplateBeforeSubmitGeran(payload);
      dispatch(resetGrantTemplate());
    } catch (err) {
      console.error("Save draft failed:", err);
    }
  };

  useEffect(() => {
    async function fetchGrantTemplate() {
      const mapped = mapGrantTemplateToGeranState(templateData?.data?.data);
      dispatch(setSyaratGeran(mapped));
    }

    fetchGrantTemplate();
  }, [dispatch, templateData]);
  if (
    isUpdateBeforeSubmitApplicationGeran ||
    isUpdateApplicationGeran ||
    isPublishApplicationGeran
  ) {
    return <LoadingOverlay />;
  }

  return (
    <>
      <Box className={classes.section}>
        <Box className={classes.sectionBox}>
          <Box
            sx={{
              display: "flex",
              alignItems: "center",
              justifyContent: "space-between",
              padding: "2px",
              mb: 2,
            }}
          >
            <Typography className="title">Tetapan Syarat Geran</Typography>
            <Button
              onClick={() => navigate(`./syarat-geran`)}
              className={classes.btnOutline}
              sx={{ fontSize: "12px !important" }}
            >
              {t("tetapan")}
            </Button>
          </Box>
        </Box>
        <Box className={classes.sectionBox} mt={2}>
          <Typography className="title">Komponen Borang</Typography>
          <Box mt={4}>
            <Grid container spacing={2}>
              {items.map((label, index) => {
                const isSelected = selected === label;

                return (
                  <Grid item xs={4} key={index}>
                    <Box
                      display="flex"
                      alignItems="center"
                      onClick={() => {
                        handleAddItem(label);
                        handlePickComponent(label);
                      }}
                      sx={{ cursor: "pointer" }}
                    >
                      <Box
                        sx={{
                          display: "flex",
                          alignItems: "center",
                          justifyContent: "center",
                          bgcolor: "#00B6AD",
                          border: `1px solid #00B6AD`,
                          color: "#00B6AD",
                          borderRadius: "10px",
                          width: "37px",
                          height: "32px",
                          boxShadow: "4px 6px 12px 0px #6666664D",
                          mr: 1,
                        }}
                      >
                        <img
                          src={"/plus-white.png"}
                          alt="plus"
                          style={{ width: 14, height: 14 }}
                        />
                      </Box>
                      <Typography
                        sx={{
                          border: `1px solid ${
                            isSelected ? "#00B6AD" : "var(--primary-color)"
                          }`,
                          borderRadius: "10px",
                          px: 2,
                          py: 1,
                          fontSize: "12px",
                          fontWeight: 400,
                          width: "160px",
                          color: isSelected ? "white" : "var(--primary-color)",
                          backgroundColor: isSelected
                            ? "#00B6AD"
                            : "transparent",
                          textAlign: "center",
                        }}
                      >
                        {label}
                      </Typography>
                    </Box>
                  </Grid>
                );
              })}
            </Grid>
          </Box>
        </Box>
        <Box className={classes.sectionBox} mt={2}>
          <Typography className="title" mb={2}>
            Nama Geran Borang
          </Typography>
          <FormFieldRow
            label={<Label text="Nama Geran" required />}
            value={
              <TextFieldController
                control={control}
                required
                name="title"
                onChange={(e) => {
                  const value = e.target.value;
                  setValue("title", value);
                  dispatch(setSyaratGeran({ title: value }));
                }}
              />
            }
          />
        </Box>
        <Box className={classes.sectionBox} mt={2}>
          <Typography className="title" mb={8}>
            Masa dan Tarikh Pelancaran Geran
          </Typography>
          <Grid container sx={{ mb: 1 }}>
            <Grid item xs={12} sm={4.05}>
              <Typography sx={labelStyle}>
                Tarikh Pelancaran Geran <span style={{ color: "red" }}>*</span>
              </Typography>
            </Grid>
            <Grid item xs={12} sm={7.95}>
              <DatePickerForm
                name="prePublishDate"
                control={control}
                required
                minDate={dayjs()}
              />
            </Grid>
          </Grid>

          <Grid container sx={{ mb: 1, mt: 2 }}>
            <Grid item xs={12} sm={4.05}>
              <Typography sx={labelStyle}>
                Tarikh Tamat Geran <span style={{ color: "red" }}>*</span>
              </Typography>
            </Grid>
            <Grid item xs={12} sm={7.95}>
              <DatePickerForm
                name="endDate"
                control={control}
                required
                minDate={dayjs()}
              />
            </Grid>
          </Grid>
        </Box>{" "}
      </Box>
      <Box mt={2}>
        <DragDropTemplateEditor
          items={sections?.map((section) => ({
            id: section.id,
            component: (
              <SectionBorang
                key={section.id}
                section={section}
                isActive={activeSectionId === section.id}
                onClick={() => setActiveSectionId(section.id)}
                onDelete={(id) => dispatch(removeSection(id))}
                onAddField={(sectionId) => {
                  const sectionIndex = sections.findIndex(
                    (s) => s.id === sectionId
                  );
                  dispatch(
                    addFieldToSection({
                      sectionId,
                      field: {
                        id:
                          crypto?.randomUUID?.() ??
                          `f-${Date.now()}-${Math.floor(Math.random() * 1000)}`,
                        sectionId,
                        fieldName: "New Field",
                        fieldType: "text",
                        isRequired: false,
                        sequenceOrder: section.fields.length + 1,
                        sectionName: section.name,
                        pageNumber: sectionIndex + 1,
                      },
                    })
                  );
                }}
              />
            ),
          }))}
          onOrderChange={(updatedItems) => {
            const newOrder = updatedItems.map((item) => item.id);
            dispatch(updateSectionsOrder(newOrder));
            updatedItems.forEach((item, index) => {
              const sectionId = item.id;
              const section = sections.find((s) => s.id === sectionId);
              if (section) {
                section.fields.forEach((field) => {
                  dispatch(
                    updateFieldPageNumber({
                      sectionId,
                      fieldId: field.id,
                      pageNumber: index + 1,
                    })
                  );
                });
              }
            });
          }}
        />
      </Box>

      <Box display="flex" justifyContent="center" mb={2}>
        <Button
          variant="contained"
          size="small"
          onClick={handleAddSection}
          sx={{
            minWidth: "39px",
            padding: 1,
            mt: 2,
            backgroundColor: "#00b4b4",
            "&:hover": {
              backgroundColor: "#00a0a0",
            },
          }}
        >
          <img
            src={"/plus-white.png"}
            alt="plus"
            style={{ width: 14, height: 14 }}
          />
        </Button>
      </Box>

      <Box className={classes.section}>
        <Box
          className={classes.sectionBox}
          sx={{
            display: "flex",
            flexDirection: "row",
            justifyContent: "right",
            gap: "8px",
          }}
        >
          <ButtonOutline
            type="submit"
            onClick={handleSubmit(onSaveDraft)}
            disabled={!isDraftEnabled}
            sx={{
              display: "block",
              fontSize: "12px",
            }}
          >
            {t("updateDraftGeran")}
          </ButtonOutline>
          <ButtonPrimary
            type="submit"
            onClick={handleSubmit(onPublishDraft)}
            sx={{
              display: "block",
              fontSize: "12px",
            }}
          >
            Cipta Borang Geran
          </ButtonPrimary>
        </Box>
      </Box>
    </>
  );
};

export default CiptaBorangDetail;
