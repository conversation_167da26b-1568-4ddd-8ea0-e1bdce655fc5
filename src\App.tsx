import React from "react";
import { PersistGate } from "reduxjs-toolkit-persist/integration/react";
import {
  <PERSON><PERSON><PERSON><PERSON>outer,
} from "react-router-dom";
import { Provider } from "react-redux";
import CssBaseline from "@mui/material/CssBaseline";
import GlobalStyles from "@mui/material/GlobalStyles";

import { ColorModeContextProvider } from "./contexts/color-mode";
import {
  UnsavedChangesNotifier,
} from "@refinedev/react-router-v6";

import { RefineComponents } from "./RefineComponents";
import { LocalizationProvider } from "./components/provider/Localization";
import ChatbotWidget from "./components/chatbot";
import { AppRoutes } from "./AppRoutes";
import ReactGA from 'react-ga4';
import { store, persistor } from "./redux/store";

import "./styles/global.scss";

const measurementId = import.meta.env.VITE_GA_MEASUREMENT_ID

ReactGA.initialize(measurementId);
function App() {
  //const notificationProvider = useNotificationProvider();
  React.useEffect(() => {
    const handleBeforeUnload = () => {
      if ("caches" in window) {
        caches.keys().then((names) => {
          names.forEach((name) => {
            caches.delete(name);
          });
        });
      }
    };

    window.addEventListener("beforeunload", handleBeforeUnload);

    return () => {
      window.removeEventListener("beforeunload", handleBeforeUnload);
    };
  }, []);

  return (
    <Provider store={store}>
      <PersistGate loading={null} persistor={persistor}>
        <LocalizationProvider>
          <BrowserRouter>
            <ColorModeContextProvider>
              <CssBaseline />
              <GlobalStyles
                styles={{ html: { WebkitFontSmoothing: "auto" } }}
              />
              <RefineComponents>
                <AppRoutes />
                <ChatbotWidget />
                <UnsavedChangesNotifier />
              </RefineComponents>
            </ColorModeContextProvider>
          </BrowserRouter>
        </LocalizationProvider>
      </PersistGate>
    </Provider>
  );
}

export default App;
