import { Navigate, Route } from "react-router-dom";
import UpdateAttendance from "@/pages/takwim/UpdateAttendance";
import TakwimProvider from "@/contexts/takwimProvider";
import TakwimLandingPage from "@/pages/takwim/TakwimLandingPage";
import { registerRoutes } from "../../helpers/routeDetector";
import { RouteGuard } from "../../components/RouteGuard";
import AuthHelper from "@/helpers/authHelper";
import { NEW_PermissionNames } from "@/helpers";

// Layout component that combines TakwimProvider with RouteGuard
const TakwimGuardedLayout = () => {
  if (
    !AuthHelper.hasAuthority([NEW_PermissionNames.TAKWIM.label]) &&
    localStorage.getItem("portal") === "2"
  ) {
    return <Navigate to="/forbidden" replace />;
  }
  <RouteGuard
    autoUpdatePortal={true}
    showDebugInfo={process.env.NODE_ENV === "development"}
  >
    <TakwimProvider>
      <TakwimLandingPage />
    </TakwimProvider>
  </RouteGuard>;
};

// Register routes with their portal types
// TODO: Team should register specific paths here
registerRoutes({
  // Example: '/takwim': 'shared',
  // Example: '/takwim/update-attendance': 'shared',
  // Add your route registrations here
});

export const takwimAuth = {
  routes: (
    <>
      <Route path="/takwim" element={<TakwimGuardedLayout />}>
        {/* <Route index element={<TakwimActivityListsPage />} />
        <Route path="activity" element={<TakwimActivityListsPage />} />
        <Route
          path="activity/:eventNo"
          element={<TakwimActivityDetailsPage />}
        />
        <Route path="create-event" element={<CreateEventPage />} />
        <Route path="edit-event/:eventNo" element={<CreateEventPage />} />
        <Route path="terma-penggunaan" element={<TermaPenggunaan />} /> */}
        <Route path="update-attendance" element={<UpdateAttendance />} />
      </Route>
    </>
  ),
};
