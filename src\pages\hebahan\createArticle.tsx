import React, {useCallback, useEffect, useState} from "react";
import InternalHebahanHeader from "@/pages/hebahan/hebahanHeader";
import {Box, Checkbox, FormControl, FormHelperText, Grid, MenuItem, Select, TextField, Typography} from "@mui/material";
import {headerStyle, labelStyle} from "@/pages/hebahan/hebahanConstant";
import {useTranslation} from "react-i18next";
import {
  ButtonOutline,
  ButtonPrimary,
  DatePickerController,
  DialogConfirmation,
  TimePickerController
} from "@/components";
import {DocumentUploadType, useGetDocument} from "@/helpers";
import FileUploader from "../../components/input/fileUpload";
import {API_URL} from "@/api";
import {useCustom, useCustomMutation} from "@refinedev/core";
import {useLocation, useNavigate, useParams} from "react-router-dom";
import {FieldValues, useForm} from "react-hook-form";

export interface PostArticleProps {
  isUpdate: boolean,
  isAdmin: boolean,
}

const CreateArticle: React.FC<PostArticleProps> = ({ isUpdate, isAdmin }) => {
  const { control, setValue } = useForm<FieldValues>({
    defaultValues: {},
  });
  const { t, i18n } = useTranslation();
  const [openModal, setOpenModal] = useState(false);
  //const [category, setCategory] = useState()
  const [dokumen, setDokumen] = useState("");
  const navigate = useNavigate();
  const [articleId, setArticleId] = useState<string>("0");
  const [formErrors, setFormErrors] = useState<{ [key: string]: string }>({});
  const [publishStartDate, setPublishStartDate] = useState<string | null>("");
  const [publishStartTime, setPublishStartTime] = useState<string | null>("");
  const [publishEndDate, setPublishEndDate] = useState<string | null>("");
  const [publishEndTime, setPublishEndTime] = useState<string | null>("");
  //const [isUrgent, setIsUrgent] = useState(false);
  const location = useLocation();
  const userData = location?.state?.userData ?? {};
  const [formData, setFormData] = useState({
    title: "",
    author: `${userData.name}`,
    category: isAdmin ? "ANNOUNCEMENT" : "",
    subCategory: "",
    description: "",
    expirationDate: "",
    prePublishDate: "",
    urgencyCategory: "GENERAL",
    status: ""
  });

  const societyId = location?.state?.societyId ?? null;

  const datePickerStyles = {
    ".MuiInputBase-input": {
      fontSize: 14,
      ...({ backgroundColor: "#E8E9E8 !important" }),
    },
    ...({
      ".MuiInputBase-adornedEnd": {
        backgroundColor: "#E8E9E8 !important",
      },
    }),
  };

  const timePickerDefaultStyles = {
    width: {
      xs: "100%",
      sm: "40%",
      md: "40%",
      lg: "30%",
      xl: "20%",
    },
    // flex: 2,
    fontSize: 14,
    borderRadius: 2,
  };
  const timePickerStyles = {
    sx: {
      ...timePickerDefaultStyles,
      ...datePickerStyles,
    },
  };

  const { id } = useParams<{ id: string }>()
  const tempArticleId = (id ?? "0")
  //console.log("articleId",!!tempArticleId);
  useEffect(() => {
    if (tempArticleId != "0") {
      setArticleId(tempArticleId);
      /*getPreviousDocument(
        [
          {
            field: "postingMediaId",
            operator: "eq",
            value: tempArticleId,
          },
        ]
      )*/
    }
  }, [tempArticleId]);
  const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      //setSelectedFile(file);
      setDokumen(file.name);
    }
  };

  const { getDocument: getPreviousDocument } = useGetDocument({
    onSuccess: (data) => {
      if (data) {
        //setValue("urlPdf", data?.name ?? "");
        //setPDFPreview(data?.url ?? "");
        console.log(data);
      }
    },
  });

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData((prevState) => ({
      ...prevState,
      [name]: value,
    }));
    setFormErrors((prev) => ({ ...prev, [name]: "" }));
  };

  const { data: categoryData, isLoading: isCategoryLoading } = useCustom({
    url: `${API_URL}/society/admin/category/list`,
    method: "get",
    config: {
      headers: {
        portal: localStorage.getItem("portal"),
        authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
      },
    },
  });

  const categories = categoryData?.data?.data || [];
  const articleCategoriesAdmin = [
    {
      id: 1,
      name: "ANNOUNCEMENT"
    },
    {
      id: 2,
      name: "ACTIVITY"
    },
    {
      id: 3,
      name: "PROMOTION"
    },
  ]
  const articleCategoriesSecretary = [
    {
      id: 1,
      name: "ACTIVITY"
    },
    {
      id: 2,
      name: "PROMOTION"
    },
  ]
  const mainCategories = categories.filter((cat: any) => cat.level === 1);
  //const subCategories = categories.filter((cat: any) => cat.level === 2);

  const { mutate: create, isLoading: isLoadingCreate } = useCustomMutation();
  const Create = (status: string): void => {
    const currentDate = new Date();
    const formattedDate = currentDate.toISOString().split("T")[0];
    create(
      {
        url: isAdmin ? `${API_URL}/society/admin/posting/create` : `${API_URL}/society/secretary/posting/create`,
        method: "post",
        values: {
          author: formData.author,
          title: formData.title,
          description: formData.description,
          category: formData.category,
          subCategory: formData.subCategory,
          expirationDate: `${publishEndDate} ${publishEndTime}`,
          prePublishDate: `${publishStartDate} ${publishStartTime}`,
          urgencyCategory: formData.urgencyCategory,
          status: status,
          societyId: isAdmin ? null : societyId
        },
        config: {
          headers: {
            "Content-Type": "application/json",
            portal: localStorage.getItem("portal") || "",
            authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
          },
        },
        successNotification: (data) => {
          if (data?.data?.data) {
            //console.log("Create", data?.data?.data);
            const id = data?.data?.data;
            navigate(`../update/${id}`, { replace: true });
            //setArticleId(data?.data?.data);
            return {
              message: data?.data?.msg,
              type: "success",
            };
          } else {
            return {
              message: t("error") + data?.data?.msg,
              type: "error",
            };
          }
        },
        errorNotification: (data) => {
          return {
            message: data?.response?.data?.msg,
            type: "error",
          };
        },
      },
      {
        onError(error, variables, context) {
          console.log(error);
        }
      }
    );
  };
  console.log("isAdmin",isAdmin)
  console.log("formData.category",formData.category)
  const { mutate: edit, isLoading: isLoadingEdit } = useCustomMutation();
  const Edit = (status: string): void => {
    const currentDate = new Date();
    const formattedDate = currentDate.toISOString().split("T")[0];
    edit(
      {
        url: isAdmin ? `${API_URL}/society/admin/posting/update` : `${API_URL}/society/secretary/posting/update`,
        method: "put",
        values: {
          id: id,
          author: formData.author,
          title: formData.title,
          description: formData.description,
          category: formData.category,
          subCategory: formData.subCategory,
          expirationDate: `${publishEndDate} ${publishEndTime}`,
          prePublishDate: `${publishStartDate} ${publishStartTime}`,
          urgencyCategory: formData.urgencyCategory,
          status: formData.status,
        },
        config: {
          headers: {
            "Content-Type": "application/json",
            portal: localStorage.getItem("portal") || "",
            authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
          },
        },
        successNotification: (data) => {
          if (data?.data?.data) {
            //console.log("Edit", data?.data?.data);
            navigate(`../preview/${articleId}`)
            return {
              message: data?.data?.msg,
              type: "success",
            };
          } else {
            return {
              message: t("error") + data?.data?.msg,
              type: "error",
            };
          }
        },
        errorNotification: (data) => {
          return {
            message: data?.response?.data?.msg,
            type: "error",
          };
        },
      },
      {
        onError(error, variables, context) {
          console.log(error);
        },
      }
    );
  };


  const { data: articleData, isLoading: isArticleLoading } = useCustom({
    url: `${API_URL}/society/posting/${articleId}`,
    method: "get",
    config: {
      headers: {
        portal: localStorage.getItem("portal"),
        authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
      },
    },
    queryOptions: {
      enabled: isUpdate && articleId!="0",
      retry: false,
      cacheTime: 0,
    },
  });

  const articleDetails = articleData?.data?.data || {};
  console.log("articleDetails", articleDetails, isUpdate)

  const handleSaveDraft = (e: React.MouseEvent<HTMLButtonElement>) => {
    if (articleId!="0") Edit("DRAFT");
    else Create("DRAFT");
  }

  const validateForm = () => {
    const errors: { [key: string]: string } = {};
    console.log("validateForm");
    if (!formData.title) errors.title = t("fieldRequired");
    if (!formData.description) errors.description = t("fieldRequired");
    if (!formData.author) errors.level = t("fieldRequired");
    if (!formData.subCategory) errors.explanation = t("fieldRequired");
    if (!formData.expirationDate) errors.objective = t("fieldRequired");
    if (!formData.prePublishDate) errors.objective = t("fieldRequired");
    if (!formData.status) errors.objective = t("fieldRequired");
    return errors;
  };

  const handleSave = () => {
    const errors = validateForm();
    if (Object.keys(errors).length > 0) {
      setFormErrors(errors);
      return;
    }
    if (articleId!="0") Edit(formData.status);
    else Create(formData.status);
  }

  useEffect(() => {
    if (Object.keys(articleDetails).length > 0) {
      const temp = {
        title: articleDetails.title,
        author: articleDetails.author,
        category: articleDetails.category,
        subCategory: articleDetails.subCategory,
        description: articleDetails.description,
        expirationDate: articleDetails.expirationDate,
        prePublishDate: articleDetails.prePublishDate,
        urgencyCategory: articleDetails.urgencyCategory,
        status: articleDetails.status
      }
      //console.log("temp",temp);
      setFormData(temp);
      if (articleDetails.prePublishDate) {
        const tempPublishStartDate = articleDetails.prePublishDate.split(" ")[0];
        let tempPublishStartTime = articleDetails.prePublishDate.split(" ")[1];
        if(tempPublishStartTime.split(":").length == 2){
          tempPublishStartTime = `${tempPublishStartTime}:00`
        }
        console.log(tempPublishStartTime);
        setPublishStartDate(tempPublishStartDate);
        setPublishStartTime(tempPublishStartTime);
        setValue("publishStartDate", tempPublishStartDate);
        setValue("publishStartTime", tempPublishStartTime);
      }

      if (articleDetails.expirationDate) {
        const tempPublishEndDate = articleDetails.expirationDate.split(" ")[0];
        let tempPublishEndTime = articleDetails.expirationDate.split(" ")[1];
        if(tempPublishEndTime.split(":").length == 2){
          tempPublishEndTime = `${tempPublishEndTime}:00`
        }
        console.log("tempPublishEndDate", tempPublishEndDate);
        setPublishEndDate(tempPublishEndDate);
        setPublishEndTime(tempPublishEndTime);
        setValue("publishEndDate", tempPublishEndDate);
        setValue("publishEndTime", tempPublishEndTime);
      }
    }
  }, [articleData])

  const handleOldUploadedFiles = useCallback((files: any) => {},[])

  return (
    <>
      <InternalHebahanHeader />
      <Box
        sx={{
          borderRadius: 2.5,
          backgroundColor: "#fff",
          //display: "inline",
          px: 2,
          py: 2,
          mb: 1,
        }}
      >
        <Box
          sx={{
            borderRadius: 2.5,
            backgroundColor: "#fff",
            border: "1px solid #D9D9D9",
            //flex: 5,
            //display: "inline",
            px: 2,
            py: 2,
            mb: 1,
          }}
        >
          <Typography sx={headerStyle}>
            {t("articleTitle")}
          </Typography>
          <Grid container spacing={2} sx={{ mt: 1 }}>
            <Grid item xs={12} sm={4}>
              <Typography sx={labelStyle}>
                {t("tajuk")}
              </Typography>
            </Grid>
            <Grid item xs={12} sm={8}>
              <TextField
                size={"small"}
                fullWidth
                required
                name="title"
                value={formData.title}
                error={!!formErrors.title}
                onChange={handleInputChange}
              />
            </Grid>
            <Grid item xs={12} sm={4}>
              <Typography sx={labelStyle}>
                {t("author")}
              </Typography>
            </Grid>
            <Grid item xs={12} sm={8}>
              <TextField
                size={"small"}
                fullWidth
                required
                name="author"
                value={formData.author}
                error={!!formErrors.author}
                onChange={handleInputChange}
              />
            </Grid>
            <Grid item xs={12} sm={4}>
              <Typography sx={labelStyle}>
                {t("category")}
              </Typography>
            </Grid>
            <Grid item xs={12} sm={8}>
              <FormControl
                fullWidth
                required
                error={!!formErrors.category}
              >
                <Select
                  value={formData.category}
                  displayEmpty
                  required
                  name="category"
                  size={"small"}
                  //disabled={isAdmin}
                  onChange={(e) => {
                    //setCategory(e.target.value);
                    setFormData((prevState) => ({
                      ...prevState,
                      category: e.target.value,
                      subCategory: ""
                    }));
                    setFormErrors((prev) => ({
                      ...prev,
                      category: isAdmin ? "ANNOUNCEMENT" : "",
                    }));
                  }}
                  variant={'outlined'}
                  renderValue={(selected) =>
                    selected
                      ? t((isAdmin ? articleCategoriesAdmin : articleCategoriesSecretary).find(
                        (cat: any) => cat.name === (selected)
                      )?.name ?? "")
                      : t("selectPlaceholder")
                  }
                  disabled={isAdmin}
                >
                  <MenuItem value="" disabled>
                    {t("pleaseSelect")}
                  </MenuItem>
                  {(isAdmin ? articleCategoriesAdmin : articleCategoriesSecretary)
                    .map((item: any) => (
                      <MenuItem key={item.id} value={item.name}>
                        {t(item.name)}
                      </MenuItem>
                    ))}
                </Select>
                {formErrors.category && (
                  <FormHelperText>{formErrors.category}</FormHelperText>
                )}
              </FormControl>
            </Grid>
            <Grid item xs={12} sm={4}>
              <Typography sx={labelStyle}>
                {t("subCategory")}
              </Typography>
            </Grid>
            <Grid item xs={12} sm={8}>
              <FormControl
                fullWidth
                required
                error={!!formErrors.subCategory}
              >
                <Select
                  value={formData.subCategory}
                  displayEmpty
                  required
                  name="subCategory"
                  size={"small"}
                  //disabled={isLoadingAddress}
                  onChange={(e) => {
                    //setCategory(e.target.value);
                    setFormData((prevState) => ({
                      ...prevState,
                      subCategory: e.target.value,
                    }));
                    setFormErrors((prev) => ({
                      ...prev,
                      subCategory: "",
                    }));
                  }}
                  variant={'outlined'}
                  renderValue={(selected) =>
                    selected
                      ? mainCategories.find(
                        (cat: any) => cat.id === (selected)
                      )?.categoryNameEn
                      : t("selectPlaceholder")
                  }
                  disabled={isCategoryLoading}
                >
                  <MenuItem value="" disabled>
                    {t("pleaseSelect")}
                  </MenuItem>
                  {mainCategories
                    .map((item: any) => (
                      <MenuItem key={item.id} value={item.id}>
                        {item.categoryNameEn}
                      </MenuItem>
                    ))}
                </Select>
                {formErrors.subCategory && (
                  <FormHelperText>{formErrors.subCategory}</FormHelperText>
                )}
              </FormControl>
            </Grid>
          </Grid>
        </Box>
        <Box
          sx={{
            borderRadius: 2.5,
            backgroundColor: "#fff",
            border: "1px solid #D9D9D9",
            //flex: 5,
            //display: "inline",
            px: 2,
            py: 2,
            mb: 1,
          }}
        >
          <Typography sx={headerStyle}>
            {t("articleLaunchDate")}
          </Typography>
          <Grid container spacing={2} sx={{ mt: 1 }}>
            <Grid item xs={12} sm={4}>
              <Typography sx={labelStyle}>
                {t("publishStartDate")}
              </Typography>
            </Grid>
            <Grid item xs={12} sm={8}>
              <DatePickerController
                control={control}
                onChange={(date) => setPublishStartDate(date)}
                name="publishStartDate"
                sx={{ maxWidth: "160px" }}
              />

            </Grid>
            <Grid item xs={12} sm={4}>
              <Typography sx={labelStyle}>
                {t("publishStartTime")}
              </Typography>
            </Grid>
            <Grid item xs={12} sm={8}>
              <TimePickerController
                name="publishStartTime"
                control={control}
                onChange={(time) => {
                  if(time!=null && time.split(":").length == 2){
                    time = `${time}:00`
                  }
                  setPublishStartTime(time)
                }}
                sx={{
                  width: "140px",
                }}
                disabled={false}
              />
            </Grid>
            <Grid item xs={12} sm={4}>
              <Typography sx={labelStyle}>
                {t("publishEndDate")}
              </Typography>
            </Grid>
            <Grid item xs={12} sm={8}>
              <DatePickerController
                control={control}
                onChange={(date) => setPublishEndDate(date)}
                name="publishEndDate"
                sx={{ maxWidth: "160px" }}
              />
            </Grid>
            <Grid item xs={12} sm={4}>
              <Typography sx={labelStyle}>
                {t("publishEndTime")}
              </Typography>
            </Grid>
            <Grid item xs={12} sm={8}>
              <TimePickerController
                name="publishEndTime"
                control={control}
                onChange={(time) => {
                  if(time!=null && time.split(":").length == 2){
                    time = `${time}:00`
                  }
                  setPublishEndTime(time)
                }}
                sx={{
                  width: "140px",
                }}
                disabled={false}
              />
            </Grid>
          </Grid>
        </Box>
        <Box
          sx={{
            borderRadius: 2.5,
            backgroundColor: "#fff",
            border: "1px solid #D9D9D9",
            //flex: 5,
            //display: "inline",
            px: 2,
            py: 2,
            mb: 1,
          }}
        >
          <Typography sx={headerStyle}>
            {t("articleDescription")}
          </Typography>
          <Grid container spacing={2} sx={{ mt: 1 }}>
            <Grid item xs={12} sm={4}>
              <Typography sx={labelStyle}>
                {t("articleDescription")}
              </Typography>
            </Grid>
            <Grid item xs={12} sm={8}>
              <TextField
                size={"small"}
                fullWidth
                required
                multiline
                rows={20}
                name="description"
                value={formData.description}
                error={!!formErrors.description}
                onChange={handleInputChange}
              />
            </Grid>
            {articleId!="0" ? <>
            <Grid item xs={12} sm={4}>
              <Typography sx={labelStyle}>
                {t("uploadMedia")}
              </Typography>
            </Grid>
            <Grid item xs={12} sm={8}>
              <FileUploader
                postingMediaId={articleId}
                type={DocumentUploadType.POSTING_ARTICLE_MEDIA}
                //hasOldFiles={handleOldUploadedFiles}
                sxContainer={{
                  border: "2px dashed #ccc",
                  background: "#fff",
                  mb: 3,
                }}
                maxFileSize={25 * 1024 * 1024}
                validTypes={[
                  "image/png",
                  "image/jpeg",
                  "image/jpg",
                ]}
              />
            </Grid></> : <></>}
            {isAdmin ? (<>
              <Grid item xs={12} sm={4}>
                <Typography sx={labelStyle}>
                  {t("urgent")}
                </Typography>
              </Grid>
              <Grid item xs={12} sm={8}>
                <Checkbox
                  checked={formData.urgencyCategory==="URGENT"}
                  onChange={(e) => {
                    setFormData(prevState => ({
                      ...prevState,
                      urgencyCategory: e.target.checked ? "URGENT" : "GENERAL"
                    }));
                  }}
                  sx={{ p: 0, ml: 0, mr: 0, mt: 2 }}
                />
              </Grid></>) : <></>}
            <Grid
              item
              xs={12}
              sx={{
                mt: 2,
                display: "flex",
                flexDirection: "row",
                justifyContent: "flex-end",
                gap: 1,
              }}
            >
              <ButtonOutline
                sx={{
                  bgcolor: "white",
                  "&:hover": { bgcolor: "white" },
                  width: "auto",
                }}
                disabled={isLoadingCreate || isLoadingEdit}
                onClick={handleSaveDraft}
              >
                {t("draft")}
              </ButtonOutline>
              <ButtonPrimary
                variant="contained"
                sx={{
                  width: "auto",
                }}
                onClick={() => navigate(`../preview/${articleId}`)}
                disabled={articleId === "0" || isLoadingCreate || isLoadingEdit}
              >
                {t("next")}
              </ButtonPrimary>
            </Grid>
          </Grid>
        </Box>
      </Box>
      <DialogConfirmation
        open={openModal}
        onClose={() => {
          setOpenModal(false);
        }}
        onAction={handleSave}
        isMutating={false}
        onConfirmationText={t("publishArticle")}
      />
    </>)
}

export default CreateArticle
