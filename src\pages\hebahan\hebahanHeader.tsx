import React, {useState} from "react";
import {Box, IconButton, InputAdornment, Stack, TextField, Typography} from "@mui/material";
import {Search} from "@mui/icons-material";
import {useNavigate} from "react-router-dom";
import HebahanBreadcrumb from "@/pages/hebahan/breadcrumb";


const InternalHebahanHeader: React.FC = () => {
  const navigate = useNavigate();
  const [searchTerm, setSearchTerm] = useState("");


  return (
    <>
      <HebahanBreadcrumb/>
      <Box
        sx={{
          borderRadius: 2.5,
          backgroundColor: "#fff",
          //display: "inline",
          px: 2,
          mb: 1,
        }}
      >

      </Box>
    </>
  );
}

export default InternalHebahanHeader;
