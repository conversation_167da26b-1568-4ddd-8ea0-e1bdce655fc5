import React, {useState} from "react";
import {Container, Typography} from "@mui/material";
import Box from "@mui/material/Box";
import {DurationIcon} from "@/components/icons/duration";
import CertificateInfo from "@/pages/training/certificateDetails/certificateInfo";
import {useTranslation} from "react-i18next";
import {ButtonPrimary} from "@/components";
import TrainingQuiz from "@/pages/training/trainingDetails/trainingQuiz";
import FeedBackFragment from "@/pages/training/trainingDetails/feedBackFragment";
import ReactPlayer from 'react-player';
import {formatDate} from "@/helpers";

interface TrainingInfoFragmentProps {
  course: any;
  item: any[],
  quiz?: boolean,
  handleNext: (currentPage: number) => void,
  isReview?: boolean,
  answerScheme?: boolean
}

interface resultProps {
  score: number,
  passed: boolean,
  time: string,
  attemptId: string,
  quizFinished: boolean,
}

const TrainingInfoFragment: React.FC<TrainingInfoFragmentProps> = ({
                                                                     course,
                                                                     item,
                                                                     quiz = false,
                                                                     handleNext,
                                                                     isReview = false,
                                                                     answerScheme = false
                                                                   }) => {

  const {t, i18n} = useTranslation();

  const [page, setPage] = useState(0);
  const [result, setResult] = useState<resultProps>({
    score: 0,
    passed: false,
    time: "",
    attemptId: "0",
    quizFinished: false,
  })


  const goNext = (currentPage: number) => {
    console.log(currentPage, item.length + 2);
    if (currentPage < item.length + 2) {
      handleNext(currentPage + 1);
      setPage(currentPage + 1);
    }
  }

  const hour = Math.floor(course.duration / 60);
  const minute = course.duration % 60;

  const handleFinish = (submitData: any) => {
    console.log("submitData", submitData);
    //const startTimeString = formatDate(submitData.startTime,"hh:mm:ss");
    //const endTimeString = formatDate(submitData.endTime,"hh:mm:ss");
    const hour = (submitData.endTime[3] - submitData.startTime[3]) * 3600;
    const minute = (submitData.endTime[4] - submitData.startTime[4]) * 60;
    const second = submitData.endTime[5] - submitData.startTime[5];
    const totalTime = (hour + minute + second);
    const formattedTotalTime = formatTime(totalTime)
    //console.log("totalTime",totalTime,formattedTotalTime);
    setResult({
      score: submitData.score,
      passed: submitData.passed,
      time: formattedTotalTime,
      attemptId: submitData.id,
      quizFinished: submitData.passed,
    })
    setPage(page + 1);
    handleNext(page + 1);
  }

  const formatTime = (seconds: number) => {
    return [
      Math.floor(seconds / 60 / 60),
      Math.floor(seconds / 60 % 60),
      Math.floor(seconds % 60)
    ]
      .join(":")
      .replace(/\b(\d)\b/g, "0$1")
  }

  //console.log("item",item);
  //console.log("course",course);
  return (
    <>
      {answerScheme ? <TrainingQuiz handleFinish={handleFinish} attemptId={course.latestQuizAttemptId}
                                    courseId={course.trainingCourseId ?? course.id} submitted={answerScheme}/> :
        quiz && !answerScheme && page === item.length && !result.quizFinished ?
          <TrainingQuiz handleFinish={handleFinish} attemptId={"0"} courseId={course.trainingCourseId ?? course.id}
                        submitted={answerScheme}/> :
          <Box
            sx={{
              //flex: 5,
              width: "100%",
              borderRadius: 2.5,
              backgroundColor: "#fff",
              //flex: 5,
              //display: "inline",
              px: 2,
              py: 2,
              mb: 1,
            }}
          >
            {page === item.length + 2 ?
              <FeedBackFragment handleNext={handleNext} isAdmin={false} quizAttemptId={result.attemptId}/> :
              <Box
                sx={{
                  height: "100%",
                  borderRadius: 2.5,
                  backgroundColor: "#fff",
                  border: "1px solid #D9D9D9",
                  //flex: 5,
                  px: 5,
                  py: 2,
                  mb: 1,
                }}
              >
                <Typography
                  sx={{
                    color: "#666666",
                    //pt: 3,
                    fontWeight: "500",
                    fontSize: 20,
                  }}
                >
                  {course.title}
                </Typography>
                <Box sx={{
                  p: 0,
                  m: 0,
                  height: 30,
                  display: "flex",
                  flexDirection: "row",
                }}>
                  <Box
                    sx={{py: 1, px: 1}}
                  >
                    <DurationIcon/>
                  </Box>
                  <Box
                    sx={{py: 1, px: 0}}
                  >
                    <Typography
                      sx={{
                        color: "#666666",
                        lineHeight: "18px",
                        fontWeight: "400",
                        fontSize: 12,
                      }}
                    >
                      {`${t("timeUnitHour", {
                        count: hour,
                      })} ${t("timeUnitMinute", {
                        count: minute,
                      })}`}
                    </Typography>
                  </Box>
                </Box>
                {(page === 0 && isReview && !answerScheme) ? <>
                  <Box sx={{
                    p: 0,
                    m: 0,
                    height: 30,
                    display: "flex",
                    flexDirection: "row",
                  }}>
                    <Box
                      sx={{py: 1, px: 1}}
                    >
                      <Typography
                        sx={{
                          color: "#666666",
                          lineHeight: "18px",
                          fontWeight: "400",
                          fontSize: 12,
                        }}
                      >
                        {`Status: ${t("success")}`}
                      </Typography>
                    </Box>
                    <Box
                      sx={{py: 1, px: 1}}
                    >
                      <Typography
                        sx={{
                          color: "#666666",
                          lineHeight: "18px",
                          fontWeight: "400",
                          fontSize: 12,
                        }}
                      >
                        {`${result.score} %`}
                      </Typography>
                    </Box>
                    <Box
                      sx={{py: 1, px: 0}}
                    >
                      <Typography
                        sx={{
                          color: "#666666",
                          lineHeight: "18px",
                          fontWeight: "400",
                          fontSize: 12,
                        }}
                      >
                        {`${t("timeUnitHour", {
                          count: hour,
                        })} ${t("timeUnitMinute", {
                          count: minute,
                        })}`}
                      </Typography>
                    </Box>
                  </Box>
                </> : <></>
                }
                {page < item.length && !answerScheme ?
                  <>
                    <Box sx={{}}>
                      {item[page].materialType === "VIDEO" ?
                        <Container>
                          <ReactPlayer
                            src={item[page].media}
                            controls
                            width="100%"
                            height="500px"
                          /> </Container> :
                        <img
                          src={item[page].media ?? '/latihanSample/images5.jpg'}
                          alt="Logo"
                          style={{
                            //height: "80px", // Default height
                            //marginRight: "10px",
                            height: "500px",
                            width: "100%",
                            objectFit: "cover",
                          }}
                          className="responsive-logo"
                        />}
                    </Box>
                    <Box sx={{mt: 5}}>
                      <Typography
                        sx={{
                          color: "#666666",
                          lineHeight: "18px",
                          fontWeight: "700",
                          fontSize: 20,
                        }}
                      >
                        {t("articleDescription")}
                      </Typography>
                      <Typography
                        sx={{
                          mt: 5,
                          color: "#666666",
                          lineHeight: "18px",
                          fontWeight: "400",
                          fontSize: 14,
                        }}
                      >
                        {item[page].description}
                      </Typography>
                    </Box>
                  </> : <></>}
                {page == item.length + 1 && !answerScheme ?
                  <>
                    <Box sx={{height: "85%", display: "flex", flexDirection: "column", justifyContent: "center"}}>
                      <Box>
                        <Typography
                          sx={{
                            color: result.passed ? "#84D819" : "#FF0000",
                            fontWeight: "500",
                            fontSize: 45,
                            textAlign: "center",
                          }}
                        >
                          {`${result.score} %`}
                        </Typography>
                      </Box>
                      <Box>
                        <Typography
                          sx={{
                            color: "#0CA6A6",
                            fontWeight: "400",
                            fontSize: 14,
                            textAlign: "center"
                          }}
                        >
                          {course.title}
                        </Typography>
                      </Box>
                      <Box>
                        <Typography
                          sx={{
                            color: "#55556D",
                            fontWeight: "400",
                            fontSize: 14,
                            textAlign: "center"
                          }}
                        >
                          {result.passed ? t("trainingPassedMsg"):t("trainingFailedMsg")}
                        </Typography>
                      </Box>
                      <Box sx={{display: "flex", mt: 1, flexDirection: "row", justifyContent: "center", gap: 3}}>
                        <Typography
                          sx={{
                            color: "#666666",
                            fontWeight: "500",
                            fontSize: 14,
                            textAlign: "center"
                          }}
                        >
                          {`${t("time")}: ${result.time}`}
                        </Typography>
                        <Typography
                          sx={{
                            color: "#666666",
                            fontWeight: "500",
                            fontSize: 14,
                            textAlign: "center"
                          }}
                        >
                          {`${t("pointsGained")} + ${result.passed ? '1' : '0'} Pts`}
                        </Typography>
                      </Box>
                    </Box>
                  </> : <></>}
                {page !== item.length + 2 && !answerScheme ?
                  <Box sx={{display: "flex", mt: 1, justifyContent: "flex-end"}}>
                    <ButtonPrimary
                      variant="outlined"
                      sx={{
                        borderColor: "#0CA6A6",
                        bgcolor: "#0CA6A6",
                        "&:hover": {bgcolor: "#0CA6A6", borderColor: "#0CA6A6",},
                        color: "#fff",
                        fontWeight: "400",
                      }}
                      //disabed={}}
                      onClick={() => goNext(page)}
                    >
                      {t("next")}
                    </ButtonPrimary>
                  </Box> : <></>}
              </Box>}
          </Box>}
    </>
  );
}

export default TrainingInfoFragment;
