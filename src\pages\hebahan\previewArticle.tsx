import {Box, Grid, IconButton, Slide, Typography} from "@mui/material";
import {t} from "i18next";
import {headerStyle} from "./hebahanConstant";
import InternalHebahanHeader from "./hebahanHeader";
import {API_URL} from "@/api";
import {useCustom, useCustomMutation} from "@refinedev/core";
import {useNavigate, useParams} from "react-router-dom";
import React, {useEffect, useState} from "react";
import {DocumentUploadType} from "@/helpers";
import {EditIcon, TrashIcon} from "@/components/icons";
import {ButtonPrimary, DialogConfirmation} from "@/components";
import {useTranslation} from "react-i18next";


export interface SlideData {
  background: string;
}

interface PreviewArticleProps {
  isAdmin: boolean
}

const PreviewArticle: React.FC<PreviewArticleProps> = ({isAdmin}) => {

  const { t, i18n } = useTranslation();

  const {id} = useParams<{ id: string }>()
  const articleId = (id ?? "0")
  const [currentIndex, setCurrentIndex] = useState<number>(0);
  const [isContentVisible, setIsContentVisible] = useState(false);
  const [openModal, setOpenModal] = useState(false);
  const [article, setArticle] = useState({
    title: "",
    description: "",
    status: ""
  });

  const [slideComponents, setSlideComponents] = useState<SlideData[]>([]);

  const navigate = useNavigate();


  const {data: articleData, isLoading: isArticleLoading} = useCustom({
    url: `${API_URL}/society/posting/${articleId}`,
    method: "get",
    config: {
      headers: {
        portal: localStorage.getItem("portal"),
        authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
      },
    },
    queryOptions: {
      enabled: articleId != "0",
      retry: false,
      cacheTime: 0,
    },
  });

  const articleDetails = articleData?.data?.data || {};
  console.log("articleDetails", articleData)


  const {data: articleDocData, isLoading: isArticleDocLoading} = useCustom({
    url: `${API_URL}/document/documentByParam`,
    method: "get",
    config: {
      headers: {
        portal: localStorage.getItem("portal"),
        authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
      },
      query: {
        postingMediaId: articleId,
        type: DocumentUploadType.POSTING_ARTICLE_MEDIA
      },
    },
    queryOptions: {
      enabled: articleId != "0",
      retry: false,
      cacheTime: 0,
    },
  });

  const articleDoc = articleDocData?.data?.data || [];
  console.log("trainingDoc", articleDocData)

  //const slideComponents: SlideData[] = []

  useEffect(() => {
    setArticle(articleDetails)
  }, [articleData])

  useEffect(() => {
    if (articleDoc.length > 0) {
      const urls = articleDoc.map((a: any) => {
        return a.url;
      })
      setSlideComponents(urls);
    }

  }, [articleDocData])

  const handleSave = () => {
    //console.log("articleId",articleId);
    if (articleId != "0") Publish("PUBLISHED");
  }

  const {mutate: publish, isLoading: isLoadingCreate} = useCustomMutation();
  const Publish = (status: string): void => {
    const currentDate = new Date();
    const formattedDate = currentDate.toISOString().split("T")[0];
    publish(
      {
        url:  isAdmin ? `${API_URL}/society/admin/posting/${articleId}/publish` : `${API_URL}/society/secretary/posting/${articleId}/publish`,
        method: "put",
        values: {},
        config: {
          headers: {
            "Content-Type": "application/json",
            portal: localStorage.getItem("portal") || "",
            authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
          },
        },
        successNotification: (data) => {
          if (data?.data?.data) {
            console.log("Create", data?.data?.data);
            article.status = "PUBLISHED";
            setArticle(article);
            setOpenModal(false)
            return {
              message: data?.data?.msg,
              type: "success",
            };
          } else {
            return {
              message: t("error") + data?.data?.msg,
              type: "error",
            };
          }
        },
        errorNotification: (data) => {
          return {
            message: data?.response?.data?.msg,
            type: "error",
          };
        },
      },
      {
        onError(error, variables, context) {
          console.log(error);
        },
      }
    );
  };

  useEffect(() => {
    const timer = setTimeout(() => {
      const nextIndex = (currentIndex + 1) % slideComponents.length;
      setCurrentIndex(nextIndex);
    }, 5000);

    return () => clearTimeout(timer); // Cleanup timer on unmount
  }, [currentIndex]);



  const { mutate: deletePosting, isLoading: isLoadingDelete } = useCustomMutation();
  const Delete = (): void => {
    deletePosting(
      {
        url: `${API_URL}/society/admin/posting/${articleId}`,
        method: "delete",
        values: {},
        config: {
          headers: {
            "Content-Type": "application/json",
            portal: localStorage.getItem("portal") || "",
            authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
          },
        },
        successNotification: (data) => {
          if (data?.data?.data) {
            setOpenModal(false);
            return {
              message: data?.data?.msg,
              type: "success",
            };
          } else {
            return {
              message: t("error") + data?.data?.msg,
              type: "error",
            };
          }
        },
        errorNotification: (data) => {
          return {
            message: data?.response?.data?.msg,
            type: "error",
          };
        },
      },
      {
        onError(error, variables, context) {
          console.log(error);
        },
      }
    );
  };

  return (<>
    <InternalHebahanHeader/>
    <Box
      sx={{
        borderRadius: 2.5,
        backgroundColor: "#fff",
        //display: "inline",
        px: 2,
        py: 2,
        mb: 1,
      }}
    >
      <Box
        sx={{
          borderRadius: 2.5,
          backgroundColor: "#fff",
          border: "1px solid #D9D9D9",
          //flex: 5,
          //display: "inline",
          px: 2,
          py: 2,
          mb: 1,
        }}
      >
        <Typography sx={headerStyle}>
          {t("previewArticle")}
        </Typography>
        <Box sx={{
          display: "flex", gap: 2,
        }}>
          <Box sx={{width: "75%", pt: 3, mt: 3}}>
            <Box
              sx={{
                borderRadius: 2.5,
                backgroundColor: "#fff",
                border: "1px solid #D9D9D9",
                //flex: 5,
                //display: "inline",
                px: 5,
                py: 5,
                mb: 1,
              }}
            >
              <Typography sx={{fontWeight: 500, fontSize: 35}}>
                {article.title}
              </Typography>
              {slideComponents.length > 0 ?
                <Box
                  sx={{
                    height: "300px",
                    backgroundImage: `url(${slideComponents[currentIndex]})`,
                    backgroundSize: "cover",
                    backgroundRepeat: "no-repeat",
                    backgroundPosition: "center center",
                    justifyContent: "center",
                    overflow: "hidden",
                    "@media screen and (-webkit-min-device-pixel-ratio: 1.5) and (-webkit-max-device-pixel-ratio: 1.75), screen and (min-resolution: 144dpi) and (max-resolution: 168dpi)":
                      {
                        maxHeight: "600px",
                      },
                  }}
                >
                  {slideComponents.length > 1 && slideComponents.map((slide, index) => (
                    <Slide
                      style={{overflow: "hidden"}}
                      key={index}
                      direction={"left"}
                      in={currentIndex === index}
                      mountOnEnter
                      unmountOnExit
                      timeout={500}
                      onEntered={() => setIsContentVisible(true)} // Trigger fade-in after slide transition
                      onExit={() => setIsContentVisible(false)}
                    >
                      <Box
                        sx={{
                          height: "100%",
                          zIndex: 0,
                          backgroundImage: `url(${slide})`,
                          backgroundSize: "cover",
                          backgroundRepeat: "no-repeat",
                          backgroundPosition: "center center",
                          justifyContent: "center",
                          display: "flex",
                        }}
                      >
                      </Box>
                    </Slide>
                  ))}
                </Box> : <></>}
              <Box sx={{pt: 5}}>
                <Box sx={{pt: 5}}>
                  {/*<Typography
                    component="div"
                    dangerouslySetInnerHTML={{
                      __html: `<div class="ql-editor">${article.description}</div>`,
                    }}
                    sx={{fontWeight: 400, fontSize: 14}} />*/}
                  {article.description.split("\n").map((line, index) => (
                    <Typography mb={"16px"} color="text.secondary" textAlign="justify">
                      {line}
                    </Typography>
                  ))}
                </Box>
              </Box>
            </Box>
            {article.status !== "PUBLISHED" ?
              <Box>
                <Grid container>
                  <Grid
                    item
                    xs={12}
                    sx={{
                      mt: 2,
                      display: "flex",
                      flexDirection: "row",
                      justifyContent: "flex-end",
                      gap: 1,
                    }}
                  >
                    <ButtonPrimary
                      variant="contained"
                      sx={{
                        width: "auto",
                      }}
                      onClick={() => setOpenModal(true)}
                      disabled={articleId === "0" || isLoadingCreate}
                    >
                      {isAdmin? t("publish") : t("send")}
                    </ButtonPrimary>
                  </Grid>
                </Grid>
              </Box> : <></>}
          </Box>
          <Box sx={{width: "25%",pt:3,mt:3}}>
            <Box
              sx={{
                borderRadius: 2.5,
                backgroundColor: "#fff",
                border: "1px solid #D9D9D9",
                //flex: 5,
                //display: "inline",
                px: 2,
                py: 2,
                mb: 1,
              }}
            >
              <Box
                sx={{
                  display: "flex",
                  justifyContent: "center",
                  alignItems: "center",
                  columnGap: "0.5rem",
                }}
              >
                <Box
                  sx={{
                    //display:"block",
                    border: "1px solid #FFD100",
                    borderRadius: 2.5,
                    margin: "auto",
                    //ml:1,
                    flex: 1,
                    width: "100%",
                    py: 1,
                    //px: 2
                  }}
                >
                  <Typography
                    sx={{
                      color: "#FFD100",
                      lineHeight: "100%",
                      fontWeight: "500",
                      fontSize: 12,
                      textAlign: "center"
                    }}
                  >
                    {t(`${article.status}`)}
                  </Typography>
                </Box>
                {article.status !== "PUBLISHED" ?
                  <>
                    <IconButton
                      style={{
                        minWidth: "2rem",
                        minHeight: "2rem",
                      }}
                      color="primary"
                      onClick={() => {
                        navigate(`../update/${articleId}`)
                      }}
                    >
                      <EditIcon
                        sx={{
                          fontSize: "2rem",
                          width: "1rem",
                          height: "1rem",
                        }}
                      />
                    </IconButton>

                    <IconButton
                      style={{
                        minHeight: "2rem",
                        minWidth: "2rem",
                      }}
                      color="error"
                      onClick={() => setOpenModal(true)}
                    >
                      <TrashIcon/>
                    </IconButton></> : <></>}
              </Box>
            </Box>
          </Box>
        </Box>
      </Box>
    </Box>
    <DialogConfirmation
      open={openModal}
      onClose={() => {
        setOpenModal(false);
      }}
      onAction={handleSave}
      isMutating={false}
      onConfirmationText={isAdmin ? t("publishArticle") : t("publishArticleToAdmin")}
    />
    <DialogConfirmation
      open={openModal}
      onClose={() => {
        setOpenModal(false);
      }}
      onAction={Delete}
      isMutating={false}
      onConfirmationText={t("CONFIRM_DELETE_POSTING")}
    />
  </>);
}

export default PreviewArticle
