name: Deploy Frontend Eroses Dev

on:
  push:
    branches:
      - development

jobs:
  deploy_dev:
    runs-on: ubuntu-latest

    steps:
      - name: Checkout Repository
        uses: actions/checkout@v4

      - name: Set up Node.js
        uses: actions/setup-node@v4
        with:
          # TODO: update to latest LTS version of node.js if possible
          # node-version: lts/*
          node-version: 18
          cache: 'npm'

      - name: Install dependencies
        run: npm ci

      - name: Build the project for development
        env:
          VITE_BACKEND_URL: ${{ secrets.BACKEND_URL_DEV }}
          VITE_APP_ENV: development
          NODE_ENV: production
          VITE_AWS_API_KEY_REGION: 'ap-southeast-5'
          VITE_AWS_API_KEY: ${{ secrets.MAP_API_KEY_DEV }}
          VITE_AWS_PLACE_INDEX: 'explore.place.Grab'
          VITE_AWS_MAP_NAME: 'explore.map.Grab'
          VITE_GA_MEASUREMENT_ID: ${{ secrets.GA_MEASUREMENT_ID }}
          NODE_OPTIONS: --max-old-space-size=4096
        run: npm run build

      - name: Configure AWS Credentials for Development
        uses: aws-actions/configure-aws-credentials@v4
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID_DEV }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY_DEV }}
          aws-region: ap-southeast-5

      - name: Deploy to S3 with Enhanced Caching Strategy
        run: |
          # Make deployment script executable
          chmod +x deploy.sh

          # Deploy with proper cache control headers
          ./deploy.sh ./dist eroses-frontend-cicd ${{ secrets.CLOUDFRONT_DISTRIBUTION_ID_DEV }}
        env:
          VERIFY_DEPLOYMENT: 'true'
