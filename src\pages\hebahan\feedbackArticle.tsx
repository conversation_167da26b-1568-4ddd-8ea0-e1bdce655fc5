import {Box, FormHelperText, Grid, Slide, TextField, Typography} from "@mui/material";
import {t} from "i18next";
import InternalHebahanHeader from "./hebahanHeader";
import {API_URL} from "@/api";
import {useCustom, useCustomMutation} from "@refinedev/core";
import {useNavigate, useParams} from "react-router-dom";
import React, {useEffect, useState} from "react";
import {DocumentUploadType} from "@/helpers";
import {ButtonPrimary, DialogConfirmation} from "@/components";
import {borderStyle, headerStyle, labelStyle} from "@/pages/internal-training/trainingConstant";
import Checkbox from "@mui/material/Checkbox";
import InputLabel from "@mui/material/InputLabel";
import {SlideData} from "@/pages/hebahan/previewArticle";
import {useTranslation} from "react-i18next";

const FeedbackArticle: React.FC = () => {

  const { t, i18n } = useTranslation();

  const { id } = useParams<{ id: string }>()
  const articleId = (id ?? "0")
  const [currentIndex, setCurrentIndex] = useState<number>(0);
  const [isContentVisible, setIsContentVisible] = useState(false);
  const [isChecked, setIsChecked] = useState(false);
  const [isApproved, setIsApproved] = useState(0);
  const [response, setResponse] = useState("");
  const [feedback, setFeedback] = useState("");
  const [openModal, setOpenModal] = useState(false);
  const [article, setArticle] = useState({
    title: "",
    description: "",
    status: "",
    published:false,
  });

  const navigate = useNavigate();

  const [slideComponents, setSlideComponents] = useState<SlideData[]>([]);

  const { data: articleData, isLoading: isArticleLoading } = useCustom({
    url: `${API_URL}/society//admin/posting/${articleId}`,
    method: "get",
    config: {
      headers: {
        portal: localStorage.getItem("portal"),
        authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
      },
    },
    queryOptions: {
      enabled: articleId != "0",
      retry: false,
      cacheTime: 0,
    },
  });

  const articleDetails = articleData?.data?.data || {};
  console.log("articleDetails", articleData)


  const { data: articleDocData, isLoading: isArticleDocLoading } = useCustom({
    url: `${API_URL}/document/documentByParam`,
    method: "get",
    config: {
      headers: {
        portal: localStorage.getItem("portal"),
        authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
      },
      query: {
        postingMediaId: articleId,
        type: DocumentUploadType.POSTING_ARTICLE_MEDIA
      },
    },
    queryOptions: {
      enabled: articleId != "0",
      retry: false,
      cacheTime: 0,
    },
  });

  const articleDoc = articleDocData?.data?.data || [];
  console.log("trainingDoc", articleDocData)



  useEffect(() => {
    setArticle(articleDetails)
  }, [articleData])

  useEffect(() => {
    if (articleDoc.length > 0) {
      const urls = articleDoc.map((a: any) => {
        return a.url;
      })
      setSlideComponents(urls);
    }
  }, [articleDocData])

  const handleSave = () => {
    if(isChecked){
      Publish();
    }
  }

  const { mutate: publish, isLoading: isLoadingCreate } = useCustomMutation();
  const Publish = (): void => {
    publish(
      {
        url: `${API_URL}/society/secretary/posting/review`,
        method: "post",
        values: {
          postingId : articleId,
          responseStatus : isApproved ? "YA" : "TIDAK",
          rejectionComment: response!="others" ? response : feedback
        },
        config: {
          headers: {
            "Content-Type": "application/json",
            portal: localStorage.getItem("portal") || "",
            authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
          },
        },
        successNotification: (data) => {
          if (data?.data?.data) {
            console.log("Create", data?.data?.data);
            setOpenModal(false)
            return {
              message: data?.data?.msg,
              type: "success",
            };
          } else {
            return {
              message: t("error") + data?.data?.msg,
              type: "error",
            };
          }
        },
        errorNotification: (data) => {
          return {
            message: data?.response?.data?.msg,
            type: "error",
          };
        },
      },
      {
        onError(error, variables, context) {
          console.log(error);
        },
      }
    );
  };

  const handleCheckboxChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setIsChecked(e.target.checked ? true : false);
  }

  const handleApprove = (e: React.ChangeEvent<HTMLInputElement>) => {
    setIsApproved(e.target.checked ? 1 : 0);
  }

  const handleNotApprove = (e: React.ChangeEvent<HTMLInputElement>) => {
    setIsApproved(e.target.checked ? 2 : 0);
  }

  useEffect(() => {
    const timer = setTimeout(() => {
      const nextIndex = (currentIndex + 1) % slideComponents.length;
      setCurrentIndex(nextIndex);
    }, 5000);

    return () => clearTimeout(timer); // Cleanup timer on unmount
  }, [currentIndex]);

  return (<>
    <Box
      sx={{
        borderRadius: 2.5,
        backgroundColor: "#fff",
        //display: "inline",
        px: 2,
        py: 2,
        mb: 1,
      }}
    >
      <Box
        sx={{
          borderRadius: 2.5,
          backgroundColor: "#fff",
          border: "1px solid #D9D9D9",
          //flex: 5,
          //display: "inline",
          px: 2,
          py: 2,
          mb: 1,
        }}
      >
        <Typography sx={headerStyle}>
          {t("previewArticle")}
        </Typography>
        <Box sx={{
          display: "flex", gap: 2,
        }}>
          <Box sx={{ width: "75%",pt:3,mt:3 }}>
            <Box
              sx={{
                borderRadius: 2.5,
                backgroundColor: "#fff",
                border: "1px solid #D9D9D9",
                boxShadow: 3,
                //flex: 5,
                //display: "inline",
                px: 2,
                py: 2,
                mb: 1,
              }}
            >
              <Typography sx={{ fontWeight: 500, fontSize: 35 }}>
                {article.title}
              </Typography>
              {slideComponents.length > 0 ?
                <Box
                  sx={{
                    height: "300px",
                    backgroundImage: `url(${slideComponents[currentIndex]})`,
                    backgroundSize: "cover",
                    backgroundRepeat: "no-repeat",
                    backgroundPosition: "center center",
                    justifyContent: "center",
                    overflow: "hidden",
                    "@media screen and (-webkit-min-device-pixel-ratio: 1.5) and (-webkit-max-device-pixel-ratio: 1.75), screen and (min-resolution: 144dpi) and (max-resolution: 168dpi)":
                      {
                        maxHeight: "600px",
                      },
                  }}
                >
                  {slideComponents.length > 1 && slideComponents.map((slide, index) => (
                    <Slide
                      style={{ overflow: "hidden" }}
                      key={index}
                      direction={"left"}
                      in={currentIndex === index}
                      mountOnEnter
                      unmountOnExit
                      timeout={500}
                      onEntered={() => setIsContentVisible(true)} // Trigger fade-in after slide transition
                      onExit={() => setIsContentVisible(false)}
                    >
                      <Box
                        sx={{
                          height: "100%",
                          zIndex: 0,
                          backgroundImage: `url(${slide})`,
                          backgroundSize: "cover",
                          backgroundRepeat: "no-repeat",
                          backgroundPosition: "center center",
                          justifyContent: "center",
                          display: "flex",
                        }}
                      >
                      </Box>
                    </Slide>
                  ))}
                </Box> : <></>}
              <Box sx={{pt: 5}}>
                {/*<Typography
                    component="div"
                    dangerouslySetInnerHTML={{
                      __html: `<div class="ql-editor">${article.description}</div>`,
                    }}
                    sx={{fontWeight: 400, fontSize: 14}} />*/}
                {article.description.split("\n").map((line, index) => (
                  <Typography mb={"16px"} color="text.secondary" textAlign="justify">
                    {line}
                  </Typography>
                ))}
              </Box>
            </Box>
          </Box>
          <Box sx={{ width: "25%",pt:3,mt:3 }}>
            <Box
              sx={{
                borderRadius: 2.5,
                backgroundColor: "#fff",
                border: "1px solid #D9D9D9",
                boxShadow: 3,
                //flex: 5,
                //display: "inline",
                px: 2,
                py: 2,
                mb: 1,
              }}
            >
              <Box
                sx={{
                  display: "flex",
                  justifyContent: "center",
                  alignItems: "center",
                  columnGap: "0.5rem",
                }}
              >
                <Box
                  sx={{
                    //display:"block",
                    border: "1px solid #FFD100",
                    borderRadius: 2.5,
                    margin: "auto",
                    //ml:1,
                    flex: 1,
                    width: "100%",
                    py: 1,
                    //px: 2
                  }}
                >
                  <Typography
                    sx={{
                      color: "#FFD100",
                      lineHeight: "100%",
                      fontWeight: "500",
                      fontSize: 12,
                      textAlign: "center"
                    }}
                  >
                    {t(`${article.status}`)}
                  </Typography>
                </Box>

              </Box>
            </Box>
          </Box>
        </Box>
      </Box>
    </Box>
    <Box
      sx={{
        borderRadius: 2.5,
        backgroundColor: "#fff",
        //display: "inline",
        px: 2,
        py: 2,
        mb: 1,
      }}
    >
      <Box
        sx={borderStyle}
      >
        <Typography
          sx={headerStyle}
        >
          {t("committeeResult")}
        </Typography>
        <Grid container spacing={2} sx={{ mt: 1 }}>
          <Grid item xs={12} sm={4}>
            <Typography sx={labelStyle}>
              {t("articleApproval")}
            </Typography>
            {(isApproved!=0) ? (
              <FormHelperText sx={{ color: "red" }}>
                {t("fieldRequired")}
              </FormHelperText>
            ) : null}
          </Grid>
          <Grid item xs={12} sm={8}>
            <Box
              sx={{
                display: "flex",
                alignItems: "flex-start",
                gap: 1,
              }}
            >
              <Checkbox
                id="approveCheckbox"
                checked={isApproved===1}
                onChange={handleApprove}
                sx={{
                  color: "#00A7A7",
                  "&.Mui-checked": {
                    color: "#00A7A7",
                  },
                  padding: "0",
                }}
              />
              <InputLabel
                htmlFor="approveCheckbox"
                required
                sx={{
                  color: "#333333",
                  fontSize: 14,
                  fontWeight: 400,
                  lineHeight: 1.4,
                  "& .MuiFormLabel-asterisk": {
                    color: "var(--error)",
                  },
                }}
              >
                {t("verified")}
              </InputLabel>
              <Checkbox
                id="notApprovedCheckbox"
                checked={isApproved===2}
                onChange={handleNotApprove}
                sx={{
                  color: "#00A7A7",
                  "&.Mui-checked": {
                    color: "#00A7A7",
                  },
                  padding: "0",
                }}
              />
              <InputLabel
                htmlFor="notApprovedCheckbox"
                required
                sx={{
                  color: "#333333",
                  fontSize: 14,
                  fontWeight: 400,
                  lineHeight: 1.4,
                  "& .MuiFormLabel-asterisk": {
                    color: "var(--error)",
                  },
                }}
              >
                {t("unverified")}
              </InputLabel>
            </Box>
          </Grid>
        </Grid>
      </Box>
      <Box
        sx={borderStyle}
      >
        <Typography
          sx={headerStyle}
        >
          {t("committeeResponse")}
        </Typography>
        <Grid container spacing={2} sx={{ mt: 1 }}>
          <Grid item xs={12} sm={4}>
            <Typography sx={labelStyle}>
              {t("articleApproval")}
            </Typography>
            {(response!="") ? (
              <FormHelperText sx={{ color: "red" }}>
                {t("fieldRequired")}
              </FormHelperText>
            ) : null}
          </Grid>
          <Grid item xs={12} sm={8}>
            <Box
              sx={{
                display: "flex",
                alignItems: "flex-start",
                gap: 1,
              }}
            >
              <Checkbox
                id="noObjectionCheckbox"
                checked={response==="Tiada Bantahan"}
                onChange={() => setResponse("Tiada Bantahan")}
                sx={{
                  color: "#00A7A7",
                  "&.Mui-checked": {
                    color: "#00A7A7",
                  },
                  padding: "0",
                }}
              />
              <InputLabel
                htmlFor="noObjectionCheckbox"
                required
                sx={{
                  color: "#333333",
                  fontSize: 14,
                  fontWeight: 400,
                  lineHeight: 1.4,
                  "& .MuiFormLabel-asterisk": {
                    color: "var(--error)",
                  },
                }}
              >
                {t("verified")}
              </InputLabel>
              <Checkbox
                id="otherResponseCheckbox"
                checked={(response==="others")}
                onChange={() => setResponse("others")}
                sx={{
                  color: "#00A7A7",
                  "&.Mui-checked": {
                    color: "#00A7A7",
                  },
                  padding: "0",
                }}
              />
              <TextField
                size={"small"}
                fullWidth
                name="feedback"
                value={feedback}
                onChange={(e) => setFeedback(e.target.value)}
              />
            </Box>
          </Grid>
        </Grid>
      </Box>
      <Box
        sx={borderStyle}
      >
        <Typography
          sx={headerStyle}
        >
          {t("informationVerification")}
        </Typography>
        <Grid container spacing={2} sx={{ mt: 1 }}>
          <Grid item xs={12} sm={12}>
            <Box
              sx={{
                display: "flex",
                alignItems: "flex-start",
                gap: 1,
              }}
            >
              <Checkbox
                id="verificationCheckbox"
                checked={isChecked}
                onChange={handleCheckboxChange}
                sx={{
                  color: "#00A7A7",
                  "&.Mui-checked": {
                    color: "#00A7A7",
                  },
                  padding: "0",
                }}
              />
              <InputLabel
                htmlFor="verificationCheckbox"
                required
                sx={{
                  color: "#333333",
                  fontSize: 14,
                  fontWeight: 400,
                  lineHeight: 1.4,
                  "& .MuiFormLabel-asterisk": {
                    color: "var(--error)",
                  },
                }}
              >
                {t("documentInformationIsCorrect")}
              </InputLabel>
              {(!isChecked) ? (
                <FormHelperText sx={{ color: "red" }}>
                  {t("fieldRequired")}
                </FormHelperText>
              ) : null}
            </Box>
          </Grid>
        </Grid>
      </Box>
      {!article.published ?
        <Box>
          <Grid container>
            <Grid
              item
              xs={12}
              sx={{
                mt: 2,
                display: "flex",
                flexDirection: "row",
                justifyContent: "flex-end",
                gap: 1,
              }}
            >
              <ButtonPrimary
                variant="contained"
                sx={{
                  width: "auto",
                }}
                onClick={() => setOpenModal(true)}
                disabled={!articleId}
              >
                {t("send")}
              </ButtonPrimary>
            </Grid>
          </Grid>
        </Box> : <></> }
    </Box>
    <DialogConfirmation
      open={openModal}
      onClose={() => {
        setOpenModal(false);
      }}
      onAction={handleSave}
      isMutating={false}
      onConfirmationText={t("send")}
    />
  </>);
}

export default FeedbackArticle
