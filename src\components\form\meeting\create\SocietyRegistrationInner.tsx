import { TextFieldControllerFormik } from "@/components/input";
import { capitalizeWords, MALAYSIA, MeetingMethods, MeetingTypeOption, useQuery, useThrottle } from "@/helpers";
import { Box, Checkbox, FormControlLabel, Grid, Theme, Typography, useMediaQuery, useTheme } from "@mui/material";
import { Field, FieldProps, Form, useFormikContext } from "formik";
import { useTranslation } from "react-i18next";
import { FormMeetingDateTimeInnerFormik } from "../DateTimeFormik";
import AWSLocationMap, { LocationSelectedParams, OnReactiveSearchOptions } from "@/components/geocoder/geocoder";
import { useEffect, useRef, useState } from "react";
import { IAddressNode } from "@/types";
import { useSelector } from "react-redux";
import { getAddressListsRedux } from "@/redux/addressDataReducer";
import { FormMeetingAttendeesFormikInner } from "../AttendeesFormik";
import { ButtonOutline, ButtonPrimary } from "@/components/button";
import { useCheckAndUpdateRegistration } from "@/helpers/hooks/useCheckAndupdateRegister";
import { useNavigate, useSearchParams } from "react-router-dom";
import { getSocietyDataRedux } from "@/redux/societyDataReducer";
import { FormMeetingAttachmentFormik } from "../AttachmentFormik";
import { SearchPlaceIndexForTextCommand } from "@aws-sdk/client-location";

export interface MeetingCreateSocietyRegistrationCreateOrUpdateBase {
  id: null | string | number;
  meetingType: number
  meetingMethod: number
  platformType: number
  meetingPurpose: string
  meetingPlace: string

  meetingAddress: string
  state: string | number
  district: string | number
  meetingMinute: string

  meetingMinuteAttachment: File | null
}

export interface MeetingCreateSocietyRegistrationCreateOrUpdateFormPayload
  extends MeetingCreateSocietyRegistrationCreateOrUpdateBase {
  agreeToTheStatement: boolean
  /**
   * @todo move to GISInformation
   */
  meetingLocationLatitude: number
  meetingLocationLongitude: number

  // meetingType: number
  // meetingMethod: number
  // platformType: number

  meetingDate: Date | null
  meetingTime: Date | null
  meetingTimeTo: Date | null
}

export interface MeetingCreateSocietyRegistrationCreateOrUpdateResponseBodyGet
  extends MeetingCreateSocietyRegistrationCreateOrUpdateBase {
  GISInformation: string

  // meetingType: string
  // meetingMethod: string
  // platformType: string

  meetingDate: string
  meetingTime: string
  meetingTimeTo: string
}

export interface MeetingCreateSocietyRegistrationCreateOrUpdateRequestBody
  extends MeetingCreateSocietyRegistrationCreateOrUpdateResponseBodyGet {
  societyId: string | number
  society_no: string
}

interface MeetingListResponseBodyGet {
  pid: number
  id: number
  nameBm: string
  nameEn: string
}

export interface FormMeetingCreateSocietyRegistrationInnerProps<
  InitialValue extends MeetingCreateSocietyRegistrationCreateOrUpdateFormPayload = MeetingCreateSocietyRegistrationCreateOrUpdateFormPayload
> {
  initialValue: InitialValue;
}

export const FormMeetingCreateSocietyRegistrationInner = <
  ReqBody extends MeetingCreateSocietyRegistrationCreateOrUpdateFormPayload = MeetingCreateSocietyRegistrationCreateOrUpdateFormPayload,
  MeetingList extends MeetingListResponseBodyGet = MeetingListResponseBodyGet,
  PropType extends FormMeetingCreateSocietyRegistrationInnerProps<ReqBody> = FormMeetingCreateSocietyRegistrationInnerProps<ReqBody>
>({ initialValue }: PropType) => {
  const { t, i18n } = useTranslation();
  const theme = useTheme();
  const navigate = useNavigate();
  const [params] = useSearchParams();
  const encodedId = params.get("id") ?? null;
  const stateRef = useRef<number | string | null>(null);
  const societyDataRedux = useSelector(getSocietyDataRedux);
  const { resetForm, values, setFieldValue, isSubmitting, isValid, validateForm } = useFormikContext<ReqBody>();
  const [reactiveMeetingLocation, setReactiveMeetingLocation] = useState(false);
  const {
    data: meetingListResponse,
    isLoading: isLoadingMeetingList
  } = useQuery<{ data: MeetingList[] }>({
    url: "society/admin/meeting/list",
  });
  const { data: addressListResponse } = useQuery<{ data: IAddressNode[] }>({
    url: `society/admin/address/list`,
  });
  const isMobile = useMediaQuery<Theme>((theme) =>
    theme.breakpoints.down("sm")
  );
  const { fetch: getCheckAndUpdateRegistration } =
    useCheckAndUpdateRegistration({
      id: societyDataRedux?.id ?? null,
      pageNo: 2,
      enabled: false,
      onSuccess: (data) => {
        const responseData = data?.data?.data;
        if (responseData) {
          navigate(`../perlembagaan?id=${encodedId}`);
        }
      },
    });

  const error = theme.palette.error.main;
  const labelStyle = {
    fontSize: "14px",
    color: "#666666",
    fontWeight: "400 !important",
  };
  const sectionStyle = {
    color: "var(--primary-color)",
    marginBottom: "16px",
    borderRadius: "16px",
    fontSize: "14px",
    fontWeight: "500 !important",
  };
  const { meetingMethod } = values;
  const meetingData = meetingListResponse?.data?.data ?? [];
  const addressDataFallback = addressListResponse?.data?.data ?? [];
  const isCreated = [values.meetingDate, values.meetingTime, values.meetingTimeTo].every((val) => val !== null);

  const addressData = useSelector(getAddressListsRedux) ?? addressDataFallback;
  const throttledMeetingAddress = useThrottle(values.meetingAddress);

  const handleMeetingLocationSelected = (location: LocationSelectedParams) => {
    setFieldValue("meetingLocationLongitude", location.geometry[0]);
    setFieldValue("meetingLocationLatitude", location.geometry[1]);
    setFieldValue("meetingAddress", location.fullAddress);
    setFieldValue("state", location.stateId);
    setFieldValue("district", location.districtId);
    setFieldValue("city", location.city);
    setFieldValue("postcode", location.postcode);
  }
  const handleReactiveMeetingLocation = async ({ client, query, getFoundDistrictId, getFoundStateId }: OnReactiveSearchOptions) => {
    try {
      const command = new SearchPlaceIndexForTextCommand({
        IndexName: import.meta.env.VITE_AWS_PLACE_INDEX,
        Text: query,
        MaxResults: 1,
        FilterCountries: ["MYS"],
      });
      const response = await client.send(command);
      if (!response?.Results || response.Results.length === 0) {
        console.warn("⚠️ No locations found");
        return;
      }
      const firstResult = response.Results[0];
      console.log("firstResult", firstResult);

      if (firstResult?.Place?.Geometry?.Point) {
        const [longitude, latitude] = firstResult.Place.Geometry.Point;
        console.log("📍 Coordinates:", " lng: ", longitude, " Lat: ", latitude);

        const city = firstResult.Place.Municipality ?? "";
        const postcode = firstResult.Place.PostalCode ?? "";
        const stateId = getFoundStateId(firstResult.Place);
        const districtId = getFoundDistrictId(firstResult.Place);
        setFieldValue(`state`, stateId !== null ? stateId : null);
        setFieldValue(`district`, districtId !== null ? districtId : null);
        setFieldValue(`city`, city);
        setFieldValue(`postcode`, postcode);
        setFieldValue(`meetingLocationLongitude`, longitude);
        setFieldValue(`meetingLocationLatitude`, latitude);
      }
    } finally {
      setReactiveMeetingLocation(false);
      setTimeout(async () => await validateForm(), 500);
    }
  }
  const handleReset = () => {
    resetForm({
      values: {
        ...initialValue,
        id: values.id,
      }
    });
  }
  const handleGoNext = async () => await getCheckAndUpdateRegistration();

  useEffect(() => {
    if (throttledMeetingAddress?.trim()?.length > 1) {
      setReactiveMeetingLocation(true);
    }
  }, [throttledMeetingAddress]);

  useEffect(() => {
    if (values.state != stateRef.current) {
      stateRef.current = values.state;
      const districtLists = addressData
        .filter((item) => item.pid == values.state);
      setFieldValue("district", districtLists?.[0]?.id ?? null);
    }
  }, [values.state])

  return (
    <Form>
      <Box sx={{ backgroundColor: "white", p: 3, borderRadius: "15px" }}>
        <Box
          sx={{
            p: { xs: 1, sm: 2, md: 3 },
            border: "1px solid #D9D9D9",
            borderRadius: "14px",
            mb: 2,
          }}
        >
          <Typography variant="subtitle1" sx={sectionStyle}>
            {capitalizeWords(t("establishmentMeeting"))}
          </Typography>

          <Grid container spacing={2}>
            <Grid item xs={12} sm={4}>
              <Typography sx={labelStyle}>
                {t("meetingType")} <span style={{ color: error }}>*</span>
              </Typography>
            </Grid>
            <Grid item xs={12} sm={8}>
              <TextFieldControllerFormik
                name="meetingType"
                disabled
                select
                helperTextComponentPlacement="INSIDE"
                helperTextFallbackValue=""
              >
                <option value="" disabled>
                  {t("pleaseSelect")}
                </option>
               {MeetingTypeOption.map((item, index) => (
                  <option key={`meeting-type-${index}`} value={item.value}>
                    {item.label}
                  </option>
                ))}
              </TextFieldControllerFormik>
            </Grid>

            <Grid item xs={12} sm={4}>
              <Typography sx={labelStyle}>
                {t("meetingMethod")} <span style={{ color: error }}>*</span>
              </Typography>
            </Grid>
            <Grid item xs={12} sm={8}>
              <TextFieldControllerFormik
                name="meetingMethod"
                required
                select
                helperTextComponentPlacement="INSIDE"
                helperTextFallbackValue=""
              >
                <option value="" disabled>
                  {isLoadingMeetingList ? "Loading..." : t("pleaseSelect")}
                </option>
                {!isLoadingMeetingList &&
                  meetingData
                    .filter((item) => item.pid === 2)
                    .map((item) => (
                      <option key={`meeting-method-${item.id}`} value={item.id}>
                        {i18n.language === "en"
                          ? item.nameEn
                          : item.nameBm == "Hybrid"
                          ? "Hibrid"
                          : item.nameBm}
                      </option>
                    ))}
              </TextFieldControllerFormik>
            </Grid>
            {/** @ts-expect-error */}
            {meetingMethod == MeetingMethods.ATAS_TALIAN || meetingMethod == MeetingMethods.HYBRID ? (
              <>
                <Grid item xs={12} sm={4}>
                  <Typography sx={labelStyle}>
                    {t("platformType")}{" "}
                    <span style={{ color: error }}>*</span>
                  </Typography>
                </Grid>
                <Grid item xs={12} sm={8}>
                  <TextFieldControllerFormik
                    name="platformType"
                    required
                    select
                    helperTextComponentPlacement="INSIDE"
                    helperTextFallbackValue=""
                  >
                    <option value="" disabled>
                      {isLoadingMeetingList ? "Loading..." : t("pleaseSelect")}
                    </option>
                    {!isLoadingMeetingList &&
                      meetingData
                        .filter((item) => item.pid === 3)
                        .map((item) => (
                          <option key={item.id} value={item.id}>
                            {i18n.language === "en"
                              ? item.nameEn
                              : item.nameBm}
                          </option>
                        ))}
                  </TextFieldControllerFormik>
                </Grid>
              </>
            ) : null}
            <Grid item xs={12} sm={4}>
              <Typography sx={labelStyle}>
                {t("tujuanMesyuarat")}{" "}
                <span style={{ color: error }}>*</span>
              </Typography>
            </Grid>
            <Grid item xs={12} sm={8}>
              <TextFieldControllerFormik
                fullWidth
                required
                name="meetingPurpose"
                helperTextComponentPlacement="INSIDE"
                helperTextFallbackValue=""
              />
            </Grid>
          </Grid>
        </Box>
        <Box
          sx={{
            p: { xs: 1, sm: 2, md: 3 },
            border: "1px solid #D9D9D9",
            borderRadius: "14px",
            mb: 2,
          }}
        >
          <Typography variant="subtitle1" sx={sectionStyle}>
            {t("masaDanTarikhMesyuarat")}
          </Typography>
          <FormMeetingDateTimeInnerFormik
            meetingTimeFromAttribute="meetingTime"
            withLocalizationProviderWrapper={false}
          />
        </Box>
        {/** @ts-expect-error */}
        {meetingMethod == MeetingMethods.BERSEMUKA || meetingMethod == MeetingMethods.HYBRID ? (
          <>
            <Box
              sx={{
                p: { xs: 1, sm: 2, md: 3 },
                border: "1px solid #D9D9D9",
                borderRadius: "14px",
                mb: 2,
              }}
            >
              <Typography variant="subtitle1" sx={sectionStyle}>
                {t("alamatTempatMesyuarat")}
              </Typography>
              <Grid container spacing={2}>
                <Grid item xs={12} sm={4}>
                  <Typography sx={labelStyle}>
                    {t("tempatMesyuarat")}{" "}
                    <span style={{ color: error }}>*</span>
                  </Typography>
                </Grid>
                <Grid item xs={12} sm={8}>
                  <TextFieldControllerFormik
                    fullWidth
                    required
                    name="meetingPlace"
                    helperTextComponentPlacement="INSIDE"
                    helperTextFallbackValue=""
                  />
                </Grid>
                <Grid item xs={12} sm={4}>
                  <Typography sx={labelStyle}>
                    {t("meetingLocation")}
                  </Typography>
                </Grid>
                <Grid item xs={12} sm={8}>
                  <div
                    style={{
                      height: "150px",
                      width: "100%",
                      borderRadius: "8px",
                    }}
                  >
                    <AWSLocationMap
                      longitude={values.meetingLocationLongitude}
                      latitude={values.meetingLocationLatitude}
                      searchQuery={throttledMeetingAddress}
                      reactiveSearch={reactiveMeetingLocation}
                      onReactiveSearch={handleReactiveMeetingLocation}
                      onLocationSelected={handleMeetingLocationSelected}
                    />
                  </div>
                </Grid>
                <Grid item xs={12} sm={4}>
                  <Typography sx={labelStyle}>
                    {t("alamatTempatMesyuarat")}{" "}
                    <span style={{ color: error }}>*</span>
                  </Typography>
                </Grid>
                <Grid item xs={12} sm={8}>
                  <TextFieldControllerFormik
                    fullWidth
                    required
                    name="meetingAddress"
                    helperTextComponentPlacement="INSIDE"
                    helperTextFallbackValue=""
                  />
                </Grid>
                <Grid item xs={12} sm={4}>
                  <Typography sx={labelStyle}>
                    {t("state")} <span style={{ color: error }}>*</span>
                  </Typography>
                </Grid>

                <Grid item xs={12} sm={8}>
                  <TextFieldControllerFormik
                    name="state"
                    required
                    select
                    helperTextComponentPlacement="INSIDE"
                    helperTextFallbackValue=""
                  >
                    <option value="" disabled>
                      {t("pleaseSelect")}
                    </option>
                    {addressData
                      .filter((item) => item.pid === MALAYSIA)
                      .map((item) => (
                        <option key={item.id} value={item.id}>
                          {item.name}
                        </option>
                      ))}
                  </TextFieldControllerFormik>
                </Grid>
                <Grid item xs={12} sm={4}>
                  <Typography sx={labelStyle}>
                    {t("district")} <span style={{ color: error }}>*</span>
                  </Typography>
                </Grid>
                <Grid item xs={12} sm={8}>
                  <TextFieldControllerFormik
                    name="district"
                    required
                    select
                    helperTextComponentPlacement="INSIDE"
                    helperTextFallbackValue=""
                  >
                    <option value="" disabled>
                      {t("pleaseSelect")}
                    </option>
                    {addressData
                      .filter((item) => item.pid == values.state)
                      .map((item) => (
                        <option key={item.id} value={item.id}>
                          {item.name}
                        </option>
                      ))}
                  </TextFieldControllerFormik>
                </Grid>
                <Grid item xs={12} sm={4}>
                  <Typography sx={labelStyle}>{t("bandar")}</Typography>
                </Grid>

                <Grid item xs={12} sm={8}>
                  <TextFieldControllerFormik
                    fullWidth
                    name="city"
                    helperTextComponentPlacement="INSIDE"
                    helperTextFallbackValue=""
                  />
                </Grid>
                <Grid item xs={12} sm={4}>
                  <Typography sx={labelStyle}>
                    {t("poskod")} <span style={{ color: error }}>*</span>
                  </Typography>
                </Grid>
                <Grid item xs={12} sm={8}>
                  <TextFieldControllerFormik
                    fullWidth
                    required
                    name="postcode"
                    helperTextComponentPlacement="INSIDE"
                    helperTextFallbackValue=""
                    onlyAcceptNumber
                    isPostcode
                  />
                </Grid>
              </Grid>
            </Box>
          </>
        ) : null}

        <FormMeetingAttendeesFormikInner />
        <Box
          sx={{
            p: { xs: 1, sm: 2, md: 3 },
            border: "1px solid #D9D9D9",
            borderRadius: "14px",
            mb: 2,
          }}
        >
          <Typography variant="subtitle1" sx={sectionStyle}>
            {t("minitMesyuarat")}
          </Typography>
          <Grid container spacing={2}>
            <Grid item xs={12} sm={4}>
              <Typography sx={labelStyle}>
                {t("minitMesyuarat")}{" "}
                <span style={{ color: error }}>*</span>
              </Typography>
            </Grid>
            <Grid item xs={12} sm={8}>
              <FormMeetingAttachmentFormik />
            </Grid>

            <Grid item xs={12}>
              <Box
                sx={{
                  px: { xs: 1, sm: 2, md: 3 },
                  py: 1,
                  border: "1px solid #D9D9D9",
                  backgroundColor: "#FFFFFF",
                  borderRadius: "14px",
                  mt: 2,
                  display: "flex",
                  justifyContent: "flex-start",
                  alignItems: "flex-start",
                }}
              >
                <Field name="agreeToTheStatement">
                  {({ field }: FieldProps) => (
                    <FormControlLabel
                      sx={{
                        alignItems: "flex-start",
                        color: "#666666",
                        "& .MuiFormControlLabel-label": {
                          fontWeight: 400,
                        },
                      }}
                      control={
                        <Checkbox
                          {...field}
                          checked={values.agreeToTheStatement}
                          sx={{ mt: -1 }}
                        />
                      }
                      label={
                        <>
                          {t("meetingInfoConfirmation")}{" "}
                          <span style={{ color: error }}>*</span>
                        </>
                      }
                    />
                  )}
                </Field>
              </Box>
            </Grid>
            <Grid
              item
              xs={12}
              sx={{
                mt: 2,
                display: "flex",
                flexDirection: isMobile ? "column" : "row",
                justifyContent: "flex-end",
                gap: 1,
              }}
            >
              <ButtonOutline
                sx={{
                  bgcolor: "white",
                  "&:hover": { bgcolor: "white" },
                  width: isMobile ? "100%" : "auto",
                }}
                onClick={handleReset}
                disabled={isSubmitting}
              >
                {t("semula")}
              </ButtonOutline>
              <ButtonPrimary
                disabled={isSubmitting || !isValid}
                variant="contained"
                type="submit"
                sx={{ width: isMobile ? "100%" : "auto" }}
              >
                {t(isSubmitting ? "saving" : "update")}
              </ButtonPrimary>
            </Grid>
          </Grid>
        </Box>
        <Box sx={{ p: { xs: 1, sm: 2, md: 3 } }}>
          <ButtonPrimary
            disabled={!isCreated}
            onClick={handleGoNext}
            variant="contained"
            sx={{
              width: isMobile ? "100%" : "auto",
              float: "right",
            }}
          >
            {t("next")}
          </ButtonPrimary>
        </Box>
      </Box>
    </Form>
  )
}
