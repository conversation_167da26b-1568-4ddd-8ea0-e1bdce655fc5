import {
  DocumentUploadType,
  globalStyles,
  useMutation,
  useQuery,
  useUploadPresignedUrl,
} from "@/helpers";
import {
  Controller,
  FieldValues,
  FormProvider,
  useForm,
} from "react-hook-form";
import { useTranslation } from "react-i18next";

import {
  ButtonOutline,
  ButtonPrimary,
  DatePickerController,
  DatePickerForm,
  DialogConfirmation,
  FileUploadController,
  FormFieldRow,
  Label,
  SelectFieldController,
  SocietyBanner,
  TextFieldController,
  ToggleButtonController,
} from "@/components";
import {
  Box,
  Checkbox,
  Divider,
  FormControlLabel,
  FormGroup,
  Grid,
  TextField,
  ToggleButton,
  Typography,
} from "@mui/material";

import { LoadingOverlay } from "@/components/loading";
import i18n from "@/i18n";
import { useEffect, useState } from "react";
import { useNavigate, useParams } from "react-router-dom";
import MaklumatPertubuhanDetail from "./MaklumatPertubuhanDetail";
import TransaksiTable from "./TransaksiTable";
import dayjs from "dayjs";

interface GrantField {
  id: string | number;
  fieldName: string;
  fieldType: string;
  isRequired: boolean;
  sequenceOrder: number;
  sectionName: string;
  pageNumber: number;
  options?: string[] | FormOption[] | TableOption[];
  grantTemplateId?: string | number;
}
type FormOption = {
  key: string;
  value: string;
};
type TableOption = {
  key: string;
  value: string;
  row?: number;
  col?: number;
};

interface ApplicationPayload {
  grantTemplateId: string | number;
  societyId: string | number;
  fieldValues: {
    grantTemplateFieldId: string | number;
    fieldValue: any;
  }[];
}
const SenaraiPermohonanForm: React.FC = () => {
  const { t } = useTranslation();
  const classes = globalStyles();
  const { societyid, id } = useParams<{ societyid: string; id: string }>();
  const [dialogOpen, setDialogOpen] = useState(false);
  const [isSuccess, setIsSuccess] = useState(false);
  const [payload, setPayload] = useState<ApplicationPayload | null>(null);
  const isMyLanguage = i18n.language === "my";
  const navigate = useNavigate();
  const {
    data: grantTemplateListDataResponse,
    isLoading: isLoadingGrantTemplate,
  } = useQuery({ url: `grant/template/${id}` });

  const { data: societyListDataResponse, isLoading: isLoadingSociety } =
    useQuery({ url: `society/${societyid}` });
  const societyData = societyListDataResponse?.data?.data ?? {};
  const {
    fetch: createApplicationGeran,
    isLoading: isCreatingApplicationGeran,
  } = useMutation({
    url: "grant/application/create",
    method: "post",
    onSuccess: (res) => {
      const responseCode = res?.data?.code;
      const id = res?.data?.data;
      if (responseCode === 201) {
        setIsSuccess(true);
        setTimeout(() => {
          navigate("../", { replace: true });
        }, 2000);
      }
    },
  });

  const {
    fetch: createApplicationGeranBeforeSubmit,
    isLoading: isCreatingApplicationGeranBeforeSubmit,
  } = useMutation({
    url: "grant/application/create",
    method: "post",
    showSuccessNotification: false,
    onSuccess: (res) => {
      const id = res?.data?.data;
      submitApplicationGeran({ id: id });
    },
  });

  const { fetch: submitApplicationGeran, isLoading: isSubmitApplicationGeran } =
    useMutation({
      url: "grant/application/submit",
      method: "put",
      showSuccessNotification: false,
      onSuccess: (res) => {
        const responseCode = res?.data?.code;

        if (responseCode === 201) {
          setIsSuccess(true);
          setTimeout(() => {
            navigate("../", { replace: true });
          }, 2000);
        }
      },
    });
  const { upload: uploadFile, isLoading: isUploading } = useUploadPresignedUrl({
    onSuccessUpload: (data) => {
      const documentName = data?.data?.data?.name ?? null;
      const documentUrl = data?.data?.data?.url ?? null;
    },
  });
  const handleUpload = async (
    file: File | null,
    fieldName: string,
    fieldId: string | number
  ) => {
    if (!file) return;
    try {
      await uploadFile({
        params: {
          type: DocumentUploadType.GERAN,
          code: `GERAN_APPLICATION`,
          grantTemplateId: id,
          societyId: societyid,
          grantTemplateFieldId: fieldId,
        },
        file,
      });
    } catch (error) {
      console.error("Upload failed:", error);
    }
  };

  const grantFields: GrantField[] =
    grantTemplateListDataResponse?.data?.data?.fields ?? [];

  const grantTemplateData: GrantField[] =
    grantTemplateListDataResponse?.data?.data?.fields ?? [];

  const methods = useForm<FieldValues>({
    mode: "onChange",
    defaultValues: {
      grantTemplateId: `${id}`,
      societyId: `${societyid}`,
      fieldValues: [],
    },
  });

  const onSaveDraft = (formData: FieldValues) => {
    const fieldValues = grantTemplateData.map((field, index) => {
      const fieldNames = `${field.fieldName}_${field.sequenceOrder}_${field.pageNumber}`;

      return {
        grantTemplateFieldId: field.id ?? "",
        fieldValue: formData[fieldNames] ?? null,
      };
    });

    const dataPayload = {
      grantTemplateId: id ?? "",
      societyId: societyid ?? "",
      fieldValues,
    };
    createApplicationGeran(dataPayload);
  };

  const onSubmit = (formData: FieldValues) => {
    const fieldValues = grantTemplateData.map((field, index) => {
      const fieldNames = `${field.fieldName}_${field.sequenceOrder}_${field.pageNumber}`;

      return {
        grantTemplateFieldId: field.id ?? "",
        fieldValue: formData[fieldNames],
      };
    });

    const dataPayload = {
      grantTemplateId: id ?? "",
      societyId: societyid ?? "",
      fieldValues,
    };
    setPayload(dataPayload);
    setDialogOpen(true);
  };
  const handleConfirmedSubmit = async () => {
    if (!payload) return;

    await createApplicationGeranBeforeSubmit(payload);
  };

  const { control, handleSubmit, getValues, setValue, watch } = methods;

  const groupedFields = grantFields.reduce((acc, field) => {
    const key = `${field.pageNumber}-${field.sectionName}`;
    if (!acc[key]) acc[key] = [];
    acc[key].push(field);
    return acc;
  }, {} as Record<string, GrantField[]>);

  const sortedGroups = Object.entries(groupedFields).sort(([aKey], [bKey]) => {
    const aPage = parseInt(aKey.split("-")[0]);
    const bPage = parseInt(bKey.split("-")[0]);
    return aPage - bPage;
  });

  const renderFieldComponent = (field: GrantField, index: number) => {
    const label = <Label text={field.fieldName} required={field.isRequired} />;

    switch (field.fieldType) {
      case "TEXT":
        return (
          <FormFieldRow
            key={index}
            label={label}
            value={
              <TextFieldController
                control={control}
                name={`${field.fieldName}_${field.sequenceOrder}_${field.pageNumber}`}
                placeholder={field.fieldName}
                required={field.isRequired}
              />
            }
          />
        );

      case "DROPDOWN":
        const rawOptions = Array.isArray(field?.options) ? field.options : [];

        const optionDropdown =
          typeof rawOptions[0] === "string"
            ? (rawOptions as string[])
                .map((itemString) => {
                  try {
                    const item = JSON.parse(itemString);
                    return {
                      value: item.value,
                      label: item.key,
                    };
                  } catch (err) {
                    console.warn("Failed parse option:", itemString);
                    return null;
                  }
                })
                .filter(
                  (item): item is { value: string | number; label: string } =>
                    item !== null
                )
            : rawOptions.map((item: any) => ({
                value: item.key,
                label: item.value,
              }));

        return (
          <FormFieldRow
            key={index}
            label={label}
            value={
              <SelectFieldController
                control={control}
                name={`${field.fieldName}_${field.sequenceOrder}_${field.pageNumber}`}
                options={optionDropdown}
                placeholder={field.fieldName}
                required={field.isRequired}
              />
            }
          />
        );

      case "CHECKBOX":
        const checkboxOptions: FormOption[] =
          Array.isArray(field?.options) && field.options.length > 0
            ? (field.options as FormOption[])
            : [];

        return (
          <FormGroup>
            <Controller
              name={`${field.fieldName}_${field.sequenceOrder}_${field.pageNumber}`}
              control={control}
              rules={{
                validate: (value) => {
                  const hasChecked = Object.values(value || {}).some((v) => v);
                  return field.isRequired && !hasChecked
                    ? "Minimal one must choosen"
                    : true;
                },
              }}
              render={({ field: controllerField, fieldState }) => (
                <>
                  {checkboxOptions.map((opt) => (
                    <FormControlLabel
                      key={opt.key}
                      control={
                        <Checkbox
                          checked={!!controllerField.value?.[opt.key]}
                          onChange={(e) =>
                            controllerField.onChange({
                              ...controllerField.value,
                              [opt.key]: e.target.checked,
                            })
                          }
                        />
                      }
                      label={<Label text={opt?.value} />}
                    />
                  ))}

                  {fieldState.error && (
                    <p style={{ color: "red" }}>{fieldState.error.message}</p>
                  )}
                </>
              )}
            />
          </FormGroup>
        );
      case "UPLOAD":
        return (
          <FormFieldRow
            key={index}
            label={label}
            value={
              <FileUploadController
                control={control}
                name={`${field.fieldName}_${field.sequenceOrder}_${field.pageNumber}`}
                required={field.isRequired}
                onFileSelect={(file) => {
                  handleUpload(file, `${field.fieldName}`, `${field.id}`);
                }}
              />
            }
          />
        );
      case "TARIKH":
        return (
          <FormFieldRow
            key={index}
            label={label}
            value={
              <DatePickerController
                minDate={dayjs()}
                control={control}
                name={`${field.fieldName}_${field.sequenceOrder}_${field.pageNumber}`}
                required={field.isRequired}
              />
            }
          />
        );
      case "CONFIRM":
        let descriptionConfirm = "";
        let confirmationText = field.fieldName;

        if (Array.isArray(field.options) && field.options.length > 0) {
          if (typeof field.options[0] !== "string") {
            const opts = field.options as FormOption[];
            descriptionConfirm =
              opts.find((o) => o.key === "description")?.value || "";
            confirmationText =
              opts.find((o) => o.key === "confirmationText")?.value ||
              field.fieldName;
          }
        }

        return (
          <Controller
            key={index}
            name={`${field.fieldName}_${field.sequenceOrder}_${field.pageNumber}`}
            control={control}
            rules={{
              required: field.isRequired ? "Sila tandakan akuan ini." : false,
            }}
            render={({ field: rhfField }) => (
              <>
                <Typography className="label" mb={3}>
                  {descriptionConfirm}
                </Typography>

                <Box
                  sx={{ display: "flex", alignItems: "center", gap: "20px" }}
                >
                  <Checkbox
                    {...rhfField}
                    checked={rhfField.value || false}
                    icon={
                      <Box
                        sx={{
                          width: "12px",
                          height: "12px",
                          border: "0.5px solid #848484",
                          background: "none",
                        }}
                      />
                    }
                    checkedIcon={
                      <Box
                        sx={{
                          width: "12px",
                          height: "12px",
                          border: "0.5px solid var(--primary-color)",
                          background: "var(--primary-color)",
                        }}
                      />
                    }
                    sx={{ padding: 0 }}
                  />
                  <Label text={confirmationText} required={field.isRequired} />
                </Box>
              </>
            )}
          />
        );

      case "TRANSAKSI": {
        return (
          <TransaksiTable
            field={field}
            control={control}
            getValues={getValues}
            setValue={setValue}
          />
        );
      }

      case "TABLE":
        const optionTable: TableOption[] = (field.options ?? []).map((opt) => {
          if (typeof opt === "string") {
            try {
              const parsed = JSON.parse(opt);
              return parsed as TableOption;
            } catch {
              return { key: "", value: "" };
            }
          } else {
            return opt as TableOption;
          }
        });

        const rowCounts = Number(
          optionTable.find((o: any) => o.key === "count-row")?.value || 0
        );
        const colCounts = Number(
          optionTable.find((o: any) => o.key === "count-col")?.value || 0
        );

        const tables: string[][] = Array.from({ length: rowCounts }, () =>
          Array.from({ length: colCounts }, () => "")
        );

        optionTable.forEach((opt: any) => {
          if (opt.key === "count-row" || opt.key === "count-col") return;
          tables[opt.row][opt.col] = opt.value;
        });

        return (
          <Grid container direction="column" spacing={2} mb={2}>
            <Grid item>
              <Typography className="title" mt={2} mb={2}>
                {field.fieldName}
              </Typography>
            </Grid>

            {tables.map((row, rowIndex) => (
              <Grid
                container
                spacing={2}
                key={rowIndex}
                ml={1}
                mt={rowIndex > 0 ? 1 : 0}
              >
                {row.map((cellValue, colIndex) => (
                  <Grid item xs={12} md={12 / colCounts - 0.25} key={colIndex}>
                    {rowIndex === 0 ? (
                      <Typography
                        variant="subtitle2"
                        align="center"
                        sx={{ fontWeight: "bold" }}
                      >
                        {cellValue || `Kolom ${colIndex + 1}`}
                      </Typography>
                    ) : (
                      <TextFieldController
                        control={control}
                        name={`${field.fieldName}_${field.sequenceOrder}_${field.pageNumber}[${rowIndex}][${colIndex}]`}
                        placeholder="Isi disini"
                        defaultValue={cellValue}
                        fullWidth
                        required={field.isRequired}
                        size="small"
                      />
                    )}
                  </Grid>
                ))}
              </Grid>
            ))}
          </Grid>
        );

      case "MAKLUMAT":
        return (
          <FormFieldRow
            key={index}
            label={<Label text={field.fieldName} required={field.isRequired} />}
            value={
              <TextFieldController
                control={control}
                name={`${field.fieldName}_${field.sequenceOrder}_${field.pageNumber}`}
                placeholder={field.fieldName || "Tulis Maklumat di sini"}
                fullWidth
                multiline
                required={field.isRequired}
                size="small"
              />
            }
          />
        );

      case "CATATAN":
        const optionMaklumat: FormOption[] =
          Array.isArray(field.options) && typeof field.options[0] !== "string"
            ? (field.options as FormOption[])
            : [];

        const description =
          optionMaklumat.find((o) => o.key === "description")?.value || "";
        const parts = description.split(/(NOTA)/i);

        return (
          <Grid item xs={12}>
            <Typography variant="body2">
              {parts.map((part, idx) =>
                part.toUpperCase() === "NOTA" ? (
                  <span key={idx} style={{ color: "red", fontWeight: "bold" }}>
                    {part}
                  </span>
                ) : (
                  <span key={idx}>{part}</span>
                )
              )}
            </Typography>
          </Grid>
        );
    }
  };

  if (isCreatingApplicationGeran) {
    return <LoadingOverlay />;
  }
  return (
    <FormProvider {...methods}>
      <Box className={classes.section} mb={1}>
        <SocietyBanner
          societyName={societyData?.societyName}
          societyNo={societyData?.societyNo}
        />
      </Box>

      <Box className={classes.section} mb={2}>
        <MaklumatPertubuhanDetail society={societyData} />
      </Box>
      {isLoadingGrantTemplate ? (
        <LoadingOverlay />
      ) : (
        <form onSubmit={handleSubmit(onSubmit)}>
          {Object.entries(
            grantTemplateData
              .sort((a, b) => {
                if (a.pageNumber !== b.pageNumber) {
                  return a.pageNumber - b.pageNumber;
                }

                return a.sequenceOrder - b.sequenceOrder;
              })
              .reduce<Record<string, GrantField[]>>((acc, field) => {
                const key = `${field.pageNumber}`;
                if (!acc[key]) acc[key] = [];
                acc[key].push(field);
                return acc;
              }, {})
          ).map(([groupKey, fields], i) => {
            let sectionTitle =
              fields.find((f) => f.sequenceOrder === 1)?.sectionName || "";

            if (fields.some((f) => f.fieldType === "CATATAN")) {
              sectionTitle = "";
            }
            return (
              <Box className={classes.section} mb={2}>
                <Box
                  key={`${groupKey}_${fields}`}
                  mb={3}
                  p={2}
                  border="1px solid #ccc"
                  borderRadius={2}
                >
                  <Typography
                    className="title"
                    mb={2}
                  >{`${sectionTitle}`}</Typography>
                  {fields.map((field, index) =>
                    renderFieldComponent(field, index)
                  )}
                </Box>
              </Box>
            );
          })}

          <Box
            className={classes.section}
            sx={{
              display: "flex",
              flexDirection: "row",
              justifyContent: "right",
              gap: "8px",
            }}
          >
            <ButtonOutline
              type="button"
              onClick={() => {
                const values = methods.getValues();
                onSaveDraft(values);
              }}
              // disabled={!isDraftEnabled}
              sx={{
                display: "block",
                fontSize: "12px",
              }}
            >
              {t("saveDraftGeran")}
            </ButtonOutline>
            <ButtonPrimary
              type="submit"
              sx={{
                display: "block",
                fontSize: "12px",
              }}
              disabled={isCreatingApplicationGeran}
            >
              {t("hantar")}
            </ButtonPrimary>
          </Box>
        </form>
      )}

      <DialogConfirmation
        isMutating={isCreatingApplicationGeran}
        open={dialogOpen}
        onClose={() => setDialogOpen(false)}
        onAction={handleConfirmedSubmit}
        onConfirmationText={t("confirmSubmitApplication")}
        isSuccess={isSuccess}
        onSuccessText={t("submitApplicationGeranSuccess")}
      />
    </FormProvider>
  );
};

export default SenaraiPermohonanForm;
