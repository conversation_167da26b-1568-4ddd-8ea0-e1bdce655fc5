import React, {useEffect, useState} from "react";
import { useNavigate } from "react-router-dom";
import {
  Box,
  Button,
  Grid,
  Stack,
  Typography,
  useMediaQuery,
  Theme,
} from "@mui/material";
import CalendarMonthOutlinedIcon from "@mui/icons-material/CalendarMonthOutlined";
import { useTranslation } from "react-i18next";
import { ArtikelObject } from "@/pages/artikel/data";
import {useCustom} from "@refinedev/core";
import {API_URL} from "@/api";

type TabItemObject = {
  label: string;
  value: string;
};

const BacaanTerkini: React.FC = () => {
  const { t } = useTranslation();
  const navigate = useNavigate();

  const defaultTabItems: TabItemObject[] = [
    { label: "landing_bacaan_tab_2", value: "ANNOUNCEMENT" },
    { label: "landing_bacaan_tab_3", value: "ACTIVITY" },
    { label: "landing_bacaan_tab_4", value: "PROMOTION" },
  ];

  const [value, setValue] = React.useState(0);
  const [tabItems, setTabItems] = useState<TabItemObject[]>(defaultTabItems);
  const [selectedTab, setSelectedTab] = useState("ANNOUNCEMENT");
  const [items, setItems] = useState<ArtikelObject[]>([]);

  const handleChange = (event: React.SyntheticEvent, newValue: number) => {
    setValue(newValue);
  };

  const isMobile = useMediaQuery((theme: Theme) =>
    theme.breakpoints.down("sm")
  );

  interface TabPanelProps {
    children?: React.ReactNode;
    dir?: string;
    index: number;
    value: number;
  }

  const handleViewArtikel = (slug: string) => {
    navigate(`/artikel/${slug}`);
  };

  const { data: publishedArticleData, isLoading: isPublishedArticleLoading, refetch: refetchArticle} = useCustom({
    url: `${API_URL}/society/posting`,
    method: "get",
    config: {
      headers: {
        portal: localStorage.getItem("portal"),
        //authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
      },
      filters: [
        {
          field: "category",
          operator: "eq",
          value: selectedTab,
        },
        {
          field: "status",
          operator: "eq",
          value: selectedTab==="ANNOUNCEMENT" ? "PUBLISHED" : "RECOMMENDED",
        },
        {
          field: "includeMedia",
          operator: "eq",
          value: true,
        },
        {
          field: "pageSize",
          operator: "eq",
          value: 4,
        },
      ],
    },
    queryOptions: {
      enabled: true,
      retry: false,
      cacheTime: 0,
    },
  });

  const publishedArticle = publishedArticleData?.data?.data?.data ?? [];
  console.log("publishedArticle",publishedArticle);

  useEffect(() => {
    if(publishedArticle){
      const temp:ArtikelObject[] = publishedArticle.map((p: any) => {
        const img = p.mediaUrl && p.mediaUrl.length > 0 ? p.mediaUrl[0] : '/latihanSample/images5.jpg';
        console.log(p);
        return {
          slide: [],
          img: img,
          slug: p.uuid,
          heading1: p.title,
          description: p.description,
          date: p.postingDate,
          name: p.author,
          content: p.description,
        }
      });
      setItems(temp);
    }
  },[publishedArticleData])

  return (
    <Box
      className="layout-container"
      sx={{
        display: "grid",
        justifyItems: "center",
        justifyContent: "center",
        "@media screen and (-webkit-min-device-pixel-ratio: 1.5) and (-webkit-max-device-pixel-ratio: 1.75), screen and (min-resolution: 144dpi) and (max-resolution: 168dpi)":
          {
            transform: "scale(0.75)",
          },
      }}
    >
      <Box sx={{ width: { md: "90%" } }}>
        <Box
          sx={{
            alignItems: "center",
            pb: "64px",
          }}
        >
          <Typography
            sx={{
              color: "#666666",
              fontSize: { lg: "40px", md: "40px", sm: "32px", xs: "32px" },
              lineHeight: { lg: "40px", md: "40px", sm: "32px", xs: "32px" },
              fontWeight: 500,
              textAlign: "center",
              mb: "16px",
            }}
          >
            {t("bacaantitle")}
          </Typography>

          <Typography
            sx={{
              color: "#9A9797",
              fontWeight: "300!important",
              textAlign: "center",
              fontSize: { xs: "14px", md: "22px" },
              width: "100%",
              padding: "0 10px",
            }}
          >
            {t("bacaann_subtitle")}
          </Typography>
        </Box>
        <Box>
          <Box
            sx={{
              display: "flex",
              justifyContent: "space-between",
              alignItems: "center",
            }}
          >
            {tabItems.map((item, index) => (
              <Button
                key={index}
                //disabled={index != 0}
                onClick={() => setSelectedTab(item.value)}
                sx={{
                  textTransform: "unset",
                  textDecoration: "none",
                  fontWeight: 400,
                  fontSize: "16px",
                  color:
                    selectedTab === item.value
                      ? "#333333"
                      : "#9A9797 !important",
                  "&:hover": {
                    textDecoration: "underline",
                    textUnderlineOffset: "6px",
                    textDecorationThickness: "1px",
                    background: "transparent",
                  },
                  "@media (max-width: 500px)": {
                    fontSize: "14px",
                  },
                }}
                disableRipple
              >
                {t(item.label)}
              </Button>
            ))}
          </Box>
        </Box>
        <Grid container spacing={4} sx={{ my: "30px" }}>
          <Grid item xs={12} sm={12} md={12} lg={6}>
            <Stack spacing={5}>
              {items.length === 0 ? <Box sx={{
                display:"flex",
                justifyContent: "center",
              }}>
                <Typography
                  sx={{
                    color: "#333333",
                    fontWeight: 500,
                    fontSize: "18px",
                    lineHeight: "20px",
                    textAlign: "center",
                  }}
                >{t("noData")}</Typography>
              </Box> : <></>}
              {items.slice(0, -1).map((item, index) => (
                <Box
                  key={index}
                  sx={{
                    display: "flex",
                    flexDirection: { xs: "column", md: "row" },
                    gap: "12px",
                    cursor: "pointer",
                  }}
                  onClick={() => handleViewArtikel(item.slug)}
                >
                  <img
                    src={item.img}
                    alt={item.heading1}
                    style={{
                      maxWidth: isMobile ? "100%" : "30%",
                      width: isMobile ? "100%" : "140px",
                      height: isMobile ? "auto" : "140px",
                      borderRadius: isMobile ? "46px" : "32px",
                      objectFit: "cover",
                      aspectRatio: "1/1",
                      transition: "transform 0.3s ease-in-out",
                    }}
                    onMouseEnter={(e) =>
                      (e.currentTarget.style.transform = "scale(1.1)")
                    }
                    onMouseLeave={(e) =>
                      (e.currentTarget.style.transform = "scale(1)")
                    }
                  />
                  <Box
                    sx={{
                      display: "flex",
                      flexDirection: "column",
                      flex: 1,
                      gap: "16px",
                    }}
                  >
                    <Box>
                      <Typography
                        sx={{
                          color: "#333333",
                          fontWeight: 500,
                          fontSize: "18px",
                          lineHeight: "20px",
                        }}
                      >
                        {t(item.heading1)}
                      </Typography>
                      {/* <Typography
                        sx={{
                          color: "#666666B2",
                          fontWeight: 400,
                          fontSize: "16px",
                        }}
                      >
                        {item.description}
                      </Typography> */}
                    </Box>

                    <Box
                      sx={{
                        borderRadius: "12px",
                        bgcolor: "#F3F1F1",
                        padding: 2,
                        color: "#666666",
                      }}
                    >
                      <Box sx={{ display: "flex", gap: 1 }}>
                        <img src="/account.svg" />
                        <Typography
                          sx={{
                            fontSize: "12px",
                          }}
                        >
                          {t(item.name)}
                        </Typography>
                      </Box>
                      <Box sx={{ display: "flex", gap: 1, py: 1, ml: "3px" }}>
                        <img src="/calander.svg" />
                        <Typography
                          sx={{
                            fontWeight: 400,
                            fontSize: "12px",
                          }}
                        >
                          {t(item.date)}
                        </Typography>
                      </Box>
                    </Box>
                  </Box>
                </Box>
              ))}
            </Stack>
          </Grid>

          {/* large display for last article */}
          {items.length > 0 && (
            <Grid item xs={12} sm={12} md={12} lg={6}>
              <Box
                sx={{
                  backgroundRepeat: "no-repeat",
                  backgroundPosition: "center",
                  aspectRatio: "1/1",
                  backgroundImage: `linear-gradient(to bottom, rgba(0, 0, 0, 0), rgba(0, 0, 0, 0.7)),
                    url("${items[items.length - 1].img}")`,
                  borderRadius: "32px",
                  padding: 4,
                  display: "grid",
                  backgroundSize: "cover",
                  overflow: "hidden",
                  cursor: "pointer",
                  transition: "transform 0.3s ease-in-out",
                  "&:hover": {
                    transform: "scale(1.03)",
                  },
                }}
                onClick={() => handleViewArtikel(items[items.length - 1].slug)}
              >
                <Box
                  sx={{
                    display: "grid",
                    justifyContent: "flex-start",
                    alignItems: "flex-end",
                    paddingTop: { xl: 25, lg: 20, md: 20, sm: 10, xs: 1 },
                  }}
                >
                  <Typography
                    sx={{
                      fontSize: "24px",
                      lineHeight: "26px",
                      fontWeight: 400,
                      color: "white",
                      display: "-webkit-box",
                      overflow: "hidden",
                      textOverflow: "ellipsis",
                      WebkitBoxOrient: "vertical",
                      WebkitLineClamp: 2,
                    }}
                  >
                    {t(items[items.length - 1].heading1)}
                  </Typography>

                  <Box>
                    <Typography
                      sx={{
                        fontSize: "16px",
                        lineHeight: "20px",
                        fontWeight: 400,
                        color: "white",
                        display: "-webkit-box",
                        overflow: "hidden",
                        textOverflow: "ellipsis",
                        WebkitBoxOrient: "vertical",
                        WebkitLineClamp: 4,
                      }}
                    >
                      {t(items[items.length - 1].description)}
                    </Typography>
                  </Box>
                  <Box sx={{ display: "flex", gap: { md: 3, sm: 2, xs: 1 } }}>
                    <Typography
                      sx={{
                        fontSize: "12px",
                        display: "flex",
                        justifyContent: "center",
                        fontWeight: 400,
                        alignItems: "center",
                        color: "white",
                        gap: { md: 3, sm: 2, xs: 1 },
                      }}
                    >
                      <CalendarMonthOutlinedIcon />
                      {items[items.length - 1].date}
                    </Typography>
                    <Typography
                      sx={{
                        fontSize: "12px",
                        display: "flex",
                        justifyContent: "center",
                        fontWeight: 400,
                        alignItems: "center",
                        color: "white",
                        gap: { md: 3, sm: 2, xs: 1 },
                      }}
                    >
                      <svg width="24" height="24" viewBox="0 0 24 24">
                        <path
                          d="M15.3954 10.1132C15.3374 10.0539 15.2682 10.0068 15.1918 9.97452C15.1154 9.94225 15.0333 9.92552 14.9504 9.9253C14.8674 9.92507 14.7853 9.94136 14.7087 9.9732C14.6321 10.0051 14.5626 10.0518 14.5043 10.1108C14.446 10.1698 14.4 10.2398 14.3689 10.3167C14.3379 10.3936 14.3226 10.476 14.3237 10.5589C14.3249 10.6418 14.3425 10.7237 14.3756 10.7998C14.4087 10.8758 14.4566 10.9445 14.5165 11.0018C15.0468 11.5236 15.4675 12.1461 15.7539 12.8326C16.0404 13.5192 16.1868 14.2561 16.1846 15C16.1846 15.7644 13.9934 16.875 10.5596 16.875C7.12579 16.875 4.9346 15.7638 4.9346 14.9987C4.9325 14.2599 5.07696 13.528 5.35962 12.8454C5.64229 12.1628 6.05754 11.5431 6.58135 11.022C6.64059 10.9643 6.68778 10.8953 6.72017 10.8192C6.75256 10.743 6.76951 10.6612 6.77003 10.5785C6.77056 10.4957 6.75465 10.4137 6.72322 10.3372C6.6918 10.2606 6.64549 10.1911 6.58699 10.1326C6.52848 10.0741 6.45893 10.0278 6.38239 9.99635C6.30585 9.96493 6.22382 9.94902 6.14108 9.94954C6.05834 9.95007 5.97653 9.96701 5.90039 9.9994C5.82425 10.0318 5.7553 10.079 5.69754 10.1382C5.05718 10.7752 4.54954 11.5329 4.20401 12.3674C3.85849 13.202 3.68194 14.0967 3.6846 15C3.6846 17.0297 7.22648 18.125 10.5596 18.125C13.8927 18.125 17.4346 17.0297 17.4346 15C17.4374 14.0907 17.2584 13.19 16.9082 12.3508C16.5581 11.5116 16.0437 10.7508 15.3954 10.1132Z"
                          fill="#FFFFFF"
                        />
                        <path
                          d="M10.5596 10.625C11.4249 10.625 12.2707 10.3684 12.9902 9.88768C13.7097 9.40695 14.2704 8.72367 14.6015 7.92424C14.9327 7.12482 15.0193 6.24515 14.8505 5.39648C14.6817 4.54781 14.265 3.76826 13.6532 3.15641C13.0413 2.54456 12.2618 2.12788 11.4131 1.95907C10.5644 1.79026 9.68476 1.8769 8.88533 2.20803C8.0859 2.53916 7.40262 3.09992 6.92189 3.81938C6.44116 4.53885 6.18457 5.38471 6.18457 6.25C6.18591 7.40991 6.64728 8.52193 7.46746 9.34211C8.28764 10.1623 9.39966 10.6237 10.5596 10.625ZM10.5596 3.125C11.1776 3.125 11.7818 3.30828 12.2957 3.65166C12.8096 3.99504 13.2102 4.4831 13.4467 5.05412C13.6832 5.62514 13.7451 6.25347 13.6245 6.85966C13.5039 7.46585 13.2063 8.02267 12.7693 8.45971C12.3322 8.89675 11.7754 9.19438 11.1692 9.31496C10.563 9.43553 9.9347 9.37365 9.36368 9.13713C8.79267 8.9006 8.30461 8.50006 7.96123 7.98616C7.61785 7.47226 7.43457 6.86807 7.43457 6.25C7.43555 5.4215 7.7651 4.62721 8.35094 4.04137C8.93678 3.45553 9.73107 3.12598 10.5596 3.125Z"
                          fill="#FFFFFF"
                        />
                      </svg>
                      {items[items.length - 1].name}
                    </Typography>
                  </Box>
                </Box>
              </Box>
            </Grid>
          )}
        </Grid>
      </Box>
    </Box>
  );
};

export default BacaanTerkini;
