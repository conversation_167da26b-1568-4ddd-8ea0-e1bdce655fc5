import React, { useState } from "react";
import InternalHebahanHeader from "@/pages/hebahan/hebahanHeader";
import { Box, Grid, IconButton, Typography } from "@mui/material";
import { headerStyle } from "@/pages/hebahan/hebahanConstant";
import { TrainingAddIcon } from "@/components/icons/trainingAdd";
import {ButtonOutline, DialogConfirmation, Switch} from "@/components";
import CustomDataGrid from "@/components/datagrid";
import { GridColDef } from "@mui/x-data-grid";
import {formatDate, useQuery} from "@/helpers";
import { useTranslation } from "react-i18next";
import {useLocation, useNavigate} from "react-router-dom";
import { API_URL } from "@/api";
import { EditIcon, TrashIcon, EyeIcon } from "@/components/icons";
import { useCustomMutation } from "@refinedev/core";


export interface ListArticleProps {
  isAdmin: boolean,
}

const ListArticle: React.FC<ListArticleProps> = ({isAdmin}) => {
  const { t, i18n } = useTranslation();
  const navigate = useNavigate();
  const [filter, setFilter] = useState("");
  const [openModal, setOpenModal] = useState(false);
  const [refetchData, setRefetchData] = useState(false);
  const [deleteId, setDeleteId] = useState("0");
  const [userData, setUserData] = useState({});
  //const [societyId, setSocietyId] = useState(0);

  let tabs;
  if(isAdmin){
    tabs = [
      { label: "Semua", filter: "" },
      { label: "Draf", filter: "DRAFT" },
      { label: "Diterbitkan", filter: "PUBLISHED" },
      { label: "Menunggu Keputusan", filter: "IN_REVIEW" },
      { label: "Syor", filter: "RECOMMENDED" },
      { label: "Tidak Syor", filter: "NOT_RECOMMENDED" },
    ];
  }else{
    tabs = [
      { label: "Semua", filter: "" },
      { label: "Draf", filter: "DRAFT" },
      { label: "Menunggu Pengesahan", filter: "IN_REVIEW" },
      { label: "Diterbitkan", filter: "RECOMMENDED" },
    ];
  }

  const location = useLocation();

  const societyId = location?.state?.societyId ?? null;
  console.log("societyId",societyId)

  const { refetch: fetchUser } = useQuery({
    url: "user/auth/me",
    autoFetch: true,
    onSuccess: (data) => {
      const tempUserData = data?.data?.data;
      console.log("tempUserData",tempUserData);
      if (tempUserData) setUserData(tempUserData);
    },
  });


  const columns: GridColDef[] = [
    {
      field: "no",
      headerName: "No",
      flex: 1,
      renderCell: ({row}) => {
        return <Typography
          sx={{
            color: "#666666",
            fontWeight: "400",
            fontSize: 14,
            cursor: "pointer"
          }}
        //onClick={() => navigate("sijil/detail", {state:{enrollId:row?.trainingEnrollmentId, courseId:row?.trainingId}})}
        >
          {row?.no}
        </Typography>;
      },
    },
    {
      field: "title",
      headerName: t("articleTitle"),
      flex: 3,
      renderCell: ({ row }) => {
        return <Typography
          sx={{
            color: "#666666",
            fontWeight: "400",
            fontSize: 14,
            cursor: "pointer"
          }}
        //onClick={() => navigate("sijil/detail", {state:{enrollId:row?.trainingEnrollmentId, courseId:row?.trainingId}})}
        >
          {row?.title}
        </Typography>;
      },
    },
    {
      field: "createdDate",
      headerName: t("publishDate"),
      flex: 1,
      renderCell: ({ row }) => {
        return row?.createdDate;
      },
    },
    {
      field: "status",
      headerName: t("status"),
      flex: 1,
      renderCell: ({ row }) => {
        return t(row?.status);
      },
    },
    {
      field: "action",
      headerName: t("action"),
      flex: 1,
      renderCell: ({ row }) => {
        return (
          <Box
            sx={{
              display: "flex",
              justifyContent: "center",
              alignItems: "center",
              columnGap: "0.5rem",
            }}
          >
            {row.status==="DRAFT" || (isAdmin && row.status==="IN_REVIEW") ?
            <IconButton
              style={{
                minWidth: "2rem",
                minHeight: "2rem",
              }}
              color="primary"
              onClick={() => {
                if(filter==="IN_REVIEW") {
                  navigate(`review/${row.id}`)
                }else {
                  navigate(`update/${row.id}`)
                }
              }}
            >
              <EditIcon
                sx={{
                  fontSize: "2rem",
                  width: "1rem",
                  height: "1rem",
                }}
              />
            </IconButton>: <></> }
            {row.status==="DRAFT" ?
            <IconButton
              style={{
                minHeight: "2rem",
                minWidth: "2rem",
              }}
              color="error"
              onClick={() => confirmDeletePosting(row.id)}
            >
              <TrashIcon />
            </IconButton>
            : <></>}
            <IconButton
              style={{
                minHeight: "2rem",
                minWidth: "2rem",
              }}
            //color="error"
            onClick={() => navigate(`preview/${row.id}`)}
            >
              <EyeIcon />
            </IconButton>
            <Switch
              checked={row.status === "PUBLISHED"}
              onChange={(e) => handleSwitchOnChange(row, e.target.checked)}
            />
          </Box>
        );
      },
    },
  ]

  const handleSwitchOnChange = (row: any, val: boolean) => {
    //console.log("handleSwitchOnChange",row)
    Change(row.id, val ? "publish" : "draft");
  }

  const { mutate: changePosting, isLoading: isLoadingChange } = useCustomMutation();
  const Change = (id: string, status: string): void => {
    changePosting(
      {
        url: `${API_URL}/society/admin/posting/${id}/${status}`,
        method: "put",
        values: {},
        config: {
          headers: {
            "Content-Type": "application/json",
            portal: localStorage.getItem("portal") || "",
            authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
          },
        },
        successNotification: (data) => {
          if (data?.data?.data) {
            //console.log(data?.data?.data);
            setOpenModal(false);
            setRefetchData(true);
            return {
              message: data?.data?.msg.includes("hidden") ? t("TRAINING_UNPUBLISHED") : t("TRAINING_PUBLISHED"),
              type: "success",
            };
          } else {
            return {
              message: data?.data?.msg.includes("incomplete") ? t("TRAINING_INCOMPLETE") : t("error") + data?.data?.msg,
              type: "error",
            };
          }
        },
        errorNotification: (data) => {
          return {
            message: data?.response?.data?.msg,
            type: "error",
          };
        },
      },
      {
        onError(error, variables, context) {
          console.log(error);
        },
      }
    );
  };

  const confirmDeletePosting = (id: string) => {
    setDeleteId(id);
    setOpenModal(true);
  }

  const handleDelete = () => {
    if (deleteId != "0") {
      Delete(deleteId);
    }
    setDeleteId("0")
  }

  const { mutate: deletePosting, isLoading: isLoadingDelete } = useCustomMutation();
  const Delete = (id: string): void => {
    deletePosting(
      {
        url: `${API_URL}/society/admin/posting/${id}`,
        method: "delete",
        values: {},
        config: {
          headers: {
            "Content-Type": "application/json",
            portal: localStorage.getItem("portal") || "",
            authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
          },
        },
        successNotification: (data) => {
          if (data?.data?.data) {
            setOpenModal(false);
            setRefetchData(true);
            return {
              message: data?.data?.msg,
              type: "success",
            };
          } else {
            return {
              message: t("error") + data?.data?.msg,
              type: "error",
            };
          }
        },
        errorNotification: (data) => {
          return {
            message: data?.response?.data?.msg,
            type: "error",
          };
        },
      },
      {
        onError(error, variables, context) {
          console.log(error);
        },
      }
    );
  };

  return (<>
    <InternalHebahanHeader />
    <Box
      sx={{
        display: "flex",
        alignItems: "center",
        backgroundColor: "white",
        paddingBottom: "8px",
        justifyContent: "space-evenly",
        borderRadius: "10px",
        px: 2,
        py: 1,
        mb: 1,
      }}
    >
      {tabs.map((tab, index) => {
        // Tentukan apakah tab saat ini aktif berdasarkan URL
        const isActive = filter === tab.filter;
        return (
          <React.Fragment key={index}>
            <Box
              sx={{
                flex: 1,
                backgroundColor: isActive ? "#0CA6A6" : "#FFFFFF",
                p: 1,
                //mx:1,
                borderRadius: "5px",
              }}>
              <Box
                key={index}
                onClick={() => {
                  //handleNavigation(tab.path)
                  //handleFilter(tab.filter)
                  setFilter(tab.filter)
                }}
                sx={{
                  cursor: "pointer",
                  color: isActive ? "#FFFFFF" : "#666666",
                  transition: "color 0.3s, border-bottom 0.3s",
                }}
              >
                <Typography sx={{ fontWeight: "400 !important", textAlign: "center", fontSize: "14px" }}>
                  {tab.label}
                </Typography>
              </Box>
            </Box>
          </React.Fragment>
        );
      })}
    </Box>
    <Box
      sx={{
        borderRadius: 2.5,
        backgroundColor: "#fff",
        //display: "inline",
        px: 2,
        py: 2,
        mb: 1,
      }}
    >
      <Box
        sx={{
          borderRadius: 2.5,
          backgroundColor: "#fff",
          border: "1px solid #D9D9D9",
          //flex: 5,
          //display: "inline",
          px: 2,
          py: 2,
          mb: 1,
        }}
      >
        <Typography sx={headerStyle}>
          {t("articleList")}
        </Typography>
        <Box sx={{ display: "flex", justifyContent: "flex-end" }}>
          <Box sx={{ width: 220 }}>
            <Grid container sx={{ mt: 1 }}>
              <Grid item xs={3}>
                <Box
                  sx={{
                    display: "flex",
                    justifyContent: "center",
                    alignItems: "center",
                    borderRadius: 2.5,
                    backgroundColor: "#0CA6A6",
                    width: 36,
                    height: 36,
                  }}
                >
                  <TrainingAddIcon sx={{ color: "#FFF", }} />
                </Box>
              </Grid>
              <Grid item xs={9}>
                <ButtonOutline
                  sx={{
                    //display: "inline",
                    bgcolor: "white",
                    "&:hover": { bgcolor: "white" },
                    width: "75%",
                    minWidth: "75%"
                  }}
                  onClick={() => navigate('create', {state:{userData:userData,societyId:societyId}})}
                >
                  {t("createArticle")}
                </ButtonOutline>
              </Grid>
            </Grid>
          </Box>
        </Box>
        {isAdmin
          && (filter==="IN_REVIEW"
          || filter==="RECOMMENDED"
          || filter==="NOT_RECOMMENDED") ?
            <CustomDataGrid
              url={`${API_URL}/society/posting`}
              columns={columns}
              noResultMessage={t("noData")}
              isFiltered
              type={2}
              filters={[
                { field: "status", operator: "eq", value: filter },
                { field: "category", operator: "ne", value: "ANNOUNCEMENT" }
              ]}
              setRefetchData={setRefetchData}
              refetchData={refetchData}
            /> : <></>}
        {isAdmin
          && (filter===""
          || filter==="DRAFT"
          || filter==="PUBLISHED") ?
            <CustomDataGrid
              url={`${API_URL}/society/posting`}
              columns={columns}
              noResultMessage={t("noData")}
              isFiltered
              type={2}
              filters={[
                { field: "status", operator: "eq", value: filter },
                { field: "category", operator: "eq", value: "ANNOUNCEMENT" }
              ]}
              setRefetchData={setRefetchData}
              refetchData={refetchData}
            /> : <></>}
        {!isAdmin ?
          <CustomDataGrid
            url={`${API_URL}/society/posting`}
            columns={columns}
            noResultMessage={t("noData")}
            isFiltered
            type={2}
            filters={[
              { field: "status", operator: "eq", value: filter },
              //{ field: "createdBy", operator: "eq", value: userData },
              { field: "societyId", operator: "eq", value: societyId },
            ]}
            setRefetchData={setRefetchData}
            refetchData={refetchData}
          /> : <></>}

      </Box>
    </Box>
    <DialogConfirmation
      open={openModal}
      onClose={() => {
        setOpenModal(false);
      }}
      onAction={handleDelete}
      isMutating={false}
      onConfirmationText={t("CONFIRM_DELETE_TRAINING")}
    />
  </>)
}

export default ListArticle
