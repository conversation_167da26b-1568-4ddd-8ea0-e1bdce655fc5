import React, {useEffect, useState} from "react";
import {Box, Typography} from "@mui/material";
import {headerStyle, labelStyle} from "@/pages/hebahan/hebahanConstant";
import {DailyUserIcon} from "@/components/icons/dailyUser";
import <PERSON><PERSON><PERSON><PERSON><PERSON> from "@/pages/hebahan/graph";
import {API_URL} from "@/api";
import CustomDataGrid from "@/components/datagrid";
import {useTranslation} from "react-i18next";
import {GridColDef} from "@mui/x-data-grid";
import {formatDate} from "@/helpers";
import {RepeatingUserIcon} from "@/components/icons/repeatingUser";
import {UnregisteredUserIcon} from "@/components/icons/unregisteredUser";
import InternalHebahanDashboardSidebar from "@/pages/hebahan/internalDashboardSidebar";
import {useNavigate} from "react-router-dom";
import {useCustom} from "@refinedev/core";
import LoadingOverlay from "../../components/loading/LoadingOverlay";


const InternalHebahanDashboard: React.FC = () => {
  const {t, i18n} = useTranslation();

  const [data,setData] = useState<any>({
    returningUsers:0,
    dailyUsers: 0,
    mappedData:[],
    articleData: [],
  })

  const navigate = useNavigate();

  const columns: GridColDef[] = [
    {
      field: "title",
      headerName: "Tajuk Artikel",
      flex: 1,
      renderCell: ({row}) => {
        return <Typography
          sx={{
            color: "#666666",
            fontWeight: "400",
            fontSize: 14,
            cursor: "pointer"
          }}
          onClick={() => navigate(`list/update/${row.id}`)}
        >
          {row?.title}
        </Typography>;
      },
    },
    {
      field: "dateFinished",
      headerName: "Tarikh Penerbitan",
      flex: 1,
      renderCell: ({row}) => {
        const formattedDate = formatDate(row?.postingDate)
        return formattedDate;
      },
    },
    {
      field: "visit",
      headerName: "Lawatan Pengguna",
      flex: 1,
      renderCell: ({row}) => {
        if (data.articleData && data.articleData.filter((a: any) => a.uuid === row.uuid).length > 0) {
          return <Typography
            sx={{
              color: "#666666",
              fontWeight: "400",
              fontSize: 14,
              cursor: "pointer"
            }}
            //onClick={() => navigate("sijil", {state:{enrollId:row?.trainingEnrollmentId, courseId:row?.trainingId}})}
          >
            {data.articleData.filter((a: any) => a.uuid === row.uuid)[0].totalUsers}
          </Typography>;
        }
        return "";
      },
    },
    {
      field: "revisit",
      headerName: "Pengguna Berulang",
      flex: 1,
      renderCell: ({row}) => {
        if (data.articleData && data.articleData.filter((a: any) => a.uuid === row.uuid).length > 0) {
          return <Typography
            sx={{
              color: "#666666",
              fontWeight: "400",
              fontSize: 14,
              cursor: "pointer"
            }}
            //onClick={() => navigate("sijil", {state:{enrollId:row?.trainingEnrollmentId, courseId:row?.trainingId}})}
          >
            {data.articleData.filter((a: any) => a.uuid === row.uuid)[0].returningUsers}
          </Typography>;
        }
        return "";
      },
    },
    {
      field: "share",
      headerName: "Jumlah Share",
      flex: 1,
      renderCell: ({row}) => {
        return row?.share;
      },
    },
  ]

  const {data: analyticsData, isLoading: isAnalyticsLoading, refetch: refetchAnalytics} = useCustom({
    url: `${API_URL}/society/admin/posting/analytics`,
    method: "get",
    config: {
      headers: {
        portal: localStorage.getItem("portal"),
        authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
      },
    },
    queryOptions: {
      enabled: true,
      retry: false,
      cacheTime: 0,
    },
  });

  useEffect(() => {
    if(analyticsData?.data?.data){
      const dailyUsers = analyticsData?.data?.data?.dailyUsers;
      const returningUsers = analyticsData?.data?.data?.returningUsers;
      const dailyUsersGrowth = analyticsData?.data?.data?.dailyUsersGrowth;
      const returningUsersGrowth = analyticsData?.data?.data?.returningUsersGrowth;
      const mappedData = analyticsData?.data?.data?.graphData?.map((d: any) => {
        console.log(d);
        return {
          name: formatDate(d.date),
          uv: d.totalUsers,
          pv: d.totalUsers + 5,
          //amt: d.totalUsers,
        }
      });
      const articleData = analyticsData?.data?.data.articleData.map((d: any) => {
        return {
          uuid: d.uuid,
          totalUsers: d.totalUsers,
          returningUsers: d.returningUsers
        }
      });
      setData({
        returningUsers: returningUsers,
        returningUsersGrowth:returningUsersGrowth,
        dailyUsers: dailyUsers,
        dailyUsersGrowth: dailyUsersGrowth,
        mappedData: mappedData,
        articleData: articleData,
      })
    }
  },[analyticsData])


  //console.log("analyticsData", mappedData);


  return (
    isAnalyticsLoading ? <Box>
        <LoadingOverlay isLoading={true}/>
      </Box> :
      <Box>
        <Box sx={{display: "flex", gap: 2,}}>
          <Box
            sx={{
              borderRadius: 2.5,
              backgroundColor: "#fff",
              //display: "inline",
              px: 2,
              py: 2,
              mb: 1,
            }}
          >
            <Box sx={{display: "flex", justifyContent: "space-between"}}>
              <Typography sx={headerStyle}>
                {'Jumlah Pengguna Harian'}
              </Typography>
              <DailyUserIcon/>
            </Box>
            <Typography sx={{
              pt: 4, fontWeight: "500",
              fontSize: 48, color: '#666666', fontStyle: 'medium'
            }}>
              {data.dailyUsers}
            </Typography>
            <Typography sx={labelStyle}>
              {`${data.dailyUsersGrowth}% Peningkatan berbanding minggu lalu`}
            </Typography>
          </Box>
          <Box
            sx={{
              borderRadius: 2.5,
              backgroundColor: "#fff",
              //display: "inline",
              px: 2,
              py: 2,
              mb: 1,
            }}
          >
            <Box sx={{display: "flex", justifyContent: "space-between"}}>
              <Typography sx={headerStyle}>
                {'Jumlah Pengguna Berulang'}
              </Typography>
              <RepeatingUserIcon/>
            </Box>
            <Typography sx={{
              pt: 4, fontWeight: "500",
              fontSize: 48, color: '#666666', fontStyle: 'medium'
            }}>
              {data.returningUsers}
            </Typography>
            <Typography sx={labelStyle}>
              {`${data.returningUsersGrowth}% Peningkatan berbanding minggu lalu`}
            </Typography>
          </Box>
          {/*<Box
        sx={{
          borderRadius: 2.5,
          backgroundColor: "#fff",
          //display: "inline",
          px: 2,
          py: 2,
          mb: 1,
        }}
      >
        <Box sx={{display: "flex", justifyContent: "space-between"}}>
          <Typography sx={headerStyle}>
            {'Jumlah Pengguna Tak Berdaftar'}
          </Typography>
          <UnregisteredUserIcon/>
        </Box>
        <Typography sx={{
          pt: 4, fontWeight: "500",
          fontSize: 48, color: '#666666', fontStyle: 'medium'
        }}>
          {'235'}
        </Typography>
        <Typography sx={labelStyle}>
          {'9.3% Peningkatan berbanding minggu lalu'}
        </Typography>
      </Box>*/}
        </Box>
        <Box sx={{display: "flex", gap: 3}}>
          <Box sx={{width: "75%"}}>
            <Box
              sx={{
                borderRadius: 2.5,
                backgroundColor: "#fff",
                //display: "inline",
                px: 2,
                py: 2,
                mb: 1,
              }}
            >
              <Typography sx={{...headerStyle, pb: 5}}>
                {'Jumlah Pelawat'}
              </Typography>
              <HebahanChart data={data.mappedData}/>
            </Box>
            <Box
              sx={{
                borderRadius: 2.5,
                backgroundColor: "#fff",
                //display: "inline",
                px: 2,
                py: 2,
                mb: 1,
              }}
            >
              <Box
                sx={{
                  borderRadius: 2.5,
                  backgroundColor: "#fff",
                  border: "1px solid #D9D9D9",
                  //flex: 5,
                  //display: "inline",
                  px: 2,
                  py: 2,
                  mb: 1,
                }}
              >
                <Typography sx={{...headerStyle, pb: 5}}>
                  {'Pantauan Engagement Artikel'}
                </Typography>
                <CustomDataGrid
                  url={`${API_URL}/society/posting`}
                  columns={columns}
                  noResultMessage={t("noData")}
                  isFiltered
                  type={2}
                  //setRefetchData={setRefetchData}
                  //refetchData={refetchData}
                />
              </Box>
            </Box>
          </Box>
          <Box sx={{width: "25%"}}>
            <InternalHebahanDashboardSidebar/>
          </Box>
        </Box>
      </Box>)
}

export default InternalHebahanDashboard
