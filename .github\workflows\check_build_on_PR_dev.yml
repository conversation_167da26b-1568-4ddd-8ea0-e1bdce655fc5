name: Check Build on PR (dev)

on:
  pull_request:
    branches:
      - development

jobs:
  build:
    runs-on: ubuntu-latest

    steps:
      - name: Checkout Repository
        uses: actions/checkout@v4

      - name: Set up Node.js
        uses: actions/setup-node@v4
        with:
          # TODO: update to latest LTS version of node.js if possible
          # node-version: lts/*
          node-version: 18
          cache: 'npm'

      - name: Install dependencies
        run: npm ci

      - name: Build the project for development
        env:
          # TODO: add VITE_BACKEND_URL env and BACKEND_URL_DEV secret in github
          VITE_BACKEND_URL: ${{ secrets.BACKEND_URL_DEV }}
          VITE_APP_ENV: development
          VITE_AWS_API_KEY_REGION: 'ap-southeast-5'
          VITE_AWS_API_KEY: ${{ secrets.MAP_API_KEY_DEV }}
          VITE_AWS_PLACE_INDEX: 'explore.place.Grab'
          VITE_AWS_MAP_NAME: 'explore.map.Grab'
          NODE_OPTIONS: --max-old-space-size=4096
          VITE_GA_MEASUREMENT_ID: ${{ secrets.GA_MEASUREMENT_ID }}
        run: npm run build
