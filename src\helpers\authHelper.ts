import { TOKEN_KEY } from "@/helpers";

class AuthHelper {
  static getToken(): string | null {
    return localStorage.getItem(TOKEN_KEY);
  }

  static setToken(token: string) {
    localStorage.setItem(TOKEN_KEY, token);
  }

  static clearToken() {
    localStorage.removeItem(TOKEN_KEY);
  }

  static getUserAuthorities(): string[] {
    const token = this.getToken();
    if (!token) return [];

    try {
      const payload = JSON.parse(atob(token.split('.')[1])); // Decode JWT (base64)
      const authorities = payload.authorities.map((auth: string) => auth.toLowerCase());
      // console.log('getUserAuthorities', authorities) //remove after testing
      return authorities || [];
    } catch (error) {
      console.error('Invalid token format:', error);
      return [];
    }
  }

  static hasAuthority(requiredAuthorities: string | string[]): boolean {
    // console.log('hasAuthority', requiredAuthorities) //remove after testing
    const userAuthorities = this.getUserAuthorities()?.map(a => a.toLowerCase()) ?? [];

    if (!userAuthorities.length) return false;

    const required = Array.isArray(requiredAuthorities)
      ? requiredAuthorities.map(a => a.toLowerCase())
      : [requiredAuthorities.toLowerCase()];

    return required.some(auth => userAuthorities.includes(auth));
  }

  static hasPageAccess(permissionLabel: string, accessType: number): boolean {
    // console.log('hasPageAccess', permissionLabel, accessType) //remove after testing
    const userAuthorities: string[] = this.getUserAuthorities();

    if (!userAuthorities || userAuthorities.length === 0) {
      return false;
    }

    // Check if user has the specific permission with the numeric access type
    const permissionString = `${permissionLabel}:${accessType}`;
    return userAuthorities.includes(permissionString.toLowerCase());
  }
}

export default AuthHelper;
