import { useState, useEffect, useRef, useMemo } from "react";
import { useParams, useLocation, useNavigate } from "react-router-dom";
import { CrudFilter, useGetIdentity, useNotification } from "@refinedev/core";
import {
  FieldValues,
  SubmitHandler,
  FormProvider,
  useFormContext,
} from "react-hook-form";
import { useTranslation } from "react-i18next";
import { useForm } from "@refinedev/react-hook-form";
import { MALAYSIA } from "@/helpers/enums";
import { useSenaraiContext } from "@/pages/pertubuhan/SenaraiContext";
import { usePembubaranContext } from "../../PembubaranProvider";
import {
  DocumentUploadType,
  filterEmptyValuesOnObject,
  getLocalStorage,
  useQuery,
  useMutation,
  useUploadPresignedUrl,
  useGetDocument,
  MeetingTypeOption,
  getMeetingType,
  globalStyles,
  omitKeysFromObject,
} from "@/helpers";
import { yupResolver } from "@hookform/resolvers/yup";

import {
  Box,
  Typography,
  SelectChangeEvent,
  CircularProgress,
  useTheme,
  Grid,
  IconButton,
} from "@mui/material";
import {
  Label,
  ButtonPrimary,
  ButtonOutline,
  FormFieldRow,
  TextFieldController,
  SelectFieldController,
  DatePickerController,
  MapPickerController,
  TimePickerController,
  DisabledTextField,
  FileUploader,
  FileUploadController,
  CustomPickersDay,
} from "@/components";
import MemberAttendances from "./member-attendances";

import { IMeetingDetail, IUser } from "@/types";

import { useMeetingSchema } from "@/schemas";
import dayjs from "dayjs";
import { PickersDay } from "@mui/x-date-pickers";
import MessageDialog from "@/components/dialog/message";
import { EyeIcon } from "@/components/icons";

const documentTypeOptions = [
  {
    label: "Minit mesyuarat",
    value: 1,
  },
  {
    label: "Dokumen Lain",
    value: 2,
  },
];

const labelStyle = {
  fontSize: "14px",
  color: "#666666",
  fontWeight: "400 !important",
};

const MeetingForm: React.FC = () => {
  const theme = useTheme();
  const navigate = useNavigate();
  const { t, i18n } = useTranslation();
  const { open } = useNotification();
  const location = useLocation();
  const { liquidationId } = useParams();
  const { data: user } = useGetIdentity<IUser>();
  const {
    handleNextPembubaran: handleNext,
    isLoadingSocietyDetail,
    societyDetail,
  } = useSenaraiContext();
  const {
    watch: watchLiquidationForm,
    control: liquidationFormControl,
    setValue: setLiquidationFormValue,
    getValues,
    setError,
    clearErrors,
  } = useFormContext();
  const {
    meetingDetailData,
    liquidationDetailData,
    isFetchingLiquidationDetail,
    isEditable,
    isViewOnly,
  } = usePembubaranContext();
  const classes = globalStyles();
  const liquidationValue = getValues();

  const isMyLanguage = i18n.language === "my";

  const isCawangan = location.pathname.includes("/cawangan");
  const isCreate = location.pathname.includes("/create");

  const branchId = isCawangan
    ? location.state?.branchId
    : liquidationValue.branchId;
  const primary = theme.palette.primary.main;

  const liquidationDocumentType = watchLiquidationForm(
    "liquidationDocumentType"
  );

  const isPerlembagaanFaedahBersama =
    societyDetail?.constitutionType === "Perlembagaan Faedah Bersama";

  const searchMeetingUrl = isCawangan
    ? `society/meeting/findByBranchId/${branchId}`
    : `society/meeting/findBySocietyId/${liquidationValue.societyId}`;

  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [caraPembubaranFile, setCaraPembubaranFile] = useState<File | null>(
    null
  );
  const [akuanBerkanunFile, setAkuanBerkanunFile] = useState<File | null>(null);

  const [filePreview, setFilePreview] = useState("");
  const [caraPembubaranPreview, setCaraPembubaranPreview] = useState("");
  const [akuanBerkanunPreview, setAkuanBerkanunPreview] = useState("");

  const [supportFiles, setSupportFiles] = useState<File[]>([]);

  const [currentDocId, setCurrentDocId] = useState<string | number | null>(
    null
  );
  const [duration, setDuration] = useState("");
  const [isUploadingDocuments, setIsUploadingDocuments] = useState(false);
  const [existingDocuments, setExistingDocuments] = useState<any>({});
  const { upload: uploadMeetingMinuteFile, isLoading: isUploading } =
    useUploadPresignedUrl({
      onSuccessUpload: () => {
        if (currentDocId) deleteDocument();
        setSelectedFile(null);
        handleNext();
      },
    });

  const { upload: uploadPerlembagaanFile } = useUploadPresignedUrl();

  const { getDocument: getMeetingMinuteDocument } = useGetDocument({
    onSuccess: (data) => {
      setExistingDocuments(data);
      setFilePreview(data?.url ?? "");
      setCurrentDocId(data?.id ?? null);
    },
  });

  const { getDocument: getCaraPembubaranDocument } = useGetDocument({
    onSuccess: (data) => {
      if (data) {
        setLiquidationFormValue("surat_cara_pembubaran", data?.name);
        setCaraPembubaranPreview(data?.url ?? "");
      }
    },
  });

  const { getDocument: getAkuanBerkanunDocument } = useGetDocument({
    onSuccess: (data) => {
      if (data) {
        setLiquidationFormValue("surat_akuan_berkanun", data?.name);
        setAkuanBerkanunPreview(data?.url ?? "");
      }
    },
  });

  const { fetch: deleteDocument, isLoading: isDeletingDocument } = useMutation({
    url: `document/deleteDocument?id=${currentDocId}`,
    method: "put",
  });

  const [availableDateList, setAvailableDateList] = useState<string[]>([]);
  const [meetings, setMeetings] = useState<IMeetingDetail[]>([]);
  const [filteredMeetings, setFilteredMeeting] = useState<IMeetingDetail[]>([]);
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [savedMeetingDate, setSavedMeetingDate] = useState<any>("");
  const [selectedDate, setSelectedDate] = useState<any>("");
  const [meetingData, setMeetingData] = useState<any>("");

  const {
    data: meetingSearchListRes,
    isLoading: isLoadingMeetingSearchList,
    refetch: getMeetingSearchList,
  } = useQuery({
    url: searchMeetingUrl,
    filters: [
      { field: "pageSize", operator: "eq", value: 100 },
      { field: "meetingType", operator: "eq", value: [2, 3, 4, 5] },
    ],
    autoFetch: true,
    onSuccess: (data) => {
      const meetings = data?.data?.data ?? [];
      const availableList = meetings.map((item: any) => {
        return item.meetingDate;
      });

      setMeetings(meetings);
      setAvailableDateList(availableList);
    },
  });

  const {
    data: meetingSearchDetail,
    isLoading: isLoadingMeetingSearchDetail,
    refetch: getMeetingSearchDetail,
    reset: resetMeetingDetail,
  } = useQuery({
    url: `society/meeting/${liquidationValue.meetingId}`,
    autoFetch: false,
    onSuccess: (data) => {
      const meetingDetail = data?.data?.data;
      setMeetingData(meetingDetail);
      if (meetingDetail) {
        setLiquidationFormValue(
          "committeeAttendCount",
          meetingDetail.meetingMemberAttendances.length
        );
      }
    },
  });

  const handleDownloadDocument = async (filePath: string, name: string) => {
    const response = await fetch(filePath);
    const blob = await response.blob();
    const url = window.URL.createObjectURL(blob);

    const link = document.createElement("a");
    link.href = url;
    link.download = name;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    window.URL.revokeObjectURL(url);
  };

  const { fetch: updateLiquidation, isLoading: isUpdatingLiquidation } =
    useMutation({
      url: "society/liquidate/update",
      method: "put",
      onSuccess: () => {
        const { id } = getValues();

        handleUploadPerlembagaanFadahBersama(id);
        handleNext();
      },
      msgSuccess: isMyLanguage ? "Berjaya Dikemaskini" : "Successfully Updated",
    });

  const { fetch: createLiquidation, isLoading: isCreateLiquidation } =
    useMutation({
      url: "society/liquidate/create",
      method: "post",
      onSuccess: (data) => {
        const id = data?.data?.data;

        if (!id) return;

        setLiquidationFormValue("id", id);
        handleUploadPerlembagaanFadahBersama(id);

        if (meetingDetail) handleNext();
      },
    });

  const handleUploadPerlembagaanFadahBersama = (id: string | number): void => {
    if (isPerlembagaanFaedahBersama) {
      const upload = (
        file: File | null,
        code: "SURAT_CARA_PEMBUBARAN" | "SURAT_AKUAN_BERKANUN"
      ) => {
        if (!file) return;

        uploadPerlembagaanFile({
          params: {
            type: DocumentUploadType.LIQUIDATION,
            societyId: liquidationValue.societyId,
            societyNo: liquidationValue.societyNo,
            branchId: liquidationValue.branchId,
            branchNo: liquidationValue.branchNo,
            liquidationId: id,
            icNo: user?.identificationNo,
            code,
          },
          file,
        });
      };

      upload(caraPembubaranFile, "SURAT_CARA_PEMBUBARAN");
      upload(akuanBerkanunFile, "SURAT_AKUAN_BERKANUN");
    }
  };

  const handleCreateLiquidation = (meetingId?: string | number) => {
    const keysToSkip = ["surat_akuan_berkanun", "surat_cara_pembubaran"];
    const liquidationFormValues = getValues();
    const meetingFormValues = getMeetingFormValues();
    const payload = filterEmptyValuesOnObject(liquidationFormValues);

    if (meetingId) {
      payload.meetingId = meetingId;
      payload.committeeAttendCount = meetingFormValues.memberAttendances.length;
    }

    if (isCawangan) {
      const branchId = location.state.branchId ?? 0;
      const branchNo = location.state.branchNo ?? "";

      payload.branchId = branchId;
      payload.branchNo = branchNo;
      payload.branchLiquidation = 1;
    }

    const filterPayload = omitKeysFromObject(payload, keysToSkip);

    createLiquidation(filterPayload);
  };

  const meetingList = getLocalStorage("meeting_list", []);
  const addressList = getLocalStorage("address_list", []);

  const meetingSearchList = meetingSearchListRes?.data?.data ?? [];
  const meetingDetail =
    meetingDetailData || meetingSearchDetail?.data?.data || null;

  const disabledState = true;
  const requiredState = false;

  const isMutating =
    isUpdatingLiquidation ||
    isCreateLiquidation ||
    isUploading ||
    isUploadingDocuments ||
    isDeletingDocument;

  const defaultFormValues = {
    societyId: liquidationValue.societyId,
    societyNo: liquidationValue.societyNo,
    branchId: liquidationValue.branchId,
    branchNo: liquidationValue.branchNo,
    meetingType: 5,
    meetingPlace: "",
    meetingMethod: "",
    meetingPurpose: "",
    platformType: "",
    meetingDate: "",
    meetingTime: "",
    GISInformation: "",
    meetingAddress: "",
    state: "",
    district: "",
    city: "",
    postcode: "",
    totalAttendees: 0,
    meetingContent: "ya",
    openingRemarks: "",
    mattersDiscussed: "",
    otherMatters: "",
    closing: "",
    providedBy: user?.name ?? "",
    confirmBy: "",
    meetingMinute: "",
    status: 1,
    memberAttendances: [],
  };

  const schema = useMeetingSchema({
    meetingDate: requiredState,
    meetingType: requiredState,
    meetingMethod: requiredState,
    meetingTime: requiredState,
    meetingTimeTo: requiredState,
    meetingMinute: requiredState,
    meetingPurpose: requiredState,
  });

  const methods = useForm<FieldValues>({
    defaultValues: defaultFormValues,
    resolver: yupResolver<FieldValues>(schema),
  });

  const {
    control,
    setValue,
    watch,
    register,
    handleSubmit,
    getValues: getMeetingFormValues,
    reset: resetForm,
  } = methods;

  const meetingMethod = watch("meetingMethod");
  const meetingTime = watch("meetingTime");
  const meetingTimeTo = watch("meetingTimeTo");

  const meetingMethodOptions = useMemo(
    () =>
      meetingList
        ?.filter((item: any) => item.pid === 2)
        .map((item: any) => ({
          value: String(item.id),
          label: item.nameBm,
        })),
    [meetingList]
  );
  const meetingPlatformOptions = useMemo(
    () =>
      meetingList
        ?.filter((item: any) => item.pid === 3)
        .map((item: any) => ({
          value: String(item.id),
          label: item.nameBm,
        })),
    [meetingList]
  );
  const cityOptions = useMemo(
    () =>
      addressList
        .filter((item: any) => item.pid === MALAYSIA)
        .map((item: any) => ({
          value: String(item.id),
          label: item.name,
        })),
    [addressList]
  );
  const districtOptions = useMemo(
    () =>
      addressList
        .filter((item: any) => item.pid === Number(watch("state")))
        .map((item: any) => ({
          value: String(item.id),
          label: item.name,
        })),
    [addressList, watch("state")]
  );

  const [formData, setFormData] = useState<any>({});
  const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      setSelectedFile(file);
      setValue("meetingMinute", file.name, { shouldValidate: true });
      setFormData((prevState: any) => ({
        ...prevState,
        meetingMinute: file.name,
      }));
    }
  };

  useEffect(() => {
    if (!selectedDate || meetings.length === 0) return;

    const filterDate = dayjs(
      liquidationValue?.meetingDate ?? selectedDate
    ).format("YYYY-MM-DD");

    const filtered = meetings.filter(
      (meeting) =>
        dayjs(meeting.meetingDate).format("YYYY-MM-DD") === filterDate
    );
    handleSetFilteredMeeting(filtered);
  }, [
    meetings,
    selectedDate,
    liquidationValue?.meetingDate,
    liquidationValue?.meetingId,
  ]);

  const handleSearchMeetingList = (date: string | null) => {
    if (!date) return;
    resetMeetingDetail();
    setSelectedFile(null);
    setFilePreview("");
    setLiquidationFormValue("meetingId", "");
  };

  useEffect(() => {
    if (liquidationValue.meetingId && liquidationDocumentType === 1) {
      setSelectedDate(liquidationValue.meetingDate);
      getMeetingSearchDetail();
      getMeetingMinuteDocument([
        {
          field: "meetingId",
          operator: "eq",
          value: liquidationValue.meetingId,
        },
        {
          field: "type",
          operator: "eq",
          value: DocumentUploadType.MEETING,
        },
      ]);
    }
  }, [liquidationValue.meetingId]);

  const handleSelectMeetingSearch = (event: SelectChangeEvent<any>) => {
    const meetingId = event.target.value;

    if (meetingId) {
      getMeetingSearchDetail();
      getMeetingMinuteDocument([
        {
          field: "meetingId",
          operator: "eq",
          value: meetingId,
        },
        {
          field: "type",
          operator: "eq",
          value: DocumentUploadType.MEETING,
        },
      ]);
    }
  };

  const setMeetingDetail = (data: IMeetingDetail) => {
    const keysToSkip = new Set([
      "createdBy",
      "createdDate",
      "modifiedBy",
      "modifiedDate",
      "id",
      "societyId",
      "societyNo",
      "branchId",
      "branchNo",
      "meetingMemberAttendances",
      "meetingTime",
      "meetingTimeTo",
      "meetingType",
    ]);

    const formattedTime = (time: string | undefined): string => {
      return time ? time.slice(0, 5) : "";
    };

    const findMeetingType = MeetingTypeOption.find(
      (meeting) => meeting.label === data.meetingType
    );

    const meetingType =
      typeof data.meetingType === "string"
        ? findMeetingType?.value ?? data.meetingType
        : data.meetingType;

    Object.entries(data).forEach(([key, value]) => {
      if (!keysToSkip.has(key)) {
        setValue(key as keyof FieldValues, value);
      }
    });

    if (data.meetingTime) {
      setValue("meetingTime", formattedTime(data.meetingTime));
    }
    if (data.meetingTimeTo) {
      setValue("meetingTimeTo", formattedTime(data.meetingTimeTo));
    }
    if (data.meetingType) {
      setValue("meetingType", meetingType);
    }
    if (data.meetingMemberAttendances) {
      setValue("memberAttendances", data.meetingMemberAttendances);
    }
  };

  const resetFormExceptMeetingDate = () => {
    const currentMeetingDate = watch("meetingDate");

    resetForm({
      ...defaultFormValues,
      meetingDate: currentMeetingDate,
    });
  };

  const onSubmit: SubmitHandler<FieldValues> = () => {
    if (isViewOnly) return handleNext();

    const {
      id,
      liquidationDocumentType,
      meetingId,
      meetingDate,
      committeeAttendCount,
      surat_akuan_berkanun,
      surat_cara_pembubaran,
    } = getValues();

    clearErrors(["surat_cara_pembubaran", "surat_akuan_berkanun"]);

    if (isCreate && isPerlembagaanFaedahBersama) {
      let hasError = false;

      if (!surat_cara_pembubaran) {
        setError("surat_cara_pembubaran", {
          type: "required",
          message: t("validation.required"),
        });
        hasError = true;
      }

      if (!surat_akuan_berkanun) {
        setError("surat_akuan_berkanun", {
          type: "required",
          message: t("validation.required"),
        });
        hasError = true;
      }

      if (hasError) return;
    }

    if (isEditable || id) {
      const basePayload = { id, liquidationDocumentType };

      const payload =
        liquidationDocumentType === 1
          ? {
              ...basePayload,
              meetingId,
              meetingDate,
              committeeAttendCount,
            }
          : basePayload;

      return updateLiquidation({
        ...payload,
        applicationStatusCode: 1,
      });
    }

    if (liquidationDocumentType === 2) {
      handleCreateLiquidation();
      setIsUploadingDocuments(true);
      return;
    }

    handleCreateLiquidation(meetingDetail.id);
  };

  const handleSetFilteredMeeting = (meetings: IMeetingDetail[]) => {
    setFilteredMeeting(meetings);
    if (meetings && meetings.length > 0) {
      setIsDialogOpen(false);
    } else {
      setIsDialogOpen(true);
    }
  };

  const newMeetingTypeOption = MeetingTypeOption.filter(
    (item: any) => item?.id !== 1
  ).map((i) => ({
    value: String(i.value),
    label: i.label,
  }));

  useEffect(() => {
    const { meetingDate } = liquidationValue;

    if (meetingDate) {
      getMeetingSearchList({
        filters: [
          {
            field: "pageSize",
            operator: "eq",
            value: 100,
          },
          {
            field: "pageNo",
            operator: "eq",
            value: 1,
          },
          {
            field: "societyId",
            operator: "eq",
            value: liquidationValue.societyId,
          },
          {
            field: "branchId",
            operator: "eq",
            value: branchId,
          },
          {
            field: "meetingDate",
            operator: "eq",
            value: meetingDate,
          },
        ],
      });
    }
  }, []);

  useEffect(() => {
    if (meetingDetail) {
      setMeetingDetail(meetingDetail);
    } else {
      resetFormExceptMeetingDate();
    }
  }, [meetingSearchDetail, meetingDetailData]);

  useEffect(() => {
    if (meetingTime && meetingTimeTo) {
      const start = new Date(`1970-01-01T${meetingTime}:00`);
      const end = new Date(`1970-01-01T${meetingTimeTo}:00`);

      if (end >= start) {
        const diffMinutes = (end.getTime() - start.getTime()) / (1000 * 60);
        setDuration(`${diffMinutes} ${t("minute")}`);
      } else {
        setDuration(t("validation.invalidTimeDuration"));
      }
    } else {
      setDuration("");
    }
  }, [meetingTime, meetingTimeTo]);

  useEffect(() => {
    if (isCreate || !isPerlembagaanFaedahBersama) return;

    const baseFilters: CrudFilter[] = [
      {
        field: "societyId",
        operator: "eq",
        value: liquidationValue.societyId,
      },
      {
        field: "liquidationId",
        operator: "eq",
        value: liquidationId,
      },
      {
        field: "type",
        operator: "eq",
        value: DocumentUploadType.LIQUIDATION,
      },
    ];

    getAkuanBerkanunDocument([
      ...baseFilters,
      {
        field: "code",
        operator: "eq",
        value: "SURAT_AKUAN_BERKANUN",
      },
    ]);

    getCaraPembubaranDocument([
      ...baseFilters,
      {
        field: "code",
        operator: "eq",
        value: "SURAT_CARA_PEMBUBARAN",
      },
    ]);
  }, []);

  if (
    isFetchingLiquidationDetail ||
    isLoadingSocietyDetail ||
    (liquidationId && !liquidationDetailData)
  )
    return (
      <Box sx={{ display: "flex", justifyContent: "center" }}>
        <CircularProgress />
      </Box>
    );

  return (
    <>
      <FormProvider {...methods}>
        <form
          onSubmit={handleSubmit(onSubmit, (errors) =>
            console.log("Validation errors", errors)
          )}
        >
          <Box className={classes.section} mb={2}>
            <Box className={classes.sectionBox}>
              <Typography
                fontSize="14px"
                color={primary}
                fontWeight={500}
                marginBottom="20px"
              >
                Kaedah Keputusan
              </Typography>

              <FormFieldRow
                label={<Label text="Jenis Dokumen" />}
                value={
                  <SelectFieldController
                    options={documentTypeOptions}
                    control={liquidationFormControl}
                    name="liquidationDocumentType"
                  />
                }
              />
            </Box>
          </Box>

          {isPerlembagaanFaedahBersama && liquidationDocumentType && (
            <>
              <Box className={classes.section} mb={2}>
                <Box className={classes.sectionBox}>
                  <Typography
                    fontSize="14px"
                    color={primary}
                    fontWeight={500}
                    marginBottom="20px"
                  >
                    Surat Cara Pembubaran
                  </Typography>

                  <FormFieldRow
                    align="flex-start"
                    label={<Label text="Surat Cara" required />}
                    value={
                      <FileUploadController
                        control={liquidationFormControl}
                        name="surat_cara_pembubaran"
                        accept=".pdf"
                        filePreview={caraPembubaranPreview}
                        onFileSelect={(file) => setCaraPembubaranFile(file)}
                        allowedTypes={["application/pdf"]}
                        disabled={isViewOnly}
                      />
                    }
                  />
                </Box>
              </Box>

              <Box className={classes.section} mb={2}>
                <Box className={classes.sectionBox}>
                  <Typography
                    fontSize="14px"
                    color={primary}
                    fontWeight={500}
                    marginBottom="20px"
                  >
                    Surat Akuan Berkanun
                  </Typography>

                  <FormFieldRow
                    align="flex-start"
                    label={<Label text="Surat Akuan Berkanun" required />}
                    value={
                      <FileUploadController
                        control={liquidationFormControl}
                        name="surat_akuan_berkanun"
                        accept=".pdf"
                        filePreview={akuanBerkanunPreview}
                        onFileSelect={(file) => setAkuanBerkanunFile(file)}
                        allowedTypes={["application/pdf"]}
                        disabled={isViewOnly}
                      />
                    }
                  />
                </Box>
              </Box>
            </>
          )}

          {liquidationDocumentType === 1 && (
            <Box
              sx={{
                backgroundColor: "white",
                padding: "18px 16px",
                borderRadius: "14px",
                marginBottom: 1,
              }}
            >
              <Box
                sx={{
                  borderRadius: "10px",
                  padding: "41px 25px 25px",
                  border: "0.5px solid #DADADA",
                  marginBottom: "13px",
                }}
              >
                <Typography
                  fontSize="14px"
                  color={primary}
                  fontWeight={500}
                  marginBottom="20px"
                >
                  {t("dissolutionMeetingInformation")}
                </Typography>

                <FormFieldRow
                  label={<Label text={t("meetingDate")} required />}
                  value={
                    <DatePickerController
                      name="meetingDate"
                      slots={{
                        day: (dayProps) => (
                          <CustomPickersDay
                            {...dayProps}
                            availableDate={availableDateList}
                          />
                        ),
                      }}
                      control={control}
                      onChange={(date) => {
                        setSavedMeetingDate(date);
                        setSelectedDate(date);
                        handleSearchMeetingList(date);
                        const filteredMeeting = meetings.filter(
                          (meeting) =>
                            meeting.meetingDate ===
                            dayjs(
                              liquidationValue?.meetingDate
                                ? liquidationValue?.meetingDate
                                : date
                            ).format("YYYY-MM-DD")
                        );
                        handleSetFilteredMeeting(filteredMeeting);
                        setLiquidationFormValue("meetingDate", date);
                        //clear meeting data
                        setMeetingData("");
                      }}
                      disabled={isViewOnly}
                    />
                  }
                />

                {meetingSearchList.length > 0 ? (
                  <>
                    <FormFieldRow
                      label={<Label text={t("meetingList")} required />}
                      value={
                        <>
                          {filteredMeetings.length > 0 ? (
                            <SelectFieldController
                              name="meetingId"
                              control={liquidationFormControl}
                              options={filteredMeetings.map((meeting) => ({
                                value: meeting.id,
                                label: `${
                                  getMeetingType(Number(meeting.meetingType))
                                    ?.label
                                } (${meeting.meetingDate})`,
                              }))}
                              placeholder={t("selectPlaceholder")}
                              onChange={handleSelectMeetingSearch}
                            />
                          ) : (
                            <DisabledTextField value="Tiada rekod mesyuarat. Sila isi maklumat mesyuarat yang baharu." />
                          )}
                        </>
                      }
                    />

                    {meetingData ? (
                      <>
                        <FormFieldRow
                          label={<Label text={t("meetingType")} required />}
                          value={
                            <SelectFieldController
                              name="meetingType"
                              control={control}
                              options={newMeetingTypeOption}
                              placeholder={t("selectPlaceholder")}
                              disabled
                            />
                          }
                        />
                        <FormFieldRow
                          label={<Label text={t("kaedahMesyuarat")} required />}
                          value={
                            <SelectFieldController
                              name="meetingMethod"
                              control={control}
                              options={meetingMethodOptions}
                              placeholder={t("selectPlaceholder")}
                              disabled={disabledState}
                            />
                          }
                        />
                        {["9", "10"].includes(meetingMethod) && ( // hybrid or tailan meeting method only
                          <FormFieldRow
                            label={<Label text={t("jenisPlatform")} />}
                            value={
                              <SelectFieldController
                                name="platformType"
                                control={control}
                                options={meetingPlatformOptions}
                                placeholder={t("selectPlaceholder")}
                                disabled={disabledState}
                              />
                            }
                          />
                        )}
                        <FormFieldRow
                          label={<Label text={t("tujuanMesyuarat")} required />}
                          value={
                            <TextFieldController
                              name="meetingPurpose"
                              control={control}
                              disabled={disabledState}
                            />
                          }
                        />
                        <FormFieldRow
                          align="flex-start"
                          label={<Label text={t("time")} required />}
                          value={
                            <Box display="flex" gap={2}>
                              <TimePickerController
                                name="meetingTime"
                                control={control}
                                sx={{
                                  width: "140px",
                                }}
                                disabled={disabledState}
                              />

                              <TimePickerController
                                name="meetingTimeTo"
                                control={control}
                                sx={{
                                  width: "140px",
                                }}
                                disabled={disabledState}
                              />

                              <Typography
                                fontSize="12px"
                                lineHeight="37px"
                                fontWeight="400 !important"
                              >
                                {duration && duration}
                              </Typography>
                            </Box>
                          }
                        />
                      </>
                    ) : null}
                  </>
                ) : null}
              </Box>

              {meetingData ? (
                <>
                  {meetingMethod === "8" && ( //bersemuka meething method only
                    <Box
                      sx={{
                        borderRadius: "10px",
                        padding: "41px 25px 25px",
                        border: "0.5px solid #DADADA",
                        marginBottom: "15px",
                      }}
                    >
                      <Typography
                        fontSize="14px"
                        color={primary}
                        fontWeight={500}
                        marginBottom="20px"
                      >
                        {t("alamatTempatMesyuarat")}
                      </Typography>

                      <FormFieldRow
                        label={<Label text={t("meetingPlace")} />}
                        value={
                          <TextFieldController
                            name="meetingPlace"
                            control={control}
                            disabled={disabledState}
                          />
                        }
                      />

                      <FormFieldRow
                        align="flex-start"
                        label={<Label text={t("locationMap")} />}
                        value={
                          <MapPickerController
                            control={control}
                            name="GISInformation"
                            sx={{ borderRadius: "5px" }}
                            disabled={disabledState}
                          />
                        }
                      />

                      <FormFieldRow
                        label={<Label text={t("meetingPlaceAddress")} />}
                        value={
                          <TextFieldController
                            name="meetingAddress"
                            control={control}
                            disabled={disabledState}
                          />
                        }
                      />

                      <FormFieldRow
                        label={<Label text={t("state")} />}
                        value={
                          <SelectFieldController
                            name="state"
                            control={control}
                            options={cityOptions}
                            placeholder={t("selectPlaceholder")}
                            onChange={() => setValue("district", "")}
                            disabled={disabledState}
                          />
                        }
                      />

                      <FormFieldRow
                        label={<Label text={t("district")} />}
                        value={
                          <SelectFieldController
                            name="district"
                            control={control}
                            options={districtOptions}
                            placeholder={t("selectPlaceholder")}
                            disabled={!watch("state") || disabledState}
                          />
                        }
                      />

                      <FormFieldRow
                        label={<Label text={t("city")} />}
                        value={
                          <TextFieldController
                            name="city"
                            control={control}
                            disabled={disabledState}
                          />
                        }
                      />

                      <FormFieldRow
                        label={<Label text={t("postcode")} />}
                        value={
                          <TextFieldController
                            name="postcode"
                            control={control}
                            disabled={disabledState}
                          />
                        }
                      />
                    </Box>
                  )}

                  <MemberAttendances
                    disabledState={disabledState}
                    isEditable={isEditable}
                    isViewOnly={isViewOnly}
                    meetingDetail={!!meetingDetail}
                  />

                  <Box
                    sx={{
                      borderRadius: "10px",
                      padding: "41px 25px 25px",
                      border: "0.5px solid #DADADA",
                    }}
                  >
                    <Typography
                      fontSize="14px"
                      color={primary}
                      fontWeight={500}
                      marginBottom="20px"
                    >
                      Minit Mesyuarat
                    </Typography>
                    {/*  */}
                    <Grid container spacing={2}>
                      <Grid item xs={12} sm={4}>
                        <Typography sx={labelStyle}>
                          {t("minitMesyuaratViewOnly")}{" "}
                          <span style={{ color: "red" }}>*</span>
                        </Typography>
                      </Grid>
                      <Grid item xs={12} sm={8}>
                        {existingDocuments ? (
                          <Box
                            sx={{
                              border: "1px solid #DADADA",
                              borderRadius: "8px",
                              p: 3,
                              display: "flex",
                              flexDirection: "column",
                              alignItems: "center",
                              gap: 1,
                              backgroundColor: "#FFFFFF",
                              "&:hover": {
                                backgroundColor: "#F9FAFB",
                              },
                              cursor: "pointer",
                            }}
                            onClick={(e) => {
                              e.stopPropagation();
                              handleDownloadDocument(
                                existingDocuments?.url,
                                existingDocuments?.name
                              );
                            }}
                          >
                            <Box
                              sx={{
                                width: "100%",
                                display: "flex",
                                flexDirection: "column",
                                alignItems: "center",
                                gap: 1,
                              }}
                            >
                              <Box
                                sx={{
                                  width: "100%",
                                  display: "flex",
                                  alignItems: "center",
                                  justifyContent: "space-between",
                                }}
                              >
                                <Box>
                                  <Typography
                                    sx={{
                                      color: "#222222",
                                      fontSize: "14px",
                                      textAlign: "center",
                                    }}
                                  >
                                    {existingDocuments?.name}
                                  </Typography>
                                </Box>
                                <IconButton
                                  sx={{
                                    padding: 0,
                                  }}
                                >
                                  <EyeIcon color="#666666" />
                                </IconButton>
                              </Box>
                            </Box>
                          </Box>
                        ) : (
                          <Box
                            sx={{
                              border: "2px solid #DADADA",
                              borderRadius: "8px",
                              p: 2,
                              gap: 2,
                              textAlign: "center",
                              cursor: "pointer",
                              height: "200px",
                              display: "flex",
                              flexDirection: "column",
                              justifyContent: "center",
                              alignItems: "center",
                            }}
                            onClick={() => {
                              const element =
                                document.getElementById("meetingMinute");
                              if (element) {
                                element.click();
                              }
                            }}
                          >
                            {selectedFile ? (
                              <Typography sx={{ color: "#147C7C", mb: 1 }}>
                                {selectedFile.name}
                              </Typography>
                            ) : (
                              <>
                                <Box
                                  sx={{
                                    width: 50,
                                    aspectRatio: "1/1",
                                    display: "flex",
                                    justifyContent: "center",
                                    alignContent: "center",
                                    textAlign: "center",
                                    borderRadius: 20,
                                    mb: 2,
                                    // p: 5,
                                    bgcolor: "#F2F4F7",
                                  }}
                                >
                                  <img
                                    width={30}
                                    src={"/uploadFileIcon.svg"}
                                    alt={"view"}
                                  />
                                </Box>

                                <Typography
                                  sx={{
                                    color: "var(--primary-color)",
                                    fontWeight: "500",
                                    fontSize: "14px",
                                  }}
                                >
                                  {t("muatNaik")}
                                </Typography>
                                <Typography
                                  sx={{
                                    color: "#667085",
                                    fontWeight: "400",
                                    fontSize: "12px",
                                  }}
                                >
                                  {"PDF, DOCX, DOC or TXT <25 MB"}
                                </Typography>
                              </>
                            )}
                            <input
                              id="meetingMinute"
                              type="file"
                              hidden
                              {...register("meetingMinute", {
                                required: t("fieldRequired"),
                              })}
                              onChange={(e) => {
                                handleFileChange(e);
                              }}
                              accept=".pdf,.doc,.docx,.svg,.png,.jpg"
                              disabled
                            />
                          </Box>
                        )}
                      </Grid>
                    </Grid>
                  </Box>

                  <Box
                    sx={{
                      display: "flex",
                      justifyContent: "flex-end",
                      alignItems: "center",
                      gap: 1,
                      mt: 2,
                    }}
                  >
                    <ButtonOutline
                      onClick={() => {
                        resetForm();
                        resetMeetingDetail();
                        setLiquidationFormValue("meetingId", "");
                      }}
                    >
                      {t("semula")}
                    </ButtonOutline>

                    <ButtonPrimary type="submit" disabled={isMutating}>
                      {isMutating && <CircularProgress size={10} />}
                      {t("update")}
                    </ButtonPrimary>
                  </Box>
                </>
              ) : null}
            </Box>
          )}

          {liquidationDocumentType === 2 && (
            <Box className={classes.section}>
              <FileUploader
                title="Lampiran Dokumen Lain"
                type={DocumentUploadType.LIQUIDATION}
                uploadAfterSubmit={!isEditable}
                uploadAfterSubmitIndicator={watchLiquidationForm("id")}
                liquidationId={watchLiquidationForm("id") ?? 1}
                societyId={liquidationValue.societyId}
                societyNo={liquidationValue.societyNo}
                icNo={user?.identificationNo}
                code="SUPPORTING_DOCUMENT"
                sxContainer={{
                  border: "1px solid #ccc",
                  background: "#fff",
                  mb: 3,
                }}
                validTypes={[
                  "application/pdf",
                  "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
                  "application/msword",
                  "text/plain",
                ]}
                accept=".pdf, .doc, .docx, .text"
                onUploadComplete={() => {
                  if (isEditable) return;

                  setIsUploadingDocuments(false);
                  handleNext();
                }}
                onChange={(files) => setSupportFiles(files)}
                disabled={isViewOnly}
                required
              />
              <Box
                sx={{
                  display: "flex",
                  justifyContent: "flex-end",
                  alignItems: "center",
                  gap: 1,
                  mt: 2,
                }}
              >
                <ButtonOutline
                  onClick={() => {
                    resetForm();
                    resetMeetingDetail();
                    setLiquidationFormValue("meetingId", "");
                  }}
                >
                  {t("semula")}
                </ButtonOutline>

                <ButtonPrimary
                  type="submit"
                  disabled={
                    isMutating ||
                    (isCreate &&
                      liquidationDocumentType === 2 &&
                      !supportFiles.length)
                  }
                >
                  {isMutating && <CircularProgress size={10} />}
                  {t("update")}
                </ButtonPrimary>
              </Box>
            </Box>
          )}
        </form>
      </FormProvider>
      <MessageDialog
        open={isDialogOpen}
        onClickFunction={() => navigate(`../../mesyuarat`)}
        buttonText={t("Viewlist")}
        onClose={() => setIsDialogOpen(false)}
        message={t("createMeetingReminder")}
      />
    </>
  );
};

export default MeetingForm;
