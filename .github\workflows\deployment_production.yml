name: Deploy Frontend Eroses Prod

on:
  push:
    branches:
      - main

jobs:
  deploy_prod:
    runs-on: ubuntu-latest

    steps:
      - name: Checkout Repository
        uses: actions/checkout@v4

      - name: Set up Node.js
        uses: actions/setup-node@v4
        with:
          # TODO: update to latest LTS version of node.js if possible
          # node-version: lts/*
          node-version: 18
          cache: 'npm'

      - name: Install dependencies
        run: npm ci

      - name: Build the project for Production
        env:
          # TODO: add VITE_BACKEND_URL env and BACKEND_URL_PROD secret in github
          VITE_BACKEND_URL: ${{ secrets.BACKEND_URL_PROD }}
          VITE_APP_ENV: production
          NODE_ENV: production
          VITE_AWS_API_KEY_REGION: 'ap-southeast-5'
          VITE_AWS_API_KEY: ${{ secrets.MAP_API_KEY_PROD }}
          VITE_AWS_PLACE_INDEX: 'explore.place.Grab'
          VITE_AWS_MAP_NAME: 'explore.map.Grab'
          NODE_OPTIONS: --max-old-space-size=4096
          VITE_GA_MEASUREMENT_ID: ${{ secrets.GA_MEASUREMENT_ID }}
        run: npm run build

      - name: Configure AWS Credentials for Production
        uses: aws-actions/configure-aws-credentials@v4
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID_PROD }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY_PROD }}
          aws-region: ap-southeast-5

      - name: Deploy to S3 with Enhanced Caching Strategy
        run: |
          # Make deployment script executable
          chmod +x deploy.sh

          # Deploy with proper cache control headers
          ./deploy.sh ./dist eroses-frontend-cicd-prod ${{ secrets.CLOUDFRONT_DISTRIBUTION_ID_PROD }}
        env:
          VERIFY_DEPLOYMENT: 'true'
