/* eslint-disable react-refresh/only-export-components */
import { lazy, Suspense } from "react";
import { Navigate, Route } from "react-router-dom";

import { useSelector } from "react-redux";
import { PageLoader } from "@/components";
import { registerRoutes } from "@/helpers/routeDetector";
import { RouteGuard } from "@/components/RouteGuard";

import { PenguatkuasaanInternalMain } from "@/pages/penguatkuasaan/internal/Main";

const Pembatalan = lazy(
  () => import("@/pages/penguatkuasaan/internal/pembatalan")
);
const CiptaPembatalanIndukForm = lazy(
  () =>
    import(
      "@/pages/penguatkuasaan/internal/pembatalan/cipta-pembatalan-induk/Form"
    )
);
const CiptaPembatalanCawanganForm = lazy(
  () =>
    import(
      "@/pages/penguatkuasaan/internal/pembatalan/cipta-pembatalan-cawangan/Form"
    )
);
const SenaraiPembatalanForm = lazy(
  () =>
    import("@/pages/penguatkuasaan/internal/pembatalan/senarai-pembatalan/Form")
);

const DaftarPanduan = lazy(
  () => import("@/pages/penguatkuasaan/internal/daftar-panduan")
);
const DaftarPanduanForm = lazy(
  () => import("@/pages/penguatkuasaan/internal/daftar-panduan/Form")
);

const Notis = lazy(() => import("@/pages/penguatkuasaan/internal/notis"));
const NotisForm = lazy(
  () => import("@/pages/penguatkuasaan/internal/notis/Form")
);

const Pemeriksaan = lazy(
  () => import("@/pages/penguatkuasaan/internal/pemeriksaan")
);
const PemeriksaanForm = lazy(
  () => import("@/pages/penguatkuasaan/internal/pemeriksaan/form")
);
const PemeriksaanKeputusanForm = lazy(
  () => import("@/pages/penguatkuasaan/internal/pemeriksaan/keputusan-form")
);
const PemeriksaanKeputusanNotisForm = lazy(
  () =>
    import(
      "@/pages/penguatkuasaan/internal/pemeriksaan/keputusan-form/notis-form"
    )
);
const PemeriksaanKeputusanLaporanForm = lazy(
  () =>
    import(
      "@/pages/penguatkuasaan/internal/pemeriksaan/keputusan-form/laporan-form"
    )
);
const PemeriksaanKeputusanLaporanBorangForm = lazy(
  () =>
    import(
      "@/pages/penguatkuasaan/internal/pemeriksaan/keputusan-form/laporan-form/borang-form"
    )
);

import { NEW_PermissionNames, PORTAL_INTERNAL } from "@/helpers";
import { PenguatkuasaanInternalSekatanLiabiliti } from "@/pages/penguatkuasaan/internal/sekatan-liabiliti/Base";
import { NavbarEnforcement } from "@/components/navbar/Enforcement";
import { PenguatkuasaanInternalSekatanLiabilitiCreate } from "@/pages/penguatkuasaan/internal/sekatan-liabiliti/Create";
import { LayoutEnforcementDetails } from "@/components/layout/EnforcementDetails";
import { PenguatkuasaanInternalSekatanLiabilitiCreate002 } from "@/pages/penguatkuasaan/internal/sekatan-liabiliti/Create002";

import { getUserPortal } from "@/redux/userReducer";
import { PenguatkuasaanInternalAduan } from "@/pages/penguatkuasaan/internal/aduan/Base";
import { PenguatkuasaanInternalAduanLists } from "@/pages/penguatkuasaan/internal/aduan/Lists";
import { FeedbackInternalAduanCreate } from "@/pages/Feedback/internal/AduanCreate";
import { FeedbackInternalAduanSuccess } from "@/pages/Feedback/internal/AduanSuccess";
import { FeedbackInternalAduanView } from "@/pages/Feedback/internal/AduanView";
import { FeedbackPublicAduanViewWithAction } from "@/pages/Feedback/public/AduanViewWithAction";
import { PenguatkuasaanInternalSiasatan } from "@/pages/penguatkuasaan/internal/siasatan/Base";
import { PenguatkuasaanInternalSiasatanLists } from "@/pages/penguatkuasaan/internal/siasatan/Lists";
import { PenguatkuasaanInternalSiasatanById } from "@/pages/penguatkuasaan/internal/siasatan/ById";
import { PenguatkuasaanInternalSiasatanByIdOverall } from "@/pages/penguatkuasaan/internal/siasatan/ByIdOverall";
import { PenguatkuasaanInternalSiasatanUnderInvestigation } from "@/pages/penguatkuasaan/internal/siasatan/UnderInvestigation";
import { PenguatkuasaanInternalSiasatanDiary } from "@/pages/penguatkuasaan/internal/siasatan/Diary";
import { PenguatkuasaanInternalSiasatanPapers } from "@/pages/penguatkuasaan/internal/siasatan/Papers";
import { FormEnforcementInvestigationInternalConversationRecording } from "@/components/form/enforcement/investigation/InternalConversationRecording";
import { SenaraiNamaLarangan } from "@/pages/penguatkuasaan/internal/daftar-nama-larangan/senaraiNamaLarangan";
import { SenaraiLogoLarangan } from "@/pages/penguatkuasaan/internal/daftar-nama-larangan/senaraiLogoLarangan";
import { TambahRekodLarangan } from "@/pages/penguatkuasaan/internal/daftar-nama-larangan/tambahRekodLarangan";
import { LaranganProvider } from "@/contexts/laranganProvider";
import { LaranganTab } from "@/pages/penguatkuasaan/internal/daftar-nama-larangan/laranganTab";
import AuthHelper from "@/helpers/authHelper";

// Layout component to wrap all penguatkuasaan routes with protection
const PenguatkuasaanGuardedLayout = () => {
  if (!AuthHelper.hasAuthority([NEW_PermissionNames.PENGUATKUASAAN.label])) {
    return <Navigate to="/forbidden" replace />;
  }
  return (
    <RouteGuard
      autoUpdatePortal={true}
      showDebugInfo={process.env.NODE_ENV === "development"}
    >
      <Suspense fallback={<PageLoader />}>
        <PenguatkuasaanInternalMain />
      </Suspense>
    </RouteGuard>
  );
};

// Register routes with their portal types
// TODO: Team should register specific paths here
registerRoutes({
  // Example route registrations:
  // '/penguatkuasaan': 'internal',                                    // Main enforcement page
  // '/penguatkuasaan/aduan': 'internal',                              // Complaints
  // '/penguatkuasaan/aduan/create': 'internal',                       // Create complaint
  // '/penguatkuasaan/aduan/view/:id': 'internal',                     // View complaint
  // '/penguatkuasaan/aduan/update/:id': 'internal',                   // Update complaint
  // '/penguatkuasaan/siasatan': 'internal',                           // Investigation
  // '/penguatkuasaan/pembatalan': 'internal',                         // Cancellation
  // '/penguatkuasaan/pembatalan/cipta-pembatalan-induk/:id': 'internal', // Create parent cancellation
  // '/penguatkuasaan/pembatalan/cipta-pembatalan-cawangan/:id': 'internal', // Create branch cancellation
  // '/penguatkuasaan/pembatalan/senarai-pembatalan/:id': 'internal',  // Cancellation list
  // '/penguatkuasaan/sekatan_liabiliti': 'internal',                  // Liability restrictions
  // '/penguatkuasaan/sekatan_liabiliti/create': 'internal',           // Create liability restriction
  // '/penguatkuasaan/pengurusan_fee': 'internal',                     // Fee management
  // '/penguatkuasaan/daftar-panduan': 'internal',                     // Guidelines registry
  // '/penguatkuasaan/daftar-panduan/create': 'internal',              // Create guideline
  // '/penguatkuasaan/pengurusan_notis': 'internal',                   // Notice management
  // '/penguatkuasaan/red_flag': 'internal',                           // Red flag
  // '/penguatkuasaan/daftar_nama_larangan': 'internal',               // Prohibition name registry
  // '/penguatkuasaan/pemeriksaan': 'internal',                        // Inspection
  // '/penguatkuasaan/pendakwaan': 'internal',                         // Prosecution
  // Add your route registrations here
  "/penguatkuasaan/pembatalan": "internal",
  "/penguatkuasaan/pembatalan/cipta-pembatalan-induk/:id": "internal",
  "/penguatkuasaan/pembatalan/cipta-pembatalan-cawangan/:id": "internal",
  "/penguatkuasaan/pembatalan/senarai-pembatalan/:id": "internal",
  "/penguatkuasaan/daftar-panduan": "internal",
  "/penguatkuasaan/daftar-panduan/create": "internal",
  "/penguatkuasaan/daftar-panduan/:id": "internal",
  "/penguatkuasaan/pengurusan-notis": "internal",
  "/penguatkuasaan/pengurusan-notis/create": "internal",
  "/penguatkuasaan/pemeriksaan": "internal",
  "/penguatkuasaan/pemeriksaan/create": "internal",
  "/penguatkuasaan/pemeriksaan/keputusan/:id": "internal",
  "/penguatkuasaan/pemeriksaan/keputusan/:id/notis": "internal",
  "/penguatkuasaan/pemeriksaan/keputusan/:id/laporan": "internal",
  "/penguatkuasaan/pemeriksaan/keputusan/:id/laporan/borang": "internal",
});

const routeComponents = (
  <Route path="penguatkuasaan" element={<PenguatkuasaanGuardedLayout />}>
    <Route element={<NavbarEnforcement />}>
      <Route index element={<Navigate to="aduan" />} />
      <Route path="aduan" element={<PenguatkuasaanInternalAduan />}>
        <Route index element={<PenguatkuasaanInternalAduanLists />} />
        <Route
          path="aduan-saya"
          element={<PenguatkuasaanInternalAduanLists />}
        />
      </Route>
      <Route path="siasatan" element={<PenguatkuasaanInternalSiasatan />}>
        <Route index element={<PenguatkuasaanInternalSiasatanLists />} />
        <Route
          path="dalam-siasatan"
          element={<PenguatkuasaanInternalSiasatanUnderInvestigation />}
        />
      </Route>
      <Route path="pembatalan">
        <Route index element={<Pembatalan />} />
        <Route
          path="cipta-pembatalan-induk/:id"
          element={<CiptaPembatalanIndukForm />}
        />
        <Route
          path="cipta-pembatalan-cawangan/:id"
          element={<CiptaPembatalanCawanganForm />}
        />
        <Route
          path="senarai-pembatalan/:id"
          element={<SenaraiPembatalanForm />}
        />
      </Route>
      <Route
        path="sekatan_liabiliti"
        element={<PenguatkuasaanInternalSekatanLiabiliti />}
      />
      <Route path="pengurusan_fee" element={<h2>Pengurusan Fee</h2>} />
      <Route path="daftar-panduan" element={<DaftarPanduan />} />
      <Route path="daftar-panduan/create" element={<DaftarPanduanForm />} />
      <Route path="daftar-panduan/:id" element={<DaftarPanduanForm />} />
      <Route path="pengurusan-notis" element={<Notis />} />
      <Route path="pengurusan-notis/create" element={<NotisForm />} />
      <Route path="red_flag" element={<h2>Red Flag</h2>} />
      <Route
        path="daftar_nama_larangan"
        element={
          <LaranganProvider>
            <LaranganTab />
          </LaranganProvider>
        }
      >
        {/* <Route
          index
          element={<Navigate to="senarai_nama_larangan" replace />}
        /> */}
        <Route path="senarai_nama_larangan" element={<SenaraiNamaLarangan />} />
        <Route path="senarai_logo_larangan" element={<SenaraiLogoLarangan />} />
        <Route path="tambah_rekod" element={<TambahRekodLarangan />} />
      </Route>

      <Route path="pemeriksaan" element={<Pemeriksaan />} />
      <Route path="pemeriksaan/create" element={<PemeriksaanForm />} />
      <Route
        path="pemeriksaan/keputusan/:id"
        element={<PemeriksaanKeputusanForm />}
      />
      <Route
        path="pemeriksaan/keputusan/:id/notis"
        element={<PemeriksaanKeputusanNotisForm />}
      />
      <Route
        path="pemeriksaan/keputusan/:id/laporan"
        element={<PemeriksaanKeputusanLaporanForm />}
      />
      <Route
        path="pemeriksaan/keputusan/:id/laporan/borang"
        element={<PemeriksaanKeputusanLaporanBorangForm />}
      />

      <Route path="pendakwaan" element={<h2>Pendakwaan</h2>} />
    </Route>
    <Route element={<LayoutEnforcementDetails />}>
      <Route path="aduan/create" element={<FeedbackInternalAduanCreate />} />
      <Route path="aduan/success" element={<FeedbackInternalAduanSuccess />} />
      <Route path="aduan/view/:id" element={<FeedbackInternalAduanView />} />
      <Route
        path="aduan/update/:id"
        element={<FeedbackInternalAduanCreate />}
      />
      <Route path="aduan/:id" element={<FeedbackPublicAduanViewWithAction />} />

      <Route
        path="siasatan/:id"
        element={<PenguatkuasaanInternalSiasatanById />}
      >
        <Route index element={<PenguatkuasaanInternalSiasatanByIdOverall />} />
        <Route path="aduan" element={<h1>Aduan</h1>} />
        <Route path="diari" element={<PenguatkuasaanInternalSiasatanDiary />} />
        <Route
          path="papers"
          element={<PenguatkuasaanInternalSiasatanPapers />}
        />
        <Route
          path="rakaman-percakapan"
          element={
            <FormEnforcementInvestigationInternalConversationRecording />
          }
        />
      </Route>

      <Route
        path="siasatan/:id"
        element={<PenguatkuasaanInternalSiasatanById />}
      >
        <Route index element={<PenguatkuasaanInternalSiasatanByIdOverall />} />
        <Route path="aduan" element={<h1>Aduan</h1>} />
        <Route path="diari" element={<PenguatkuasaanInternalSiasatanDiary />} />
        <Route
          path="papers"
          element={<PenguatkuasaanInternalSiasatanPapers />}
        />
        <Route
          path="rakaman-percakapan"
          element={
            <FormEnforcementInvestigationInternalConversationRecording />
          }
        />
      </Route>

      <Route
        path="sekatan_liabiliti/create"
        element={<PenguatkuasaanInternalSekatanLiabilitiCreate />}
      />
      <Route
        path="sekatan_liabiliti/create/:id"
        element={<PenguatkuasaanInternalSekatanLiabilitiCreate002 />}
      />
      <Route
        path="sekatan_liabiliti/:id"
        element={
          <PenguatkuasaanInternalSekatanLiabilitiCreate002 mode="VIEW" />
        }
      />
      <Route
        path="sekatan_liabiliti/update/:id"
        element={
          <PenguatkuasaanInternalSekatanLiabilitiCreate002 mode="UPDATE" />
        }
      />
    </Route>
  </Route>
);

/**
 * @deprecated please use {@link usePenguatKuasaanRoutes} instead.
 */
export const penguatKuasaan = {
  routes: () =>
    localStorage.getItem("portal") === PORTAL_INTERNAL && routeComponents,
};

export const usePenguatKuasaanRoutes = () => {
  const userPortal = useSelector(getUserPortal);

  const routes = userPortal === parseInt(PORTAL_INTERNAL) && routeComponents;

  return {
    routes,
  };
};
