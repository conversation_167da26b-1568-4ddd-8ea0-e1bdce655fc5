import {TrainingFormProps} from "@/pages/internal-training/createStepOne";
import React, {useEffect, useState} from "react";
import {Box, Grid, IconButton, Typography} from "@mui/material";
import {useTranslation} from "react-i18next";
import ChapterFragment from "@/pages/internal-training/chapterFragment";
import {ButtonOutline, ButtonPrimary, DialogConfirmation} from "@/components";
import {DeleteIcon} from "@/components/icons/delete";
import {TrainingAddIcon} from "@/components/icons/trainingAdd";
import {useCustom, useCustomMutation, useNotification} from "@refinedev/core";
import {API_URL} from "@/api";
import {DocumentUploadType, useUploadPresignedUrl} from "@/helpers";


export interface TrainingChapter {
  trainingCourseId: string,
  id: string,
  title: string,
  description: string,
  duration: number,
  youtubeLink: string,
  media: string,
  materialType: string,
  isUpdated: boolean,
  error: boolean,
  isMediaUpdated: boolean,
  sequenceOrder: number,
  privateInternalDocument: {
    id?: string,
    type: number,
    trainingMaterialId:string,
    doc?: string,
    binary?: string,
    fileType?: string,
    name: string,
    url: string,
    presignedUrl: string,
  },
}


const CreateStepTwo: React.FC<TrainingFormProps> = ({
  headerStyle,
  labelStyle,
  borderStyle,
  handleNext,
  courseId,
  isUpdate,
                                                      allowedToDelete,
}) => {

  const { t, i18n } = useTranslation();


  const [openModal, setOpenModal] = useState(false);
  const [openModalDelete, setOpenModalDelete] = useState(false);
  const [tempId, setTempId] = useState({
    id:"0",
    index:0
  });
  const [noOfUploads, setNoOfUploads] = useState(0);
  const { open: openNotification } = useNotification();
  const [totalChapters, setTotalChapters] = useState<TrainingChapter[]>([{
    trainingCourseId: courseId,
    id: "0",
    title: "",
    description: "",
    duration: 0,
    youtubeLink: "",
    media: "",
    materialType: "",
    isUpdated: false,
    error: false,
    isMediaUpdated: false,
    sequenceOrder: 0,
    privateInternalDocument: {
      type: DocumentUploadType.TRAINING_MATERIAL,
      trainingMaterialId: "0",
      name: "",
      url:"",
      presignedUrl: ""
    }
  }])

  const [canChange, setCanChange] = useState(true);
  const [readyForFileUpload, setReadyForFileUpload] = useState<number>(0);

  const handleSaveDraft = (e: React.MouseEvent<HTMLButtonElement>) => {
    /*totalChapters.map((e, i) => {
      if (e.id === "0") Create(i);
      else if (e.id!="0" && e.isUpdated) Edit(i);
      else if (e.id!="0" && !e.isUpdated && e.isMediaUpdated) {
        uploadFile({
          params: {
            type: DocumentUploadType.TRAINING_MATERIAL,
            trainingMaterialId: e.id
          },
          file: totalChapters[i].media,
        });
      }
      else {
        if (i === totalChapters.length - 1) {
          openNotification?.({
            type: "success",
            message: t("noChanges"),
            //description: "Failed to fetch feedback answers",
          });
        }
      }
    });*/
    UpdateBatch(true);
  }

  const handleSave = () => {
    totalChapters.map((e, i) => {
      if (e.error) return;
      /*if (e.id === "0") Create(i);
      else if (e.id!="0" && e.isUpdated) Edit(i);
      else if (e.id!="0" && !e.isUpdated && e.isMediaUpdated) {
        uploadFile({
          params: {
            type: DocumentUploadType.TRAINING_MATERIAL,
            trainingMaterialId: e.id
          },
          file: totalChapters[i].media,
        });
      }
      if (i === totalChapters.length - 1 && courseId!="0") {
        handleNext(courseId, "quiz");
      }*/
    });
    UpdateBatch(false);
  }

  //console.log("courseId", courseId);
  const handleAddChapter = (e: React.MouseEvent<HTMLButtonElement>) => {
    if(!allowedToDelete){
      openNotification?.({
        type: "error",
        message: t("TRAINING_ENROLLED_CANT_ADD"),
        //description: "Failed to fetch feedback answers",
      });
      return
    }
    const temp: TrainingChapter = {
      trainingCourseId: courseId,
      id: "0",
      title: "",
      description: "",
      duration: 0,
      youtubeLink: "",
      media: "",
      materialType: "",
      isUpdated: false,
      error: false,
      isMediaUpdated: false,
      sequenceOrder: totalChapters.length,
      privateInternalDocument: {
        type: DocumentUploadType.TRAINING_MATERIAL,
        trainingMaterialId: "0",
        name : "",
        url:"",
        presignedUrl: "",
      }
    }
    setTotalChapters(prevChapter => [...prevChapter, temp]);
  }

  const handleDeleteChapter = () => {
    setTotalChapters(prevChapter => prevChapter.filter((c) => c.id != tempId.id))
    if(tempId.id != "0") Delete(tempId.id);
  }

  const { mutate: deleteMaterial, isLoading: isLoadingDelete } = useCustomMutation();
  const Delete = (id: string): void => {
    deleteMaterial(
      {
        url: `${API_URL}/society/admin/training/materials/${id}`,
        method: "delete",
        values: {
        },
        config: {
          headers: {
            "Content-Type": "application/json",
            portal: localStorage.getItem("portal") || "",
            authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
          },
        },
        successNotification: (data) => {
          if (data?.data?.data) {
            return {
              message: t("TRAINING_MATERIAL_DELETED"),
              type: "success",
            };
          } else {
            return {
              message: t("error") + data?.data?.msg,
              type: "error",
            };
          }
        },
        errorNotification: (data) => {
          return {
            message: data?.response?.data?.msg,
            type: "error",
          };
        },
      },
      {
        onError(error, variables, context) {
          console.log(error);
        },
      }
    );
  };

  /*const { mutate: create, isLoading: isLoadingCreate } = useCustomMutation();
  const Create = (i: number): void => {
    const currentDate = new Date();
    const formattedDate = currentDate.toISOString().split("T")[0];
    create(
      {
        url: `${API_URL}/society/admin/training/materials`,
        method: "post",
        values: {
          trainingCourseId: courseId,
          title: totalChapters[i].title ?? "",
          contentText: totalChapters[i].description ?? "",
          materialType: totalChapters[i].mediaType,
          youtubeLink: totalChapters[i].youtubeLink ?? "",
          description: totalChapters[i].description ?? "",
          duration: totalChapters[i].duration ?? 60,
          sequenceOrder: i + 1
        },
        config: {
          headers: {
            "Content-Type": "application/json",
            portal: localStorage.getItem("portal") || "",
            authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
          },
        },
        successNotification: (data) => {
          if (data?.data?.data) {
            setOpenModal(false);
            totalChapters[i].id = data?.data?.data;
            if (totalChapters[i].media && totalChapters[i].isMediaUpdated) {
              uploadFile({
                params: {
                  type: DocumentUploadType.TRAINING_MATERIAL,
                  trainingMaterialId: data?.data?.data
                },
                file: totalChapters[i].media,
              });
            }
            else {
              handleNext(courseId, "quiz");
            }
            return {
              message: t("TRAINING_MATERIAL_CREATED"),
              type: "success",
            };
          } else {
            return {
              message: t("error") + data?.data?.msg,
              type: "error",
            };
          }
        },
        errorNotification: (data) => {
          return {
            message: data?.response?.data?.msg,
            type: "error",
          };
        },
      },
      {
        onError(error, variables, context) {
          console.log(error);
        },
      }
    );
  };*/



  const { mutate: updateBatch, isLoading: isLoadingUpdateBatch } = useCustomMutation();
  const UpdateBatch = (draft:boolean): void => {
    let i = 0;
    const formData = new FormData();
    updateBatch(
      {
        url: `${API_URL}/society/admin/training/materials-batch`,
        method: "post",
        values: totalChapters.map((tc) => {
          return {
            id: tc.id,
            trainingCourseId: courseId,
            title: tc.title ?? "",
            contentText: tc.description ?? "",
            materialType: tc.materialType ?? "IMAGE",
            youtubeLink: tc.youtubeLink ?? "",
            description: tc.description ?? "",
            duration: tc.duration ?? 60,
            sequenceOrder: i++,
            privateInternalDocument: {
              type: DocumentUploadType.TRAINING_MATERIAL,
              trainingMaterialId: tc.id,
              name: tc.privateInternalDocument.name,
            }
          }
        }),
        config: {
          headers: {
            "Content-Type": "application/json",
            portal: localStorage.getItem("portal") || "",
            authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
          },
        },
        successNotification: (data) => {
          if (data?.data?.data) {
            setOpenModal(false);
            const newData = data?.data?.data;
            console.log("newData",newData,totalChapters);
            setTotalChapters(prevChapters => prevChapters.map((c,index) => {
              const temp = newData.filter((n:any) => n.sequenceOrder === c.sequenceOrder)[0];
              console.log("temp",temp);
              return {
                ...c,
                privateInternalDocument: {
                  id: temp.documentId,
                  type: DocumentUploadType.TRAINING_MATERIAL,
                  trainingMaterialId: c.id,
                  binary : c.privateInternalDocument.binary,
                  fileType: c.privateInternalDocument.fileType,
                  name: c.privateInternalDocument.name,
                  url: temp.url,
                  presignedUrl: temp.presignedUrl,
                }
              }
            }));
            return {
              message: t("TRAINING_MATERIAL_CREATED"),
              type: "success",
            };
          } else {
            return {
              message: t("error") + data?.data?.msg,
              type: "error",
            };
          }
        },
        errorNotification: (data) => {
          return {
            message: data?.response?.data?.msg,
            type: "error",
          };
        },
      },
      {
        onError(error, variables, context) {
          console.log(error);
        },
      }
    );
  };

  /*const { mutate: edit, isLoading: isLoadingEdit } = useCustomMutation();
  const Edit = (i: number): void => {
    const currentDate = new Date();
    const formattedDate = currentDate.toISOString().split("T")[0];
    edit(
      {
        url: `${API_URL}/society/admin/training/materials`,
        method: "put",
        values: {
          id: totalChapters[i].id,
          trainingCourseId: courseId,
          title: totalChapters[i].title ?? "",
          contentText: totalChapters[i].description ?? "",
          materialType: totalChapters[i].materialType,
          youtubeLink: totalChapters[i].youtubeLink ?? "",
          description: totalChapters[i].description ?? "",
          duration: totalChapters[i].duration ?? 60,
          sequenceOrder: i + 1
        },
        config: {
          headers: {
            "Content-Type": "application/json",
            portal: localStorage.getItem("portal") || "",
            authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
          },
        },
        successNotification: (data) => {
          if (data?.data?.data) {
            setOpenModal(false);
            // Handle file upload
            if (totalChapters[i].media && totalChapters[i].isMediaUpdated) {
              uploadFile({
                params: {
                  type: DocumentUploadType.TRAINING_MATERIAL,
                  trainingMaterialId: totalChapters[i].id
                },
                file: totalChapters[i].privateInternalDocument.doc,
              });
            }
            else {
              handleNext(courseId, "quiz");
            }
            return {
              message: t("TRAINING_MATERIAL_UPDATED"),
              type: "success",
            };
          } else {
            return {
              message: t("error") + data?.data?.msg,
              type: "error",
            };
          }
        },
        errorNotification: (data) => {
          return {
            message: data?.response?.data?.msg,
            type: "error",
          };
        },
      },
      {
        onError(error, variables, context) {
          console.log(error);
        },
      }
    );
  };*/

  const { data: trainingData, isLoading: isTrainingLoading } = useCustom({
    url: `${API_URL}/society/admin/training/courses/${courseId}/materials`,
    method: "get",
    config: {
      headers: {
        portal: localStorage.getItem("portal"),
        authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
      },
    },
    queryOptions: {
      enabled: courseId != "0",
      retry: false,
      cacheTime: 0,
    },
  });

  const trainingMaterials = trainingData?.data?.data || [];
  //console.log("trainingMaterial", trainingMaterials)

  useEffect(() => {
    if ((isUpdate || courseId) && (trainingMaterials).length > 0) {
      setCanChange(false);
      const tempArr: TrainingChapter[] = trainingMaterials.map((e: any) => {
        return {
          trainingCourseId: e.trainingCourseId,
          id: e.id,
          title: e.title || "",
          description: e.description || "",
          duration: e.duration || 60,
          youtubeLink: e.youtubeLink || "",
          media: e.media,
          isUpdated: false,
          isError: false,
          isMediaUpdated: false,
          sequenceOrder: e.sequenceOrder,
          privateInternalDocument: {
            id: e.documentId,
            type: DocumentUploadType.TRAINING_MATERIAL,
            trainingMaterialId: e.id,
            name: e.name
          }
      }})
      setTotalChapters(tempArr);
      setCanChange(true);
    }
  }, [trainingData]);

  const handleDataChange = (i: number, data: TrainingChapter) => {
    console.log("handleDataChange",canChange,data);
    if(canChange){
      setTotalChapters(prevChapters => prevChapters.map((c,index) => {
        if(i === index){
          return {
            ...c,
            id: data.id,
            title: data.title,
            description: data.description,
            youtubeLink: data.youtubeLink,
            duration: data.duration,
            media: data.media,
            isUpdated: data.isUpdated,
            isMediaUpdated: data.isMediaUpdated,
            materialType: data.materialType,
            sequenceOrder: data.sequenceOrder,
            error: data.error,
            privateInternalDocument: {
              type: DocumentUploadType.TRAINING_MATERIAL,
              trainingMaterialId: data.id,
              binary : data.privateInternalDocument.binary,
              fileType: data.privateInternalDocument.fileType,
              name: data.privateInternalDocument.name,
              url: data.privateInternalDocument.url,
              presignedUrl: data.privateInternalDocument.presignedUrl,
            }
          }
        }
        return c;
      }));
    }
  }

  return (<>
    <Box sx={{ display: "flex" }}>
      <Box sx={{ width: "85%", }}>
        {totalChapters.map((e: TrainingChapter, index) => {
          return (<Box
            key={index}
            sx={{
              borderRadius: 2.5,
              backgroundColor: "#fff",
              //display: "inline",
              px: 2,
              py: 2,
              mb: 1,
            }}
          >
            <ChapterFragment no={index + 1} headerStyle={headerStyle} borderStyle={borderStyle}
              labelStyle={labelStyle} handleDataChange={handleDataChange} data={e} readyForFileUpload={readyForFileUpload} setReadyForFileUpload={setReadyForFileUpload} />
            {index === totalChapters.length - 1 ?
              <Grid
                item
                xs={12}
                sx={{
                  mt: 2,
                  display: "flex",
                  flexDirection: "row",
                  justifyContent: "flex-end",
                  gap: 1,
                }}
              >
                <ButtonOutline
                  sx={{
                    bgcolor: "white",
                    "&:hover": { bgcolor: "white" },
                    width: "auto",
                  }}
                  disabled={isLoadingUpdateBatch}
                  onClick={() => setOpenModal(true)}
                >
                  {t("save")}
                </ButtonOutline>
                <ButtonPrimary
                  variant="contained"
                  sx={{
                    width: "auto",
                  }}
                  disabled={isLoadingUpdateBatch}
                  onClick={() => handleNext(courseId, "quiz")}
                //disabled={true}
                >
                  {t("next")}
                </ButtonPrimary>
              </Grid> : <></>} </Box>
          )
        }
        )}
      </Box>
      <Box sx={{ width: "15%" }}>
        <Box
          sx={{
            borderRadius: 2.5,
            backgroundColor: "#fff",
            //display: "inline",
            px: 2,
            py: 2,
            mb: 1,
            ml: 2,
          }}
        >
          {totalChapters.map((e, i) => {
            return <Grid container spacing={1} key={i}>
              <Grid item xs={10}>
                <Box
                  sx={borderStyle}
                >
                  <Typography key={i} sx={{
                    fontSize: 14,
                    color: "#666666",
                    fontWeight: "400 !important",
                  }}>
                    {`${t("chapter")} ${i + 1}`}
                  </Typography>
                </Box></Grid>
              {i > 0 ?
                <Grid item xs={2}>
                  <IconButton
                    sx={{
                      display: "flex",
                      justifyContent: "center",
                      alignContent: "center",
                      mt: 1
                    }}
                    onClick={() => {
                      setTempId({
                        id:e.id,
                        index: i
                      });
                      setOpenModalDelete(true);
                    }}
                  >
                    <DeleteIcon
                      sx={{
                        color: "#FF0000",
                      }}
                    />
                  </IconButton></Grid> : <Grid item xs={2}></Grid>}
            </Grid>
          })}
          <Box>
            <Grid container sx={{ mt: 1 }}>
              <Grid item xs={3}>
                <Box
                  sx={{
                    display: "flex",
                    justifyContent: "center",
                    alignItems: "center",
                    borderRadius: 2.5,
                    backgroundColor: "#0CA6A6",
                    width: 36,
                    height: 36,
                  }}
                >
                  <TrainingAddIcon sx={{ color: "#FFF", }} />
                </Box>
              </Grid>
              <Grid item xs={9}>
                <ButtonOutline
                  sx={{
                    bgcolor: "white",
                    "&:hover": { bgcolor: "white" },
                    width: "75%",
                    minWidth: "75%"
                  }}
                  onClick={handleAddChapter}
                >
                  {t("addChapter")}
                </ButtonOutline>
              </Grid>
            </Grid>
          </Box>
        </Box>
      </Box>
    </Box>
      <DialogConfirmation
        open={openModalDelete}
        onClose={() => {
          setOpenModalDelete(false);
        }}
        onAction={() => {
          if(!allowedToDelete){
            openNotification?.({
              type: "error",
              message: t("TRAINING_ENROLLED_CANT_DELETE"),
              //description: "Failed to fetch feedback answers",
            });
            return
          }
          handleDeleteChapter();
        }}
        isMutating={false}
        onConfirmationText={t("CONFIRM_DELETE_TRAINING")}
      />
    <DialogConfirmation
      open={openModal}
      onClose={() => {
        setOpenModal(false);
      }}
      onAction={handleSave}
      isMutating={false}
      onConfirmationText={t("CONFIRM_CREATE_TRAINING")}
    />
  </>
  );
}

export default CreateStepTwo;
