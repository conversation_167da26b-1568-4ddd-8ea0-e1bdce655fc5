import React, {useEffect, useRef, useState} from "react";
import {Box, FormHelperText, Grid, SxProps, TextField, Theme, Typography} from "@mui/material";
import DurationComponent from "@/pages/internal-training/durationComponent";
import {useTranslation} from "react-i18next";
import {TrainingChapter} from "@/pages/internal-training/createStepTwo";
import {useCustom} from "@refinedev/core";
import {API_URL} from "@/api";
import {convertFileToBinary, convertFileToBase64, DocumentUploadType, isValidUrl} from "@/helpers";
import FileInput from "@/components/input/FileInput";
import axios from "axios";
import useMutation from "../../helpers/hooks/useMutation";

interface ChapterFragmentProps {
  no: number,
  headerStyle: SxProps<Theme>,
  labelStyle: SxProps<Theme>,
  borderStyle: SxProps<Theme>,
  data: TrainingChapter,
  handleDataChange: (i: number, data: TrainingChapter) => void,
  readyForFileUpload: number,
  setReadyForFileUpload: React.Dispatch<React.SetStateAction<number>>
}


const ChapterFragment: React.FC<ChapterFragmentProps> = ({
  no,
  headerStyle,
  labelStyle,
  borderStyle,
  data,
  handleDataChange,
}) => {

  const { t, i18n } = useTranslation();

  const [hour, setHour] = useState<number>(1)
  const [minute, setMinute] = useState<number>(0)

  const [formErrors, setFormErrors] = useState<{ [key: string]: string }>({});
  const [formData, setFormData] = useState<TrainingChapter>(data);

  //const [selectedFile, setSelectedFile] = useState<File | null>(null);
  //const [uploadedFiles, setUploadedFiles] = useState<any[]>([]);

  const uploadedFilesRef = useRef<(File | null)[]>([null, null, null]);
  //const [isDataUpdated, setIsDataUpdated] = useState(1);

  useEffect(() => {
    setFormData((prevState) => ({
      ...prevState,
      duration: hour * 60 + minute,
      isUpdated: true,
    }));
  }, [hour, minute]);

  useEffect(() => {
    if (Object.keys(data).length > 0) {
      if (data.duration) {
        setHour(Math.floor(data.duration / 60))
        setMinute(data.duration % 60)
      }
      const temp: TrainingChapter = {
        trainingCourseId: data.trainingCourseId,
        id: data.id,
        title: data.title || "",
        description: data.description || "",
        duration: data.duration || 60,
        youtubeLink: data.youtubeLink || "",
        media: data.media,
        materialType: data.materialType || "IMAGE",
        isUpdated: data.isUpdated || false,
        error: data.error || false,
        isMediaUpdated: data.isMediaUpdated || false,
        sequenceOrder: data.sequenceOrder,
        privateInternalDocument: {
          type: DocumentUploadType.TRAINING_MATERIAL,
          trainingMaterialId: data.id != "0" ? data.id : ("0"),
          name: data.privateInternalDocument.name,
          url: data.privateInternalDocument.url,
          presignedUrl: data.privateInternalDocument.presignedUrl,
        }
      }
      //console.log("data",temp)
      //setSelectedFile(data.privateInternalDocument.doc);
      setFormData(temp);
      //validation(false);
    }
  }, [data.id]);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData((prevState) => ({
      ...prevState,
      [name]: value,
      isUpdated: true,
    }));
    setFormErrors((prev) => ({ ...prev, [name]: "" }));
    //validation(true);
  };

  const handleFileChange = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;
    const mimeType = file.type;
    let isVideo = false;
    if(mimeType == "video/mp4" || mimeType == "video/mp3" || mimeType == "video/x-ms-video"){
      isVideo = true;
    }
    const binary = await convertFileToBinary(file);
    //console.log("fileRes",fileRes);
    //let base64: string = await convertFileToBase64(file)
    //base64 = base64.replace('', '').replace(/^.+,/, '');
    setFormData((prevState) => ({
      ...prevState,
      media: "",
      isMediaUpdated: true,
      materialType: isVideo ? "VIDEO" : "IMAGE",
      privateInternalDocument: {
        type: DocumentUploadType.TRAINING_MATERIAL,
        trainingMaterialId: prevState.id != "0" ? prevState.id : ("0"),
        binary : binary,
        fileType: file.type,
        name: file.name,
        url:"",
        presignedUrl: "",
      }
    }));
    //console.log("fileName",file.name);
    //validation(true);
  };

  useEffect(() => {
    validation(true)
  },[formData.privateInternalDocument,formData.youtubeLink,formData.title,formData.description,formData.duration])

  const { fetch: registerFileInDatabase } = useMutation({
    url: "document/registerFileInDb",
    method: "put",
    onErrorNotification: (data) => {
      console.log("registerFileInDbErr", data);
    },
    onSuccess: (data) => {
      console.log("registerFileInDb", data);
      //onSuccessUpload?.(data);
      //setReturnURL(data?.data?.data?.url);
    },
  });

  const uploadFile = async (presignedUrl: string, binary: string, fileType: string) => {
    const uploadResponse = await axios.put(presignedUrl, binary, {
      headers: {
        "Content-Type": fileType,
      },
    });
    if (uploadResponse.status === 200) {
      if(data.privateInternalDocument.id){
        registerFileInDatabase({
          id: data.privateInternalDocument.id,
          type: DocumentUploadType.TRAINING_MATERIAL,
          trainingMaterialId: data.privateInternalDocument.trainingMaterialId,
          name: data.privateInternalDocument.name,
          status:1,
          url: data.privateInternalDocument.url
        })
      }else{
        registerFileInDatabase({
          type: DocumentUploadType.TRAINING_MATERIAL,
          trainingMaterialId: data.privateInternalDocument.trainingMaterialId,
          name: data.privateInternalDocument.name,
          status:1,
          url: data.privateInternalDocument.url
        })
      }

    }
  }



  useEffect(() => {
    if(isValidUrl(data.privateInternalDocument.presignedUrl)
    && data.privateInternalDocument.binary && data.privateInternalDocument.fileType){
      uploadFile(data.privateInternalDocument.presignedUrl,data.privateInternalDocument.binary,data.privateInternalDocument.fileType)

    }
  },[data.privateInternalDocument.presignedUrl])


  /*const { data: trainingDocData, isLoading: isTrainingDocLoading } = useCustom({
    url: `${API_URL}/document/documentByParam`,
    method: "get",
    config: {
      headers: {
        portal: localStorage.getItem("portal"),
        authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
      },
      query: {
        //trainingId: data.trainingCourseId,
        trainingMaterialId: data.id,
        type: DocumentUploadType.TRAINING_MATERIAL
      },
    },
    queryOptions: {
      enabled: data.id != "0",
      retry: false,
      cacheTime: 0,
    },
  });

  const trainingDoc = trainingDocData?.data?.data || [];
  //console.log("trainingDoc", trainingDocData)

  useEffect(() => {
    if(trainingDoc.length > 0) setSelectedFile(trainingDoc[0])
  },[trainingDoc])*/

  const validateForm = () => {
    const errors: { [key: string]: string } = {};
    if (!formData.title) errors.title = t("fieldRequired");
    if (!formData.description) errors.description = t("fieldRequired");
    if (!formData.youtubeLink) errors.youtubeLink = t("fieldRequired");
    if (!formData.duration) errors.duration = t("fieldRequired");
    return errors;
  };

  const validation = (updateMain: boolean) => {
    const errors = validateForm();

    if (Object.keys(errors).length > 0) {
      setFormErrors(errors);
      setFormData((prevState) => ({
        ...prevState,
        error: true,
      }));
    }else{
      setFormErrors({
        title:"",
        description:"",
        youtubeLink:"",
        duration:"",
      });
      setFormData((prevState) => ({
        ...prevState,
        error: false,
      }));
    }
    console.log("validation",formData);
    if(updateMain) handleDataChange(no - 1, formData);
  }

  return (<>
    <Box
      sx={borderStyle}
    >
      <Typography
        sx={headerStyle}
      >
        {t("chapterTitle")}
      </Typography>
      <Grid container spacing={2} sx={{ mt: 1 }}>
        <Grid item xs={12} sm={4}>
          <Typography sx={labelStyle}>
            {`${t("chapterTitle")} ${no}`} <span style={{ color: "red" }}>*</span>
          </Typography>
        </Grid>
        <Grid item xs={12} sm={8}>
          <TextField
            size={"small"}
            fullWidth
            required
            name="title"
            value={formData.title}
            error={!!formErrors.title}
            helperText={formErrors.title}
            onChange={handleInputChange}
          />
        </Grid>
        <DurationComponent setHour={setHour} setMinute={setMinute}
          labelStyle={labelStyle} hour={hour} minute={minute} />
      </Grid>
    </Box>
    <Box
      sx={borderStyle}
    >
      <Typography
        sx={headerStyle}
      >
        {t("trainingMedium")}
      </Typography>
      <Grid container spacing={2} sx={{ mt: 1 }}>
        <Grid item xs={12} sm={4}>
          <Typography sx={labelStyle}>
            {t("chapterMedia")} <span style={{ color: "red" }}>*</span>
          </Typography>
        </Grid>
        <Grid item xs={12} sm={8}>
          {/*<Box
            sx={{
              border: "2px solid #DADADA",
              borderRadius: "8px",
              p: 2,
              gap: 2,
              textAlign: "center",
              cursor: "pointer",
              height: "200px",
              display: "flex",
              flexDirection: "column",
              justifyContent: "center",
              alignItems: "center",
            }}
            onClick={() => {
              const element = document.getElementById(`media${no}`);
              if (element) {
                element.click();
              }
            }}
          >
            {formData.privateInternalDocument.name ? (
              <Typography sx={{ color: "#147C7C", mb: 1 }}>
                {formData.privateInternalDocument.name}
              </Typography>
            ) : (
              <>
                <Box
                  sx={{
                    width: 50,
                    aspectRatio: "1/1",
                    display: "flex",
                    justifyContent: "center",
                    alignContent: "center",
                    textAlign: "center",
                    borderRadius: 20,
                    mb: 2,
                    // p: 5,
                    bgcolor: "#F2F4F7",
                  }}
                >
                  <img
                    width={30}
                    src={"/uploadFileIcon.svg"}
                    alt={"view"}
                  />
                </Box>

                <Typography
                  sx={{
                    color: "var(--primary-color)",
                    fontWeight: "500",
                    fontSize: "14px",
                  }}
                >
                  {t("muatNaik")}
                </Typography>
                <Typography
                  sx={{
                    color: "#667085",
                    fontWeight: "400",
                    fontSize: "12px",
                  }}
                >
                  {`SVG, PNG, JPG or GIF (max. 800x400px)`}
                </Typography>
              </>
            )}
            <input
              id={`media${no}`}
              type="file"
              hidden
              onChange={handleFileChange}
              accept=".svg,.png,.jpg,.jpeg,.gif,.avi,.mp4,.mpeg,.pdf"
            />
          </Box>*/}
          <FileInput
            acceptedFormats={[".jpg", ".jpeg", ".png",".avi",".mp4",".mpeg",".pdf"]}
            id={`media${no}`}
            name={`media${no}`}
            value={formData.privateInternalDocument.doc}
            currentIndex={0}
            uploadedFilesRef={uploadedFilesRef}
            onChange={handleFileChange}
            currentName={formData.privateInternalDocument.name}
            t={t}
          />
          {(!formData.privateInternalDocument.name) ? (
            <FormHelperText sx={{ color: "red" }}>
              {t("fieldRequired")}
            </FormHelperText>
          ) : null}
        </Grid>
        <Grid item xs={12} sm={4}>
          <Typography sx={labelStyle}>
            {t("youtubeLink")} <span style={{ color: "red" }}>*</span>
          </Typography>
        </Grid>
        <Grid item xs={12} sm={8}>
          <TextField
            size={"small"}
            fullWidth
            required
            name="youtubeLink"
            value={formData.youtubeLink}
            error={!!formErrors.youtubeLink}
            helperText={formErrors.youtubeLink}
            onChange={handleInputChange}
          />
        </Grid>
        <Grid item xs={12} sm={4}>
          <Typography sx={labelStyle}>
            {t("chapterDescription")} <span style={{ color: "red" }}>*</span>
          </Typography>
        </Grid>
        <Grid item xs={12} sm={8}>
          <TextField
            size={"small"}
            fullWidth
            required
            multiline
            rows={4}
            name="description"
            value={formData.description}
            error={!!formErrors.description}
            helperText={formErrors.description}
            onChange={handleInputChange}
          />
        </Grid>
      </Grid>
    </Box>
  </>);
}

export default ChapterFragment;
