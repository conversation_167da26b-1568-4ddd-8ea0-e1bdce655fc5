import {
  DecisionOptions,
  DecisionOptionsCode,
  DecisionOptionsCodeGeran,
  DecisionRoOptionsCode,
  globalStyles,
  useMutation,
  useQuery,
} from "@/helpers";
import React, { useState } from "react";
import { FieldValues, useForm } from "react-hook-form";
import { useTranslation } from "react-i18next";

import {
  ButtonPrimary,
  DialogConfirmation,
  SelectFieldController,
  TextFieldController,
} from "@/components";
import { Box, Grid, Typography } from "@mui/material";
import { useNavigate } from "react-router-dom";
import KuiriIbuPejabatTab from "./KuiriIbuPejabatTab";
import KuiriNegeriTab from "./KuiriNegeriTab";
import PermohonanBaruTab from "./PermohonanBaruTab";
import KuiriNegeriIbuPejabatTab from "./KuiriNegeriIbuPejabat";
import i18n from "@/i18n";
import { LoadingOverlay } from "@/components/loading";
const labelStyle = {
  fontSize: "16px",
  color: "#666666",
  fontWeight: "400 !important",
};

interface GeranBoxesProps {
  data: {
    name: string;
    number: number;
  };
  isActive: boolean;
  onClick: () => void;
}

interface ApplicationPayload {
  grantApplicationId: string[] | number[];
  applicationStatusCode: number;
  roApprovalType: string;
  noteState: string;
}
const SaringanNegeri: React.FC = () => {
  const { t } = useTranslation();
  const classes = globalStyles();
  const navigate = useNavigate();
  const [dialogOpen, setDialogOpen] = useState(false);
  const [isSuccess, setIsSuccess] = useState(false);
  const [payload, setPayload] = useState<ApplicationPayload | null>(null);
  const isMyLanguage = i18n.language === "my";
  const defaultTab = "permohonan-baru";
  const [activeTab, setActiveTab] = useState(defaultTab);
  const decisionOptions = DecisionOptionsCodeGeran(t);
  const {
    data: applicationGrantListPendingDataResponse,
    isLoading: isLoadingApplicationGrantListData,
    refetch: refetchSocietyGrantList,
  } = useQuery({
    url: `grant/application`,
    filters: [{ field: "status", value: "PENDING_STATE", operator: "eq" }],
  });

  const {
    data: applicationGrantQueryStateListDataResponse,
    isLoading: isLoadingApplicationGrantQueryStateListData,
    refetch: refetchSocietyGrantQueryStateList,
  } = useQuery({
    url: `grant/application`,
    filters: [
      { field: "status", value: "QUERIED", operator: "eq" },
      { field: "typeQuery", value: "STATE", operator: "eq" },
    ],
  });

  const {
    data: applicationGrantQueryHqListDataResponse,
    isLoading: isLoadingApplicationGrantQueryHqListData,
    refetch: refetchSocietyGrantQueryHqList,
  } = useQuery({
    url: `grant/application`,
    filters: [
      { field: "status", value: "QUERIED", operator: "eq" },
      { field: "typeQuery", value: "HQ", operator: "eq" },
      { field: "hasApprovalState", value: true, operator: "eq" },
    ],
  });


  const [selectedIdsByTab, setSelectedIdsByTab] = useState<
    Record<string, string[] | number[]>
  >({});

  const handleSelectedIdsChange = (ids: string[] | number[]) => {
    setSelectedIdsByTab((prev) => ({
      ...prev,
      [activeTab]: ids,
    }));
  };
  const [refreshKey, setRefreshKey] = useState(0);

  const {
    fetch: updateApplicationGeran,
    isLoading: isUpdatingApplicationGeran,
  } = useMutation({
    url: "grant/application/state-approvals",
    method: "put",
    onSuccess: (res) => {
      const responseCode = res?.data?.code;
      if (responseCode === 200) {
        setIsSuccess(true);
        setTimeout(() => {
          navigate("../carian-permohonan", { replace: true });
        }, 2000);
      }
    },
  });

  const handleConfirmedSubmit = async () => {
    const selectedIds = selectedIdsByTab[activeTab] || [];
    const dataPayload = {
      applicationStatusCode: getValues("applicationStatusCode"),
      roApprovalType: "GRANT_APPLICATION_STATE",
      noteState: getValues("note"),
      grantApplicationId: selectedIds,
    };
    await updateApplicationGeran(dataPayload);
  };

  const onSubmit = () => {
    setDialogOpen(true);
  };
  const totalGrantApplication =
    applicationGrantListPendingDataResponse?.data?.data?.total;

  const totalGrantQueryStateApplication =
    applicationGrantQueryStateListDataResponse?.data?.data?.total;

  const totalGrantQueryHqApplication =
    applicationGrantQueryHqListDataResponse?.data?.data?.total;
  const setActiveTabContent = (slug: string) => {
    setActiveTab(slug);
    setSelectedIdsByTab((prev) => ({
      ...prev,
      [slug]: [],
    }));
  };

  const tab = [
    {
      name: "Permohonan Baru",
      slug: "permohonan-baru",
      number: totalGrantApplication || 0,
    },
    {
      name: "Kuiri",
      slug: "kuiri-kepada-negeri",
      number: totalGrantQueryStateApplication || 0,
    },
    {
      name: "Kuiri dari Ibu Pejabat",
      slug: "kuiri-dari-ibu-pejabat",
      number: totalGrantQueryHqApplication || 0,
    },
  ];

  const renderTab = () => {
    switch (activeTab) {
      case "permohonan-baru":
        return (
          <PermohonanBaruTab onSelectIdsChange={handleSelectedIdsChange} />
        );
      case "kuiri-kepada-negeri":
        return <KuiriNegeriTab onSelectIdsChange={handleSelectedIdsChange} />;
      case "kuiri-dari-ibu-pejabat":
        return (
          <KuiriNegeriIbuPejabatTab
            onSelectIdsChange={handleSelectedIdsChange}
          />
        );
      default:
        return null;
    }
  };

  const { control, handleSubmit, getValues, watch, setValue } =
    useForm<FieldValues>({
      defaultValues: {
        approvalStatus: "",
        rejectReason: "",
        note: "",
      },
    });

  const applicationStatusCode = watch("applicationStatusCode");
  const [isFormDisabled, setIsFormDisabled] = useState(false);

  return (
    <>
      <Box className={classes.section} mb={2}>
        <Box className={classes.sectionBox}>
          <Typography className="title" mb={4}>
            Saringan Negeri
          </Typography>
          <Box
            sx={{
              display: "grid",
              gridTemplateColumns: "repeat(auto-fit, minmax(180px, 1fr))",
              gap: "16px",
            }}
          >
            <Grid container spacing={1}>
              {tab.map((data, index) => {
                return (
                  <Grid item sm={2} key={index}>
                    <GeranBoxes
                      key={index}
                      data={data}
                      isActive={data.slug === activeTab}
                      onClick={() => setActiveTabContent(data.slug)}
                    />
                  </Grid>
                );
              })}
            </Grid>
          </Box>
        </Box>
      </Box>
      {renderTab()}
      <Box className={classes.section} mb={2}>
        <Box className={classes.sectionBox}>
          <Typography className="title" mb={4}>
            Keputusan Syor Negeri
          </Typography>
          <Grid container>
            <Grid item xs={12} sm={4}>
              <Typography variant="body1" sx={labelStyle}>
                Syor Negeri
              </Typography>
            </Grid>

            <Grid item xs={12} sm={8} sx={{ mb: 3 }}>
              <SelectFieldController
                control={control}
                name="applicationStatusCode"
                options={decisionOptions}
                disabled={isFormDisabled}
                sx={{
                  background: isFormDisabled ? "rgba(218, 218, 218, 0.5)" : "",
                }}
                required
              />
            </Grid>

            <Grid item xs={12} sm={4}>
              <Typography variant="body1" sx={labelStyle}>
                Catatan Keputusan
              </Typography>
            </Grid>
            <Grid item xs={12} sm={8}>
              <TextFieldController
                control={control}
                name="note"
                multiline
                disabled={isFormDisabled}
                sxInput={{
                  minHeight: "92px",
                }}
              />
            </Grid>
          </Grid>
        </Box>
        <ButtonPrimary
          type="submit"
          onClick={handleSubmit(onSubmit)}
          sx={{
            marginLeft: "auto",
            display: "block",
            mt: 3,
            fontSize: "10px",
          }}
        >
          Kemaskini Keputusan
        </ButtonPrimary>
      </Box>
      <DialogConfirmation
        isMutating={isUpdatingApplicationGeran}
        open={dialogOpen}
        onClose={() => setDialogOpen(false)}
        onAction={handleConfirmedSubmit}
        onConfirmationText={
          applicationStatusCode === 18
            ? t("syorConfirmationGeran")
            : applicationStatusCode === 19
            ? t("tidakSyorConfirmationGeran")
            : t("kuiriConfirmationGeran")
        }
        isSuccess={isSuccess}
        onSuccessText={
          applicationStatusCode === 18
            ? t("syorSuccessGeran")
            : applicationStatusCode === 19
            ? t("tidakSyorSuccessGeran")
            : t("kuiriSuccessGeran")
        }
      />
    </>
  );
};

const GeranBoxes: React.FC<GeranBoxesProps> = React.memo(
  ({ data, isActive, onClick }) => {
    return (
      <Box
        onClick={onClick}
        sx={{
          position: "relative",
          padding: "12px 20px 20px 5px",
          border: isActive ? "1px solid #00B69B" : "1px solid #00B69B",
          borderRadius: "8px",
          backgroundColor: isActive ? "var(--primary-color)" : "transparent",
          color: isActive ? "#fff" : "var(--primary-color)",

          height: "80px",
          cursor: "pointer",
          transition: "all 0.3s",
          "&:hover": {
            backgroundColor: isActive ? "var(--primary-color)" : "#fff",
          },
        }}
      >
        <Box
          sx={{
            color: isActive ? "#fff" : "var(--primary-color)",
            fontWeight: 400,
            fontSize: "14px",
          }}
        >
          {data.name}
        </Box>
        <Typography
          sx={{
            position: "absolute",
            bottom: 2,
            right: "10px",
            color: isActive ? "#fff" : "var(--primary-color)",
            fontWeight: 500,
          }}
        >
          {data.number}
        </Typography>
      </Box>
    );
  }
);

export default SaringanNegeri;
