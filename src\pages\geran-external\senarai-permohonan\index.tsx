import { globalStyles, useQuery } from "@/helpers";
import { Box, IconButton, Typography } from "@mui/material";
import { useNavigate } from "react-router-dom";

import { EditIcon, EyeIcon } from "@/components/icons";
import { LoadingOverlay } from "@/components/loading";
import { GridVisibilityOffIcon } from "@mui/x-data-grid";
import { useEffect } from "react";

interface GrantSocietyItem {
  id: string | number;
  societyName: string;
  societyNo: string | null;
  status: string;
  year: number;
  grantTemplateId: string | number;
  grantApplicationId: string | number;
}
const SenaraiPermohonan: React.FC = () => {
  const navigate = useNavigate();
  const classes = globalStyles();

  const {
    data: societyGrantListDataResponse,
    isLoading: isLoadingSocietyGrantListData,
    refetch: refetchSocietyGrantList,
  } = useQuery({
    url: `grant/application/society/committee`,
  });
  const societyGrantList: GrantSocietyItem[] =
    societyGrantListDataResponse?.data?.data ?? [];
  useEffect(() => {
    refetchSocietyGrantList();
  }, []);

  return (
    <>
      <Box className={classes.section} mt={1}>
        <Box className={classes.sectionBox}>
          <Typography className="title" mb={2}>
            Persatuan
          </Typography>
          {isLoadingSocietyGrantListData ? (
            <LoadingOverlay />
          ) : societyGrantList.length === 0 ? (
            <Typography
              sx={{
                textAlign: "center",
                fontSize: "14px",
                color: "#888",
                mt: 2,
              }}
            >
              Tiada data dijumpai.
            </Typography>
          ) : (
            societyGrantList.map((item: GrantSocietyItem) => (
              <Box key={item.id} className={classes.sectionBox} mb={1}>
                <Box
                  sx={{
                    display: "flex",
                    justifyContent: "space-between",
                    alignItems: "center",
                  }}
                >
                  <Typography
                    className="label"
                    sx={{ flex: 2, textAlign: "center" }}
                  >
                    {item.societyName}
                  </Typography>

                  <Typography
                    className="label"
                    sx={{ flex: 2, textAlign: "center" }}
                  >
                    {item.societyNo || "-"}
                  </Typography>

                  <Typography
                    className="label"
                    sx={{ flex: 2, textAlign: "center" }}
                  >
                    {item.year || "-"}
                  </Typography>

                  <Box
                    sx={{
                      display: "flex",
                      alignItems: "center",
                      gap: 2,
                      flex: 1,
                      minWidth: "180px",
                    }}
                  >
                    <Typography
                      sx={{
                        width: "100%",
                        height: "29px",
                        lineHeight: "29px",

                        textAlign: "center",
                        border: `0.5px solid ${
                          item.status === "APPLICABLE" ||
                          item.status === "QUERIED"
                            ? "#9747FF"
                            : item.status === "PENDING_STATE" ||
                              item.status === "PENDING_HQ" ||
                              item.status === "APPROVED" ||
                              item.status === "REJECTED" ||
                              item.status === "DRAFT"
                            ? "#FFC107"
                            : "#FF4D4F"
                        }`,
                        borderRadius: "15px",
                        fontSize: "10px",
                        fontWeight: 500,
                        fontFamily: "Poppins, sans-serif",
                        color: "var(--text-grey)",
                        backgroundColor: "#fff",
                      }}
                    >
                      {item.status === "APPLICABLE"
                        ? "Layak"
                        : item.status === "PENDING_STATE" ||
                          item.status === "PENDING_HQ"
                        ? "Menunggu Keputusan"
                        : item.status === "DRAFT"
                        ? "Draft"
                        : item.status === "APPROVED"
                        ? "Lulus"
                        : item.status === "REJECTED"
                        ? "Tolak"
                        : item.status === "CANCELLED"
                        ? "Dibatalkan"
                        : item.status === "QUERIED"
                        ? "Kuiri"
                        : "Tidak Layak"}
                    </Typography>

                    {item.status === "APPLICABLE" && (
                      <IconButton
                        onClick={() =>
                          navigate(
                            `./senarai-permohonan/${item.id}/${item.grantTemplateId}`
                          )
                        }
                      >
                        <EditIcon sx={{ color: "#1DC1C1" }} />
                      </IconButton>
                    )}
                    {(item.status === "QUERIED" || item.status === "DRAFT") && (
                      <IconButton
                        onClick={() =>
                          navigate(
                            `./senarai-permohonan/${item.id}/${item.grantTemplateId}/${item.grantApplicationId}`
                          )
                        }
                      >
                        <EditIcon sx={{ color: "#1DC1C1" }} />
                      </IconButton>
                    )}

                    {(item.status === "PENDING_STATE" ||
                      item.status === "PENDING_HQ" ||
                      item.status === "APPROVED" ||
                      item.status === "CANCELLED" ||
                      item.status === "REJECTED") && (
                      <IconButton
                        onClick={() =>
                          navigate(
                            `./senarai-permohonan/view/${item.grantApplicationId}`
                          )
                        }
                      >
                        <EyeIcon sx={{ color: "#FFA500" }} />
                      </IconButton>
                    )}
                  </Box>
                </Box>
              </Box>
            ))
          )}
        </Box>
      </Box>
    </>
  );
};

export default SenaraiPermohonan;
