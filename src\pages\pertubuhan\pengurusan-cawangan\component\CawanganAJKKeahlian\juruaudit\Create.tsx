import { Box, Typography } from "@mui/material";
import { useTranslation } from "react-i18next";
import { useLocation, useNavigate, useParams } from "react-router-dom";
import { ButtonOutline, ButtonPrimary } from "@/components/button";
import { useEffect } from "react";
import { API_URL } from "@/api";
import { useCustomMutation } from "@refinedev/core";
import {
  CitizenshipStatus,
  IdTypes,
  ListGelaran,
  ListGender,
  MALAYSIA,
} from "@/helpers/enums";
import Input from "@/components/input/Input";
import { usejawatankuasaContext } from "../jawatankuasa/jawatankuasaProvider";
import {
  Controller,
  FieldValues,
  SubmitHandler,
  useForm,
} from "react-hook-form";
import { filterEmptyValuesOnObject, getLocalStorage } from "@/helpers/utils";
import dayjs from "dayjs";
import { useSelector } from "react-redux";

export const CreateJuruAudit: React.FC = () => {
  const navigate = useNavigate();
  const { t } = useTranslation();

  const handleBack = () => {
    navigate(-1);
  };

  const occupationList = getLocalStorage("occupation_list", []);

  const location = useLocation();
  const auditorId = location.state?.auditorId;

  const {
    addressList,
    fetchAddressList,
    auditor,
    fetchAuditor,
    members,
    fetchMembers,
    fetchBranchMembers,
    branchMembers,
  } = usejawatankuasaContext();

  useEffect(() => {
    fetchAddressList();
    fetchBranchMembers();
  }, [fetchAddressList, fetchBranchMembers]);

  const sectionStyle = {
    color: "var(--primary-color)",
    marginBottom: "16px",
    borderRadius: "16px",
    fontSize: "14px",
    fontWeight: "500 !important",
  };
  // @ts-ignore
  const branchDataRedux = useSelector((state) => state?.branchData?.data);

  const defaultFormValues = {
    id: "",
    societyId: "",
    societyNo: "",
    branchNo: "",
    statementId: "",
    auditorType: "",
    titleCode: "",
    name: "",
    licenseNo: "",
    companyName: "",
    gender: "",
    nationalityStatus: "",
    identificationType: "",
    identificationNo: "",
    dateOfBirth: "",
    placeOfBirth: "",
    employmentCode: "",
    address: "",
    countryCode: "",
    stateCode: "",
    districtCode: "",
    smallDistrictCode: "",
    city: "",
    postcode: "",
    email: "",
    telephoneNo: "",
    phoneNo: "",
    appointmentDate: "",
    createdBy: "",
    createdDate: "",
    modifiedBy: "",
    modifiedDate: "",
    status: "",
    deleteStatus: "",
    pemCaw: "",
  };

  const {
    control,
    setValue,
    watch,
    getValues,
    handleSubmit,
    reset: resetForm,
    setError,
    clearErrors,
  } = useForm<FieldValues>({
    defaultValues: defaultFormValues,
    mode: "onChange", // Enable real-time validation
  });

  useEffect(() => {
    if (auditorId) {
      fetchAuditor();
      if (auditor) {
        Object.entries(auditor).forEach(([key, value]) => {
          if (value == "" || value == null) {
            setValue(key, "-");
          } else {
            setValue(key, value);
          }
        });
      }
    }
  }, [fetchAuditor]);

  const { id: societyId } = useParams();

  const { mutate: saveAuditor, isLoading: isLoadingSaveAuditor } =
    useCustomMutation();

  const onSubmit: SubmitHandler<FieldValues> = (data) => {
    const payload = filterEmptyValuesOnObject(data);
    saveAuditor(
      {
        url: auditorId
          ? `${API_URL}/society/statement/auditor/${auditorId}/edit`
          : `${API_URL}/society/statement/auditor/create?societyId=${societyId}&branchId=${branchDataRedux.id}`,
        method: auditor ? "put" : "post",
        values: payload,
        config: {
          headers: {
            portal: localStorage.getItem("portal"),
            authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
          },
        },
      },
      {
        onSuccess: () => {
          navigate(-1);
        },
      }
    );
  };

  useEffect(() => {
    const type = getValues("identificationType");
    if (type === "1") {
      const idNoString =
        typeof watch("identificationNo") === "number"
          ? String(watch("identificationNo"))
          : watch("identificationNo") ?? ""; // Ensure string

      if (idNoString.length >= 6) {
        const parsedDate = dayjs(idNoString.substring(0, 6), "YYMMDD");

        if (parsedDate.isValid()) {
          setValue("dateOfBirth", parsedDate.toDate());
        }
      }
    }
  }, [watch("identificationNo")]);

  return (
    <Box
      sx={{
        p: { xs: 1, sm: 2, md: 3 },
        backgroundColor: "white",
        borderRadius: "14px",
        mb: 2,
      }}
    >
      <form onSubmit={handleSubmit(onSubmit)}>
        <Box
          sx={{
            border: "1px solid rgba(0, 0, 0, 0.12)",
            borderRadius: "14px",
            p: 3,
            mb: 2,
          }}
        >
          <Typography variant="subtitle1" sx={sectionStyle}>
            {t("pilihanJuruaudit")}
          </Typography>

          <Controller
            name="auditorType"
            control={control}
            defaultValue={getValues("auditorType")}
            render={({ field }) => (
              <Input
                {...field}
                label={t("auditorType")}
                type="select"
                required
                onChange={(e) => setValue("auditorType", e.target.value)}
                options={[
                  { value: "L", label: "Bertauliah" },
                  { value: "D", label: "Dalaman" },
                ]}
              />
            )}
          />
          {watch("auditorType") === "D" && (
            <Controller
              name="committeeName"
              control={control}
              defaultValue={getValues("committeeName")}
              render={({ field }) => {
                const options = branchMembers
                  ? branchMembers?.data?.map((item: { id: any; name: any }) => {
                      return {
                        value: item.name, // Use name as value instead of id
                        label: item.name || "-",
                      };
                    })
                  : [];

                // Find the current selected member by name
                const currentName = getValues("name");
                const selectedValue = currentName || "";
                return (
                  <Input
                    // required
                    {...field}
                    label={t("namaAhliPertubuhan")}
                    type="select"
                    value={selectedValue} // Use name as the selected value
                    onChange={(e) => {
                      const selectedName = e.target.value; // Get the selected name
                      const selectedOption = branchMembers?.data?.find(
                        (item: { id: any; name: any }) =>
                          item.name === selectedName
                      ); // Find the corresponding option by name
                      setValue(
                        "identificationType",
                        selectedOption?.identificationType
                      );
                      setValue("name", selectedOption?.name ?? "-"); // Set the value as the label
                      let nationality = selectedOption?.nationalityStatus;
                      // Handle both string numbers and text values
                      if (
                        selectedOption?.nationalityStatus === "Warganegara" ||
                        selectedOption?.nationalityStatus === "1"
                      ) {
                        nationality = 1;
                      } else if (
                        selectedOption?.nationalityStatus ===
                          "Bukan Warganegara" ||
                        selectedOption?.nationalityStatus === "2"
                      ) {
                        nationality = 2;
                      }
                      if (nationality) {
                        setValue("nationalityStatus", Number(nationality));
                      }
                      setValue("gender", selectedOption?.gender ?? "-"); // Set the value as the label
                      setValue("placeOfBirth", selectedOption?.placeOfBirth);
                      setValue("committeeName", selectedOption?.id); // Store the ID in committeeName for backend
                    }}
                    options={options}
                  />
                );
              }}
            />
          )}
        </Box>

        {watch("auditorType") === "D" && (
          <Box
            sx={{
              border: "1px solid rgba(0, 0, 0, 0.12)",
              borderRadius: "14px",
              p: 3,
              mb: 2,
            }}
          >
            <Typography variant="subtitle1" sx={sectionStyle}>
              {t("maklumatJuruauditDalaman")}
            </Typography>

            <Controller
              name="appointmentDate"
              control={control}
              render={({ field }) => {
                return (
                  <Input
                    required
                    {...field}
                    onChange={(newValue) =>
                      setValue("appointmentDate", newValue.target.value)
                    }
                    value={
                      getValues("appointmentDate")
                        ? dayjs(getValues("appointmentDate")).format(
                            "DD-MM-YYYY"
                          )
                        : ""
                    }
                    label={t("tarikhLantik")}
                    type="date"
                  />
                );
              }}
            />

            <Controller
              name="titleCode"
              control={control}
              defaultValue={getValues("titleCode")}
              render={({ field }) => (
                <Input
                  {...field}
                  required
                  label={t("title")}
                  type="select"
                  options={ListGelaran}
                />
              )}
            />

            <Controller
              name="name"
              control={control}
              defaultValue={getValues("name")}
              render={({ field }) => (
                <Input
                  {...field}
                  disabled
                  required
                  label={t("fullName")}
                  type="text"
                />
              )}
            />

            <Controller
              name="gender"
              control={control}
              defaultValue={getValues("gender")}
              render={({ field }) => (
                <Input
                  {...field}
                  required
                  disabled
                  label={t("gender")}
                  type="select"
                  value={getValues("gender")}
                  options={ListGender.map((item) => ({
                    ...item,
                    label: t(item.label),
                  }))}
                />
              )}
            />

            <Controller
              name="nationalityStatus"
              control={control}
              defaultValue={Number(getValues("nationalityStatus"))}
              render={({ field }) => (
                <Input
                  {...field}
                  disabled
                  required
                  label={t("citizenship")}
                  type="select"
                  value={Number(getValues("nationalityStatus"))}
                  options={CitizenshipStatus.map((item) => ({
                    ...item,
                    label: t(item.label),
                  }))}
                />
              )}
            />

            <Controller
              name="identificationType"
              control={control}
              defaultValue={getValues("identificationType")}
              render={({ field }) => (
                <Input
                  {...field}
                  required
                  disabled
                  label={t("idType")}
                  type="select"
                  options={IdTypes.map((item) => ({
                    ...item,
                    label: t(item.label),
                  }))}
                />
              )}
            />

            <Controller
              name="identificationNo"
              control={control}
              defaultValue={getValues("identificationNo")}
              render={({ field }) => (
                <Input {...field} required label={t("idNumber")} type="text" />
              )}
            />

            <Controller
              name="dateOfBirth"
              control={control}
              defaultValue={getValues("dateOfBirth")}
              render={({ field }) => {
                return (
                  <Input
                    required
                    {...field}
                    label={t("dateOfBirth")}
                    type="date"
                    onChange={(newValue) =>
                      setValue("dateOfBirth", newValue.target.value)
                    }
                    value={
                      getValues("dateOfBirth")
                        ? dayjs(getValues("dateOfBirth")).format("DD-MM-YYYY")
                        : ""
                    }
                  />
                );
              }}
            />

            <Controller
              name="placeOfBirth"
              control={control}
              defaultValue={getValues("placeOfBirth")}
              render={({ field }) => (
                <Input
                  {...field}
                  required
                  label={t("placeOfBirth")}
                  type="text"
                />
              )}
            />

            <Controller
              name="employmentCode"
              control={control}
              defaultValue={getValues("employmentCode")}
              render={({ field }) => (
                <Input
                  {...field}
                  required
                  label={t("occupation")}
                  type="select"
                  options={occupationList}
                />
              )}
            />

            <Controller
              name="address"
              control={control}
              defaultValue={getValues("address")}
              render={({ field }) => (
                <Input
                  {...field}
                  required
                  label={t("residentialAddress")}
                  type="text"
                  multiline
                  rows={3}
                />
              )}
            />

            <Controller
              name="stateCode"
              control={control}
              defaultValue={getValues("stateCode")}
              render={({ field }) => (
                <Input
                  {...field}
                  required
                  label={t("state")}
                  type="select"
                  onChange={(e) => setValue("stateCode", e.target.value)}
                  value={parseInt(getValues("stateCode"))}
                  options={addressList
                    .filter((item: any) => item.pid === MALAYSIA)
                    .map((item: any) => ({
                      label: item.name,
                      value: item.id,
                    }))}
                />
              )}
            />

            <Controller
              name="districtCode"
              control={control}
              defaultValue={getValues("districtCode")}
              render={({ field }) => (
                <Input
                  {...field}
                  required
                  label={t("district")}
                  type="select"
                  value={parseInt(getValues("districtCode"))}
                  onChange={(e) => setValue("districtCode", e.target.value)}
                  options={addressList
                    .filter(
                      (item: any) => item.pid === parseInt(watch("stateCode"))
                    )
                    .map((item: any) => ({ label: item.name, value: item.id }))}
                />
              )}
            />

            <Controller
              name="city"
              control={control}
              defaultValue={getValues("city")}
              render={({ field }) => (
                <Input {...field} label={t("city")} type="text" />
              )}
            />

            <Controller
              name="postcode"
              control={control}
              defaultValue={getValues("postcode")}
              render={({ field }) => (
                <Input {...field} required label={t("poskod")} type="text" />
              )}
            />

            <Controller
              name="email"
              control={control}
              defaultValue={getValues("email")}
              rules={{
                required: t("validation.required"),
                pattern: {
                  value: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
                  message:
                    t("invalidEmail") || "Sila masukkan e-mel yang betul.",
                },
              }}
              render={({ field, fieldState: { error } }) => (
                <Input
                  {...field}
                  required
                  label={t("email")}
                  type="email"
                  error={!!error}
                  helperText={error?.message}
                />
              )}
            />

            <Controller
              name="phoneNo"
              control={control}
              defaultValue={getValues("phoneNo")}
              rules={
                watch("auditorType") === "D"
                  ? { required: t("requiredValidation") }
                  : {}
              }
              render={({ field, fieldState: { error } }) => {
                return (
                  <Input
                    {...field}
                    value={field.value || ""}
                    required={watch("auditorType") === "D"}
                    label={t("phoneNumber")}
                    type="text"
                    inputProps={{
                      inputMode: "numeric",
                    }}
                    onChange={(e) => {
                      const input = e.target as HTMLInputElement;
                      let raw = input.value.replace(/[^\d]/g, "");
                      // Allow empty input
                      if (!raw) {
                        setValue("phoneNo", null);
                        return;
                      }

                      // Remove leading 60 if present
                      if (raw.startsWith("60")) {
                        raw = raw.slice(2);
                      }

                      const limitedDigits = raw.slice(0, 10);
                      const formatted = "+60" + limitedDigits;

                      let error: any = "";
                      if (limitedDigits.length < 8) {
                        error = t("phoneDigitLimitWarning");
                      } else {
                        error = null;
                      }

                      setValue("phoneNo", formatted);

                      if (limitedDigits.length < 8) {
                        setError("phoneNo", {
                          type: "manual",
                          message: t("phoneDigitLimitWarning"),
                        });
                      } else {
                        clearErrors("phoneNo");
                      }
                    }}
                    onKeyDown={(e) => {
                      const input = e.target as HTMLInputElement;
                      const pos = input.selectionStart ?? 0;
                      const hasValue = input.value.length > 0;

                      // restrictions
                      if (hasValue) {
                        if (
                          (e.key.length === 1 && pos < 3) || // typing characters in +60
                          (e.key === "Backspace" && pos <= 3) || // backspacing into +60
                          (e.key === "Delete" && pos < 3) // deleting inside +60
                        ) {
                          e.preventDefault();
                        }
                      }
                    }}
                    onClick={(e) => {
                      const input = e.target as HTMLInputElement;
                      if (
                        input.value &&
                        input.selectionStart !== null &&
                        input.selectionStart < 3
                      ) {
                        // Move cursor to after +60 if user clicks inside prefix
                        setTimeout(() => {
                          input.setSelectionRange(3, 3);
                        }, 0);
                      }
                    }}
                    onFocus={(e) => {
                      const input = e.target as HTMLInputElement;
                      if (
                        input.value &&
                        input.selectionStart !== null &&
                        input.selectionStart < 3
                      ) {
                        // move cursor to after +60 on focus
                        setTimeout(() => {
                          input.setSelectionRange(3, 3);
                        }, 0);
                      }
                    }}
                    placeholder={
                      Number(getValues("identificationType")) === 1 ? "+60" : ""
                    }
                    // onChange={(e) => {
                    //   const numericValue = e.target.value.replace(
                    //     /[^0-9]/g,
                    //     ""
                    //   );
                    //   if (numericValue.length <= 11) {
                    //     field.onChange(numericValue);
                    //   }
                    // }}
                    error={!!error}
                    helperText={error?.message}
                  />
                );
              }}
            />

            <Controller
              name="telephoneNo"
              control={control}
              defaultValue={getValues("telephoneNo")}
              render={({ field }) => (
                <Input {...field} label={t("homeNumber")} type="text" />
              )}
            />
          </Box>
        )}

        {watch("auditorType") === "L" && (
          <Box
            sx={{
              border: "1px solid rgba(0, 0, 0, 0.12)",
              borderRadius: "14px",
              p: 3,
              mb: 2,
            }}
          >
            <Typography variant="subtitle1" sx={sectionStyle}>
              {t("maklumatJuruauditBertauliah")}
            </Typography>

            <Controller
              name="appointmentDate"
              control={control}
              defaultValue={getValues("appointmentDate")}
              render={({ field }) => {
                const date = getValues("appointmentDate");

                const formattedDate = dayjs(date).format("DD-MM-YYYY");

                return (
                  <Input
                    required
                    {...field}
                    label={t("tarikhLantik")}
                    type="date"
                    value={formattedDate}
                  />
                );
              }}
            />

            <Controller
              name="name"
              control={control}
              defaultValue={getValues("name")}
              render={({ field }) => (
                <Input {...field} required label={t("fullName")} type="text" />
              )}
            />
            {/*  ==========   */}
            <Controller
              name="gender"
              control={control}
              defaultValue={getValues("gender")}
              render={({ field }) => (
                <Input
                  {...field}
                  required
                  label={t("gender")}
                  type="select"
                  value={getValues("gender")}
                  options={ListGender.map((item) => ({
                    ...item,
                    label: t(item.label),
                  }))}
                />
              )}
            />

            <Controller
              name="nationalityStatus"
              control={control}
              defaultValue={Number(getValues("nationalityStatus"))}
              render={({ field }) => (
                <Input
                  {...field}
                  required
                  label={t("citizenship")}
                  type="select"
                  value={Number(getValues("nationalityStatus"))}
                  options={CitizenshipStatus.map((item) => ({
                    ...item,
                    label: t(item.label),
                  }))}
                />
              )}
            />

            <Controller
              name="identificationType"
              control={control}
              defaultValue={getValues("identificationType")}
              render={({ field }) => (
                <Input
                  {...field}
                  required
                  label={t("idType")}
                  type="select"
                  options={IdTypes.map((item) => ({
                    ...item,
                    label: t(item.label),
                  }))}
                />
              )}
            />

            <Controller
              name="identificationNo"
              control={control}
              defaultValue={getValues("identificationNo")}
              render={({ field }) => (
                <Input {...field} required label={t("idNumber")} type="text" />
              )}
            />

            {/*  ==========   */}
            <Controller
              name="licenseNo"
              control={control}
              defaultValue={getValues("licenseNo")}
              render={({ field }) => (
                <Input
                  {...field}
                  required
                  label={t("nomborLesen")}
                  type="text"
                />
              )}
            />

            <Controller
              name="companyName"
              control={control}
              defaultValue={getValues("companyName")}
              render={({ field }) => (
                <Input
                  {...field}
                  required
                  label={t("companyName")}
                  type="text"
                />
              )}
            />

            <Controller
              name="address"
              control={control}
              defaultValue={getValues("address")}
              render={({ field }) => (
                <Input
                  {...field}
                  required
                  label={t("residentialAddress")}
                  type="text"
                  multiline
                  rows={3}
                />
              )}
            />
            <Controller
              name="stateCode"
              control={control}
              defaultValue={getValues("stateCode")}
              render={({ field }) => (
                <Input
                  {...field}
                  required
                  label={t("state")}
                  type="select"
                  value={parseInt(getValues("stateCode"))}
                  onChange={(e) => setValue("stateCode", e.target.value)}
                  options={addressList
                    .filter((item: any) => item.pid === MALAYSIA)
                    .map((item: any) => ({
                      label: item.name,
                      value: item.id,
                    }))}
                />
              )}
            />

            <Controller
              name="districtCode"
              control={control}
              defaultValue={getValues("districtCode")}
              render={({ field }) => (
                <Input
                  {...field}
                  required
                  label={t("district")}
                  type="select"
                  value={parseInt(getValues("districtCode"))}
                  onChange={(e) => setValue("districtCode", e.target.value)}
                  options={addressList
                    .filter(
                      (item: any) => item.pid === parseInt(watch("stateCode"))
                    )
                    .map((item: any) => ({ label: item.name, value: item.id }))}
                />
              )}
            />
            <Controller
              name="city"
              control={control}
              defaultValue={getValues("city")}
              render={({ field }) => (
                <Input {...field} label={t("city")} type="text" />
              )}
            />

            <Controller
              name="postcode"
              control={control}
              defaultValue={getValues("postcode")}
              render={({ field: { onChange, value, ...rest } }) => (
                <Input
                  {...rest}
                  value={value}
                  // required
                  label={t("poskod")}
                  type="text"
                  inputMode="numeric"
                  onChange={(e) => {
                    const onlyDigits = e.target.value
                      .replace(/\D/g, "")
                      .slice(0, 5);
                    onChange(onlyDigits);
                  }}
                />
              )}
            />

            <Controller
              name="email"
              control={control}
              defaultValue={getValues("email")}
              rules={{
                required: t("validation.required"),
                pattern: {
                  value: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
                  message:
                    t("invalidEmail") || "Sila masukkan e-mel yang betul.",
                },
              }}
              render={({ field, fieldState: { error } }) => (
                <Input
                  {...field}
                  required
                  label={t("emelSyarikat")}
                  type="email"
                  error={!!error}
                  helperText={error?.message}
                />
              )}
            />

            <Controller
              name="phoneNo"
              control={control}
              defaultValue={getValues("phoneNo")}
              rules={{
                required: t("validation.required"),
                pattern: {
                  value: /^[0-9]+$/,
                  message:
                    t("phoneNumberInvalid") ||
                    "Nombor telefon hanya boleh mengandungi angka.",
                },
              }}
              render={({
                field: { onChange, value, ...rest },
                fieldState: { error },
              }) => (
                <Input
                  {...rest}
                  value={value}
                  required
                  label={t("phoneNumber")}
                  type="text"
                  inputProps={{
                    inputMode: "numeric",
                  }}
                  onChange={(e) => {
                    const input = e.target as HTMLInputElement;
                    let raw = input.value.replace(/[^\d]/g, "");
                    // Allow empty input
                    if (!raw) {
                      setValue("phoneNo", null);
                      return;
                    }

                    // Remove leading 60 if present
                    if (raw.startsWith("60")) {
                      raw = raw.slice(2);
                    }

                    const limitedDigits = raw.slice(0, 10);
                    const formatted = "+60" + limitedDigits;

                    let error: any = "";
                    if (limitedDigits.length < 8) {
                      error = t("phoneDigitLimitWarning");
                    } else {
                      error = null;
                    }

                    setValue("phoneNo", formatted);

                    if (limitedDigits.length < 8) {
                      setError("phoneNo", {
                        type: "manual",
                        message: t("phoneDigitLimitWarning"),
                      });
                    } else {
                      clearErrors("phoneNo");
                    }
                  }}
                  onKeyDown={(e) => {
                    const input = e.target as HTMLInputElement;
                    const pos = input.selectionStart ?? 0;
                    const hasValue = input.value.length > 0;

                    // restrictions
                    if (hasValue) {
                      if (
                        (e.key.length === 1 && pos < 3) || // typing characters in +60
                        (e.key === "Backspace" && pos <= 3) || // backspacing into +60
                        (e.key === "Delete" && pos < 3) // deleting inside +60
                      ) {
                        e.preventDefault();
                      }
                    }
                  }}
                  onClick={(e) => {
                    const input = e.target as HTMLInputElement;
                    if (
                      input.value &&
                      input.selectionStart !== null &&
                      input.selectionStart < 3
                    ) {
                      // Move cursor to after +60 if user clicks inside prefix
                      setTimeout(() => {
                        input.setSelectionRange(3, 3);
                      }, 0);
                    }
                  }}
                  onFocus={(e) => {
                    const input = e.target as HTMLInputElement;
                    if (
                      input.value &&
                      input.selectionStart !== null &&
                      input.selectionStart < 3
                    ) {
                      // move cursor to after +60 on focus
                      setTimeout(() => {
                        input.setSelectionRange(3, 3);
                      }, 0);
                    }
                  }}
                  placeholder={
                    Number(getValues("identificationType")) === 1 ? "+60" : ""
                  }
                  error={!!error}
                  helperText={error?.message}
                />
              )}
            />
          </Box>
        )}

        <Box
          sx={{ display: "flex", justifyContent: "flex-end", mt: 2, gap: 2 }}
        >
          <ButtonOutline onClick={handleBack}>{t("back")}</ButtonOutline>
          <ButtonPrimary type="submit">{t("save")}</ButtonPrimary>
        </Box>
      </form>
    </Box>
  );
};

export default CreateJuruAudit;
