import React from "react";
import { Area, AreaChart, CartesianGrid, ResponsiveContainer, XAxis, YA<PERSON><PERSON> } from "recharts";
import { Box } from "@mui/material";


interface HebahanChartProps {
  data: any[]
}

const HebahanChart: React.FC<HebahanChartProps> = ({data}) => {


  const sampleData = [
    {
      name: '2016',
      uv: 4000,
      pv: 2400,
      amt: 2400,
    },
    {
      name: '2017',
      uv: 3000,
      pv: 1398,
      amt: 2210,
    },
    {
      name: '2018',
      uv: 2000,
      pv: 9800,
      amt: 2290,
    },
    {
      name: '2019',
      uv: 2780,
      pv: 3908,
      amt: 2000,
    },
    {
      name: '2020',
      uv: 1890,
      pv: 4800,
      amt: 2181,
    },
    {
      name: '2021',
      uv: 2390,
      pv: 3800,
      amt: 2500,
    },
    {
      name: '2022',
      uv: 3490,
      pv: 4300,
      amt: 2100,
    },
  ];

  return (
    <Box>
      <ResponsiveContainer width="95%" height={400}>
        <AreaChart
          //width={'80%'}
          //height={600}
          data={data}
          margin={{
            top: 10,
            right: 30,
            left: 0,
            bottom: 5,
          }}
        >
          <CartesianGrid strokeDasharray="3 3" />
          <XAxis dataKey="name" dy={15} />
          <YAxis dataKey={"pv"} />
          <Area type="monotone" dataKey="uv" stroke="#1DC1C1" fill="#1DC1C1" />
        </AreaChart>
      </ResponsiveContainer>
    </Box>
  )
}

export default HebahanChart
