import React from "react";
import {Box, Typography} from "@mui/material";
import {headerStyle} from "@/pages/hebahan/hebahanConstant";
import {useNavigate} from "react-router-dom";


const InternalHebahanDashboardSidebar: React.FC = () => {

  const navigate = useNavigate();

  return (
    <>
      <Box
        sx={{
          borderRadius: 2.5,
          backgroundColor: "#fff",
          //display: "inline",
          px: 2,
          py: 2,
          mb: 1,
        }}
      >
        <Typography sx={headerStyle}>
          {'Administrasi'}
        </Typography>
        <Box
          sx={{
            borderRadius: 2.5,
            backgroundColor: "#41C3C3",
            cursor: "pointer",
            //display: "inline",
            px: 2,
            py: 2,
            pb:5,
            mt:5,
            mb: 1,
          }}
          onClick={() => {
            navigate('list')
          }}
        >
          <Typography sx={{color:"#fff",fontWeight:500,fontSize:12}}>
            {'Cipta Artikel'}
          </Typography>
        </Box>
        <Box
          sx={{
            borderRadius: 2.5,
            backgroundColor: "#41C3C3",
            cursor: "pointer",
            //display: "inline",
            px: 2,
            py: 2,
            pb:5,
            mb: 1,
          }}
          onClick={() => {
            navigate('report')
          }}
        >
          <Typography sx={{color:"#fff",fontWeight:500,fontSize:12}}>
            {'Semak Hantaran Artikel'}
          </Typography>
        </Box>
        <Box
          sx={{
            borderRadius: 2.5,
            backgroundColor: "#41C3C3",
            cursor: "pointer",
            //display: "inline",
            px: 2,
            py: 2,
            pb:5,
            mb: 1,
          }}
          onClick={() => {
            navigate('')
          }}
        >
          <Typography sx={{color:"#fff",fontWeight:500,fontSize:12}}>
            {'Semak Laporan Artikel'}
          </Typography>
        </Box>
      </Box>
    </>
  )
}

export default InternalHebahanDashboardSidebar
