import {
  DecisionOptionsCode,
  globalStyles,
  useMutation,
  useQuery,
} from "@/helpers";
import React, { useEffect, useState } from "react";
import { FieldValues, useForm } from "react-hook-form";
import { useTranslation } from "react-i18next";

import {
  ButtonPrimary,
  DialogConfirmation,
  SelectFieldController,
  TextFieldController,
} from "@/components";
import { AddIcon } from "@/components/icons";
import { LoadingOverlay } from "@/components/loading";
import i18n from "@/i18n";
import {
  Box,
  Button,
  Grid,
  IconButton,
  Stack,
  Typography,
} from "@mui/material";
import { useNavigate } from "react-router-dom";
import KuiriIbuPejabatTab from "./KuiriIbuPejabatTab";
import PermohonanBaruIbuPejabatTab from "./PermohonanBaruIbuPejabat";

interface GeranBoxesProps {
  data: {
    name: string;
    number: number;
  };
  isActive: boolean;
  onClick: () => void;
}
interface ApplicationPayload {
  grantApplicationId: number[];
  applicationStatusCode: number;
  roApprovalType: string;
  noteHq: string;
}
const PenilaianIbuPejabat: React.FC = () => {
  const { t } = useTranslation();
  const classes = globalStyles();
  const navigate = useNavigate();
  const [dialogOpen, setDialogOpen] = useState(false);
  const [isSuccess, setIsSuccess] = useState(false);
  const isMyLanguage = i18n.language === "my";
  const [states, setStates] = useState<string[]>(["Belum Di Hantar"]);
  const decisionOptions = DecisionOptionsCode(t);
  const [selectedIdsByTab, setSelectedIdsByTab] = useState<
    Record<string, string[] | number[]>
  >({});
  const defaultTab = "permohonan-baru";
  const [activeTab, setActiveTab] = useState(defaultTab);
  const labelStyle = {
    fontSize: "16px",
    color: "#666666",
    fontWeight: "400 !important",
  };
  const { control, handleSubmit, getValues, watch, setValue } =
    useForm<FieldValues>({
      defaultValues: {
        approvalStatus: "",
        rejectReason: "",
        note: "",
        meetingNumber: "",
        meetingNum: 0,
        meetingYear: 0,
      },
    });
  const handleClickState = (state: string) => {
    const match = state.match(/Bil (\d+)\/(\d{4})/);
    if (match) {
      const [, num, year] = match;
      const meetingNum = parseInt(num, 10);
      const meetingYear = parseInt(year, 10);

      setValue("meetingNum", meetingNum);
      setValue("meetingYear", meetingYear);
      setValue("meetingNumber", state);

      setSelectedMeeting({
        num: meetingNum,
        year: meetingYear,
        label: state,
      });
    }
  };
  const setActiveTabContent = (slug: string) => {
    setActiveTab(slug);
    setSelectedIdsByTab((prev) => ({
      ...prev,
      [slug]: [],
    }));
  };
  const {
    data: applicationGrantPendingHqDataResponse,
    isLoading: isLoadingApplicationGrantListData,
    refetch: refetchSocietyGrantList,
  } = useQuery({
    url: `grant/application`,
    filters: [
      { field: "status", value: "PENDING_HQ", operator: "eq" },
      { field: "hasMeeting", value: true, operator: "eq" }
    ],
  });

  const handleSelectedIdsChange = (ids: string[] | number[]) => {
    setSelectedIdsByTab((prev) => ({
      ...prev,
      [activeTab]: ids,
    }));
  };
  const {
    fetch: updateApprovalApplicationGeran,
    isLoading: isUpdatingApprovalApplicationGeran,
  } = useMutation({
    url: "grant/application/hq-decisions",
    method: "put",
    onSuccess: (res) => {
      const responseCode = res?.data?.code;

      if (responseCode === 200) {
        setIsSuccess(true);
        setTimeout(() => {
          navigate("../carian-permohonan", { replace: true });
        }, 2000);
      }
    },
  });

  const handleConfirmedSubmit = async () => {
    const selectedIds = selectedIdsByTab[activeTab] || [];

    const dataPayload = {
      applicationStatusCode: getValues("applicationStatusCode"),
      roApprovalType: "GRANT_APPLICATION_HQ",
      noteHq: getValues("note"),
      grantApplicationId: selectedIds,
      meetingNumber: getValues("meetingNum"),
      meetingYear: getValues("meetingYear"),
    };
    await updateApprovalApplicationGeran(dataPayload);
  };

  const onSubmit = () => {
    setDialogOpen(true);
  };
  const {
    data: applicationGrantListQueryDataResponse,
    isLoading: isLoadingApplicationQueryGrantListData,
    refetch: refetchSocietyGrantQueryList,
  } = useQuery({
    url: `grant/application`,
    filters: [
      { field: "status", value: "PENDING_HQ", operator: "eq" },
      { field: "typeQuery", value: "HQ", operator: "eq" },
    ],
  });

  const {
    data: applicationGrantMeetingNumberDataResponse,
    isLoading: isLoadingApplicationGrantMeetingNumberListData,
    refetch: refetchSocietyGranMeetingNumbertList,
  } = useQuery({
    url: `grant/application/meetingnumber`,
    onSuccess: (res) => {},
  });

  const meetingNumber = applicationGrantMeetingNumberDataResponse?.data?.data;
  const totalGrantApplication =
    applicationGrantPendingHqDataResponse?.data?.data?.data?.length -
      applicationGrantListQueryDataResponse?.data?.data?.data?.length || 0;
  const totalGrantApplicationQuery =
    applicationGrantListQueryDataResponse?.data?.data?.data?.length || 0;
  const tab = [
    {
      name: "Permohonan Baru",
      slug: "permohonan-baru",
      number: totalGrantApplication || 0,
    },
    {
      name: "Kuiri kepada Negeri",
      slug: "kuiri-kepada-negeri",
      number: totalGrantApplicationQuery || 0,
    },
  ];

  const [selectedMeeting, setSelectedMeeting] = useState<{
    num: number;
    year: number;
    label: string;
  } | null>(null);
  const [selectedState, setSelectedState] = useState<string | null>(null);

  const renderTab = () => {
    switch (activeTab) {
      case "permohonan-baru":
        return (
          <PermohonanBaruIbuPejabatTab
            onSelectIdsChange={handleSelectedIdsChange}
            meetingFilter={selectedMeeting}
          />
        );
      case "kuiri-kepada-negeri":
        return (
          <KuiriIbuPejabatTab
            onSelectIdsChange={handleSelectedIdsChange}
            meetingFilter={selectedMeeting}
          />
        );
      default:
        return null;
    }
  };

  useEffect(() => {
    const currentYear = new Date().getFullYear();
    if (applicationGrantMeetingNumberDataResponse) {
      const newStates = ["Belum Di Hantar"];
      for (let i = 1; i <= meetingNumber; i++) {
        const code = `Bil ${i}/${currentYear}`;
        if (!newStates.includes(code)) {
          newStates.push(code);
        }
      }
      setStates(newStates);
    }
  }, [meetingNumber, applicationGrantMeetingNumberDataResponse]);

  const applicationStatusCode = watch("applicationStatusCode");
  const [isFormDisabled, setIsFormDisabled] = useState(false);

  const chunkArray = (arr: string[], chunkSize: number) =>
    Array.from({ length: Math.ceil(arr.length / chunkSize) }, (_, i) =>
      arr.slice(i * chunkSize, i * chunkSize + chunkSize)
    );
  const stateColumns = chunkArray(states, 5);

  return (
    <>
      <Box className={classes.section} mb={2}>
        <Box className={classes.sectionBox}>
          <Typography className="title" mb={4}>
            Saringan Ibu Pejabat
          </Typography>
          <Box
            sx={{
              display: "grid",
              gridTemplateColumns: "repeat(auto-fit, minmax(180px, 1fr))",
              gap: "16px",
            }}
          >
            <Grid container spacing={1}>
              {tab.map((data, index) => {
                return (
                  <Grid item sm={2} key={index}>
                    <GeranBoxes
                      key={index}
                      data={data}
                      isActive={data.slug === activeTab}
                      onClick={() => setActiveTabContent(data.slug)}
                    />
                  </Grid>
                );
              })}
            </Grid>
          </Box>
        </Box>
        <Box className={classes.sectionBox} mt={1}>
          <Typography className="title" mb={2}>
            {t("evaluationMeeting")}
          </Typography>

          <Box
            sx={{
              display: "flex",
              gap: "4px",
              justifyContent: "flex-end",
            }}
            mb={3}
          >
            <IconButton
              sx={{
                padding: "9px 10px",
                borderRadius: "5px",
                backgroundColor: "var(--primary-color)",
              }}
            >
              <AddIcon color="#fff" />
            </IconButton>

            <Button
              className={classes.btnOutline}
              sx={{ fontSize: "10px !important" }}
              onClick={() => navigate("./mesyuarat-penilaian")}
            >
              {t("addAssesmentMeeting")}
            </Button>
          </Box>
          <Box>
            <Grid container spacing={4}>
              {stateColumns.map((column, colIndex) => (
                <Grid item key={colIndex}>
                  <Stack spacing={2}>
                    {column.map((state, idx) => {
                      return (
                        <>
                          <Box
                            key={idx}
                            display="flex"
                            alignItems="left"
                            justifyContent="left"
                            sx={{ cursor: "pointer" }}
                            onClick={() => {
                              handleClickState(state);
                              setSelectedState(state);
                            }}
                          >
                            <Box
                              sx={{
                                display: "flex",
                                alignItems: "center",
                                bgcolor: "#00B6AD",
                                mr: "5px",

                                color: "white",
                                borderRadius: "10px",
                                justifyContent: "center",
                                fontWeight: "bold",
                                width: "37px",
                                height: "32px",
                                boxShadow: "4px 6px 12px 0px #6666664D",
                              }}
                            >
                              {" "}
                              <img
                                src="/mesyuaratPenilaian.png"
                                alt="status"
                                width="24px"
                                height="24px"
                                style={{ padding: "2px" }}
                              />
                            </Box>
                            <Typography
                              sx={{
                                border: "1px solid var(--primary-color)",
                                borderRadius: "10px",
                                padding: "8px 12px",
                                fontSize: "12px",
                                minWidth: "120px",
                                bgcolor:
                                  selectedState === state ? "#00B6AD" : "white",
                                fontWeight: "400 !important",
                                color:
                                  selectedState === state
                                    ? "white"
                                    : "var(--primary-color)",
                                "&:hover": {
                                  bgcolor:
                                    selectedState === state
                                      ? "primary.dark"
                                      : "grey.100",
                                },
                              }}
                            >
                              {state}
                            </Typography>
                          </Box>
                        </>
                      );
                    })}
                  </Stack>
                </Grid>
              ))}
            </Grid>
          </Box>
        </Box>
      </Box>
      {renderTab()}
      {isLoadingApplicationGrantMeetingNumberListData ? (
        <LoadingOverlay />
      ) : (
        <>
          <Box className={classes.section} mb={2}>
            <Box className={classes.sectionBox}>
              <Typography className="title" mb={4}>
                {t("penilaianIbuPejabat")}
              </Typography>
              <Grid container>
                <Grid item xs={12} sm={4}>
                  <Typography variant="body1" sx={labelStyle}>
                    {t("syorIbuPejabat")}
                  </Typography>
                </Grid>

                <Grid item xs={12} sm={8} sx={{ mb: 3 }}>
                  <SelectFieldController
                    control={control}
                    name="applicationStatusCode"
                    options={decisionOptions}
                    disabled={isFormDisabled}
                    sx={{
                      background: isFormDisabled
                        ? "rgba(218, 218, 218, 0.5)"
                        : "",
                    }}
                    required
                  />
                </Grid>
                <Grid item xs={12} sm={4}>
                  {" "}
                  <Typography variant="body1" sx={labelStyle}>
                    {t("numberOfMeetings")}
                  </Typography>
                </Grid>
                <Grid item xs={12} sm={8} sx={{ mb: 2 }}>
                  <TextFieldController
                    disabled
                    control={control}
                    name="meetingNumber"
                  />
                </Grid>
                <Grid item xs={12} sm={4}>
                  <Typography variant="body1" sx={labelStyle}>
                    Catatan Keputusan
                  </Typography>
                </Grid>
                <Grid item xs={12} sm={8}>
                  <TextFieldController
                    control={control}
                    name="note"
                    multiline
                    disabled={isFormDisabled}
                    required={watch("applicationStatusCode") === 36}
                    sxInput={{
                      minHeight: "92px",
                    }}
                  />
                </Grid>
              </Grid>
            </Box>
            <ButtonPrimary
              type="submit"
              onClick={handleSubmit(onSubmit)}
              sx={{
                marginLeft: "auto",
                display: "block",
                mt: 3,
                fontSize: "10px",
              }}
            >
              Kemaskini Keputusan
            </ButtonPrimary>
          </Box>
        </>
      )}

      <DialogConfirmation
        isMutating={isUpdatingApprovalApplicationGeran}
        open={dialogOpen}
        onClose={() => setDialogOpen(false)}
        onAction={handleConfirmedSubmit}
        onConfirmationText={
          applicationStatusCode === 3
            ? t("approveConfirmationGeran")
            : applicationStatusCode === 4
            ? t("rejectConfirmationGeran")
            : t("kuiriConfirmationGeran")
        }
        isSuccess={isSuccess}
        onSuccessText={
          applicationStatusCode === 3
            ? t("approveSuccessGeran")
            : applicationStatusCode === 4
            ? t("rejectSuccessGeran")
            : t("kuiriSuccessGeran")
        }
      />
    </>
  );
};

const GeranBoxes: React.FC<GeranBoxesProps> = React.memo(
  ({ data, isActive, onClick }) => {
    return (
      <Box
        onClick={onClick}
        sx={{
          position: "relative",
          padding: "12px 20px 20px 5px",
          border: isActive ? "1px solid #00B69B" : "1px solid #00B69B",
          borderRadius: "8px",
          backgroundColor: isActive ? "var(--primary-color)" : "transparent",
          color: isActive ? "#fff" : "var(--primary-color)",

          height: "80px",
          cursor: "pointer",
          transition: "all 0.3s",
          "&:hover": {
            backgroundColor: isActive ? "var(--primary-color)" : "#fff",
          },
        }}
      >
        <Box
          sx={{
            color: isActive ? "#fff" : "var(--primary-color)",
            fontWeight: 400,
            fontSize: "14px",
          }}
        >
          {data.name}
        </Box>
        <Typography
          sx={{
            position: "absolute",
            bottom: 2,
            right: "10px",
            color: isActive ? "#fff" : "var(--primary-color)",
            fontWeight: 500,
          }}
        >
          {data.number}
        </Typography>
      </Box>
    );
  }
);

export default PenilaianIbuPejabat;
