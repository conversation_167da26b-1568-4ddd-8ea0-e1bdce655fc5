import React, {useEffect, useState} from "react";
import {
  Box,
  Typography,
  Card,
  CardMedia,
  List,
  ListItem,
  ListItemText,
  IconButton,
  Divider,
  useMediaQuery,
  Slide, DialogTitle, DialogContent, Dialog, Grid, Checkbox, FormHelperText
} from "@mui/material";
import {ChevronLeft, WhatsApp, Facebook, LinkedIn, Close, Link as LinkIcon} from "@mui/icons-material";
import {useParams, useNavigate, useLocation} from "react-router-dom";
import {ArtikelObject} from "@/pages/artikel/data";
import ShareMenu from "@/components/share-menu";
import {useTranslation} from "react-i18next";
import {useCustom} from "@refinedev/core";
import {API_URL} from "@/api";
import {SlideData} from "@/pages/hebahan/previewArticle";
import ReactGA from "react-ga4";
import {useCustomMutation} from "@refinedev/core";
import {ReportIcon} from "@/components/icons";
import Modal from "@/components/dialog/modal/Moda";
import {headerStyle, labelStyle} from "@/pages/internal-training/trainingConstant";
import {ButtonPrimary, DialogConfirmation} from "@/components";
import {FlagIcon} from "@/components/icons/flag";


const Artikel: React.FC = () => {
  const {t} = useTranslation();
  const {slug} = useParams();
  const navigate = useNavigate();
  const isMobile = useMediaQuery("(max-width: 768px)");
  const [article, setArticle] = useState<ArtikelObject>();
  const [randomArticle, setRandomArticle] = useState<ArtikelObject[]>([]);
  //const [slideComponents, setSlideComponents] = useState<SlideData[]>([]);
  const [currentIndex, setCurrentIndex] = useState<number>(0);
  const [isContentVisible, setIsContentVisible] = useState(false);
  const [openModal, setOpenModal] = useState(false);
  const [openReportModal, setOpenReportModal] = useState(false);
  const [formData, setFormData] = useState("")
  const location = useLocation();

  useEffect(() => {
    console.log(location);
    viewAnalytics();
  }, [location]);


  const viewAnalytics = () => {
    ReactGA.send({hitType: "pageview", page: location.pathname});
  }

  const shareAnalytics = () => {
    ReactGA.event({
      category: "Share Link",  // Event Category
      action: "Share Link",      // Event Action
      label: "Share Link",        // Optional label
    });
  }

  const {data: articleData, isLoading: isArticleLoading, refetch: refetchArticle} = useCustom({
    url: `${API_URL}/society/posting/uuid/${slug}`,
    method: "get",
    config: {
      headers: {
        portal: localStorage.getItem("portal"),
        //authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
      },
    },
    queryOptions: {
      enabled: slug != null && slug != "",
      retry: false,
      cacheTime: 0,
    },
  });

  const {data: randomArticleData, isLoading: isRandomArticleLoading, refetch: refetchRandomArticle} = useCustom({
    url: `${API_URL}/society/posting/random`,
    method: "get",
    config: {
      headers: {
        portal: localStorage.getItem("portal"),
        //authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
      },
    },
    queryOptions: {
      enabled: true,
      retry: false,
      cacheTime: 0,
    },
  });

  const {data: analyticsData, isLoading, refetch: refetchAnalytics} = useCustom({
    url: `${API_URL}/society/posting/analytics`,
    method: "get",
    config: {
      headers: {
        portal: localStorage.getItem("portal"),
        //authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
      },
    },
    queryOptions: {
      enabled: true,
      retry: false,
      cacheTime: 0,
    },
  });

  const {mutate: reportPosting, isLoading: isLoadingReport} = useCustomMutation();
  const Report = (): void => {
    reportPosting(
      {
        url: `${API_URL}/society/posting/report`,
        method: "put",
        values: {
          postingId: slug,
          reportReason: formData,
        },
        config: {
          headers: {
            "Content-Type": "application/json",
            portal: localStorage.getItem("portal") || "",
            authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
          },
        },
        successNotification: (data) => {
          if (data?.data?.data) {
            //console.log(data?.data?.data);
            setOpenModal(false);
            return {
              message: data?.data?.msg.includes("hidden") ? t("TRAINING_UNPUBLISHED") : t("TRAINING_PUBLISHED"),
              type: "success",
            };
          } else {
            return {
              message: data?.data?.msg.includes("incomplete") ? t("TRAINING_INCOMPLETE") : t("error") + data?.data?.msg,
              type: "error",
            };
          }
        },
        errorNotification: (data) => {
          return {
            message: data?.response?.data?.msg,
            type: "error",
          };
        },
      },
      {
        onError(error, variables, context) {
          console.log(error);
        },
      }
    );
  };


  useEffect(() => {
    if (articleData?.data?.data) {
      const p = articleData.data.data;
      const img = p.mediaUrl && p.mediaUrl.length > 0 ? p.mediaUrl[0] : '/latihanSample/images5.jpg';
      const mainArticle: ArtikelObject = {
        slide: p.mediaUrl.map((m: string) => {
          return m
        }),
        img: img,
        slug: p.id,
        heading1: p.title,
        description: p.description,
        date: p.postingDate,
        name: p.author,
        content: p.description,
      }
      setArticle(mainArticle);
    }
  }, [articleData]);

  useEffect(() => {
    if (randomArticleData?.data?.data) {
      const temp: ArtikelObject[] = randomArticleData.data.data.map((p: any) => {
        const img = p.mediaUrl && p.mediaUrl.length > 0 ? p.mediaUrl[0] : '/latihanSample/images5.jpg';
        return {
          slide: [],
          img: img,
          slug: p.id,
          heading1: p.title,
          description: p.description,
          date: p.postingDate,
          name: p.author,
          content: p.description,
        }
      });
      setRandomArticle(temp);
    }
  }, [randomArticleData])

  const handleViewArtikel = (slug: string) => {
    navigate(`/artikel/${slug}`);
  };

  useEffect(() => {
    if (article && article.slide.length > 1) {
      const timer = setTimeout(() => {
        const nextIndex = (currentIndex + 1) % article.slide.length;
        setCurrentIndex(nextIndex);
      }, 5000);
      return () => clearTimeout(timer);
    }
  }, [currentIndex]);

  const feedBackConstant = [
    {
      title: "Kandungan tidak sesuai",
      description: "Kandungan yang tidak wajar atau meragukan dari segi moral,\n" +
        "dan kadangkala digunakan dalam konteks undang-undang atau tatatertib"
    },
    {
      title: "Maklumat Palsu",
      description: "Sebarang informasi yang tidak benar, mengelirukan, atau direka-reka, sama \n" +
        "ada disebarkan dengan niat untuk menipu atau tanpa menyedari bahawa \n" +
        "maklumat tersebut tidak sahih."
    },
    {
      title: "Hantaran Berulang",
      description: "Hantaran Berulang (Duplicate Posting) merujuk kepada perbuatan menghantar \n" +
        "atau menyiarkan kandungan yang sama lebih daripada sekali dalam platform \n" +
        "yang sama atau berbeza, sama ada secara sengaja atau tidak sengaja."
    },
    {
      title: "Isu Plagiarisme",
      description: "Peniruan atau penciplakan hasil kerja orang lain tanpa memberikan pengiktirafan \n" +
        "sewajarnya. Ini termasuk menyalin idea, tulisan, reka bentuk, atau karya intelektual \n" +
        "orang lain dan mengakuinya sebagai hasil sendiri."
    },
  ]

  return (
    <>
      <Box sx={{display: "flex", flexDirection: "column", p: 3, py: "86px", backgroundColor: "#F1F4FA"}}>
        <IconButton onClick={() => navigate(-1)} sx={{alignSelf: "flex-start", mt: "50px", mb: "50px"}}>
          <ChevronLeft/>
        </IconButton>

        {article &&
          <Box sx={{display: "flex", flexDirection: isMobile ? "column" : "row", justifyContent: "center"}}>
            <Box sx={{width: 800, maxWidth: 800, p: "70px", bgcolor: "#fff", borderRadius: 2, boxShadow: 3}}>
              <Typography variant="h4" gutterBottom
                          sx={{color: "#666666", fontSize: "25px", fontWeight: "500", lineHeight: "30px", mb: "29px"}}>
                {article.heading1}
              </Typography>
              <Card sx={{mb: "52px"}}>
                {article.slide.length > 0 ?
                  <Box
                    sx={{
                      height: "300px",
                      backgroundImage: `url(${article.slide[currentIndex]})`,
                      backgroundSize: "cover",
                      backgroundRepeat: "no-repeat",
                      backgroundPosition: "center center",
                      justifyContent: "center",
                      overflow: "hidden",
                      "@media screen and (-webkit-min-device-pixel-ratio: 1.5) and (-webkit-max-device-pixel-ratio: 1.75), screen and (min-resolution: 144dpi) and (max-resolution: 168dpi)":
                        {
                          maxHeight: "600px",
                        },
                    }}
                  >
                    {article.slide.length > 1 && article.slide.map((slide, index) => (
                      <Slide
                        style={{overflow: "hidden"}}
                        key={index}
                        direction={"left"}
                        in={currentIndex === index}
                        mountOnEnter
                        unmountOnExit
                        timeout={500}
                        onEntered={() => setIsContentVisible(true)} // Trigger fade-in after slide transition
                        onExit={() => setIsContentVisible(false)}
                      >
                        <Box
                          sx={{
                            height: "100%",
                            zIndex: 0,
                            backgroundImage: `url(${slide})`,
                            backgroundSize: "cover",
                            backgroundRepeat: "no-repeat",
                            backgroundPosition: "center center",
                            justifyContent: "center",
                            display: "flex",
                          }}
                        >
                        </Box>
                      </Slide>
                    ))}
                  </Box> : <></>}
              </Card>
              {article.content.split("\n").map((line, index) => (
                <Typography mb={"16px"} color="text.secondary" textAlign="justify">
                  {line}
                </Typography>
              ))}
            </Box>

            {/* Related Articles Sidebar */}
            <Box sx={{
              maxWidth: isMobile ? "100%" : "400px",
              mt: isMobile ? "24px" : 0,
              ml: isMobile ? 0 : "24px"
            }}>
              {/* More Details and Share */}
              <Box sx={{p: 3, bgcolor: "#fff", borderRadius: 2, boxShadow: 3, mb: "24px"}}>
                <Typography variant="body2" color="text.secondary" gutterBottom>
                  {article.name}
                </Typography>
                <Typography variant="caption" color="text.secondary">{article.date}</Typography>

                <Box sx={{display: "flex", justifyContent: "flex-end", mt: 2}}>
                  <ShareMenu url={`${window.location.origin}/artikel/${slug}`} title={article ? article.heading1 : ""}/>
                  <IconButton onClick={() => {
                    refetchAnalytics();
                    setOpenReportModal(true);
                  }}>
                    <FlagIcon />
                  </IconButton>
                </Box>
              </Box>

              {/* Related Articles */}
              <Box sx={{p: 3, bgcolor: "#fff", borderRadius: 2, boxShadow: 3}}>
                <Typography
                  sx={{mb: "45px", fontSize: "20px", color: "#666666", textAlign: "center", fontWeight: "400"}}>
                  {t("bacaanBerkaitan")}
                </Typography>
                <List>
                  {randomArticle
                    .filter((article: ArtikelObject) => article.slug !== slug) // Exclude current article
                    .map((article, index) => (
                      <React.Fragment key={index}>
                        <ListItem sx={{alignItems: "flex-start", p: 0, mb: "16px", cursor: "pointer"}}
                                  onClick={() => handleViewArtikel(article.slug)}>
                          <CardMedia
                            component="img"
                            sx={{
                              minWidth: isMobile ? "200px" : "134px",
                              maxWidth: isMobile ? "200px" : "134px",
                              height: isMobile ? "120px" : "93px",
                              borderRadius: 1,
                              mr: "10px",
                              transition: "transform 0.3s ease-in-out",
                              "&:hover": {
                                transform: "scale(1.1)"
                              }
                            }}
                            image={article.img}
                            alt={article.heading1}
                          />
                          <ListItemText sx={{m: 0}}
                                        primary={
                                          <Typography
                                            sx={{
                                              display: "-webkit-box",
                                              WebkitBoxOrient: "vertical",
                                              WebkitLineClamp: 2,
                                              overflow: "hidden",
                                              textOverflow: "ellipsis",
                                              fontSize: "14px",
                                              lineHeight: "16px",
                                              color: "#666666",
                                              fontWeight: "500",
                                              mb: "6px"
                                            }}
                                          >
                                            {article.heading1}
                                          </Typography>
                                        }
                                        secondary={
                                          <Typography
                                            sx={{
                                              display: "-webkit-box",
                                              WebkitBoxOrient: "vertical",
                                              WebkitLineClamp: 4,
                                              overflow: "hidden",
                                              textOverflow: "ellipsis",
                                              fontSize: "10px",
                                              lineHeight: "12px",
                                              color: "#666666"
                                            }}
                                          >
                                            {article.description}
                                          </Typography>
                                        }
                          />
                        </ListItem>
                      </React.Fragment>
                    ))}
                </List>
              </Box>
            </Box>
          </Box>
        }
      </Box>
      <Dialog
        open={openReportModal}
        onClose={() => setOpenReportModal(false)}
        //maxWidth={maxWidth}
        //fullScreen={fullScreen}
        PaperProps={{
          style: {
            borderRadius: "8px",
            backgroundColor: "#fff",
            color: "#000",
            minWidth: "400px",
            width: "600px"
            //maxWidth: "100%",
          },
        }}
        slotProps={{
          backdrop: {
            style: {
              backgroundColor: "rgba(0, 0, 0, 0.5)",
              backdropFilter: "blur(4px)",
            },
          },
        }}
      >
        <DialogContent sx={{py: 4}}>
          <Box
            sx={{
              borderRadius: 2.5,
              backgroundColor: "#fff",
              border: "1px solid #D9D9D9",
              //flex: 5,
              //display: "inline",
              px: 2,
              py: 2,
              mb: 1,
            }}
          >
            <Typography sx={headerStyle}>
              {t("reportArticle")}
            </Typography>
            <Box sx={{mt: 2}}>
              <Grid container>
                {feedBackConstant.map((item, index) => {
                  return (
                    <>
                      <Grid item xs={12} sm={4}>
                        <Checkbox
                          checked={formData === item.title}
                          onChange={(e) => {
                            if(e.target.checked) setFormData(item.title);
                            else setFormData("");
                          }}
                          sx={{p: 0, ml: 0, mr: 0, mt: 2}}
                        />
                      </Grid>
                      <Grid item xs={12} sm={8}>
                        <Typography sx={labelStyle}>
                          {item.title}
                        </Typography>
                        <Typography sx={{fontWeight:400,fontSize:14,fontStyle:"regular", color:"#666666AD"}}>
                          {item.description}
                        </Typography>
                      </Grid>
                    </>
                  )
                })}
                {(formData!="") ? (
                  <FormHelperText sx={{ color: "red" }}>
                    {t("fieldRequired")}
                  </FormHelperText>
                ) : null}
              </Grid>
            </Box>
          </Box>
          <Box
            sx={{
              mt: 2,
              display: "flex",
              flexDirection: "row",
              justifyContent: "flex-end",
              gap: 1,
            }}>
            <ButtonPrimary
              variant="contained"
              sx={{
                width: "auto",
              }}
              onClick={() => {
                if(formData!="") {
                  setOpenModal(true)
                }
              }}
              //disabled={articleId === "0" || isLoadingCreate || isLoadingEdit}
            >
              {t("send")}
            </ButtonPrimary>
          </Box>
        </DialogContent>
      </Dialog>
      <DialogConfirmation
        open={openModal}
        onClose={() => {
          setOpenModal(false);
        }}
        onAction={Report}
        isMutating={false}
        onConfirmationText={t("reportArticle")}
      />
    </>
  );
};

export default Artikel;
